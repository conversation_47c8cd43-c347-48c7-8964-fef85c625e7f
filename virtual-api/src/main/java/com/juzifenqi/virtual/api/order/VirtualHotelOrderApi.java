package com.juzifenqi.virtual.api.order;

import com.juzifenqi.virtual.bean.pojo.VirtualSupplierHotelOrder;
import com.juzifenqi.virtual.bean.system.VirtualResult;
import com.juzifenqi.virtual.bean.vo.HotelOrderDetailVo;
import java.util.List;

/**
 * 酒店api
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/30 16:31
 */
public interface VirtualHotelOrderApi {

    /**
     * 获取有效的酒店订单
     *
     * @param orderSn 虚拟权益订单号
     */
    VirtualResult<List<VirtualSupplierHotelOrder>> getVirtualHotelOrder(String orderSn);

    /**
     * 获取三方酒店订单列表（去预定酒店按钮点击获取）
     *
     * @param orderSn 虚拟权益订单号
     */
    VirtualResult<String> getHotelListUrl(String orderSn);

    /**
     * 获取查看酒店详情需要的橡树token和橡树酒店订单号
     *
     * @param orderSn 虚拟权益订单号
     */
    VirtualResult<HotelOrderDetailVo> getHotelOrderDetail(String orderSn);
}
