package com.juzifenqi.virtual.api.admin;

import com.juzifenqi.virtual.bean.bo.VirtualGoodsBo;
import com.juzifenqi.virtual.bean.pojo.VirtualGoods;
import com.juzifenqi.virtual.bean.pojo.VirtualRechargeType;
import com.juzifenqi.virtual.bean.system.VirtualResult;
import com.juzifenqi.virtual.bean.vo.UpdateOnSaleStateVo;
import com.juzifenqi.virtual.bean.vo.VirtualGoodsVo;
import java.util.List;

/**
 * 权益管理
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/20 9:30 下午
 */
public interface VirtualGoodsApi {

    /**
     * 多条件查询权益列表(分页)
     */
    VirtualResult<List<VirtualGoodsVo>>  queryProfitList(VirtualGoodsBo virtualGoodsBo);

    /**
     * 权益新增
     */
    VirtualResult addProfit(VirtualGoods virtualGoods);

    /**
     * 权益修改
     */
    VirtualResult modifyProfit(VirtualGoods virtualGoods);

    /**
     * 权益删除
     */
    VirtualResult deleteProfit(Integer id);


    /**
     * 权益上架/下架 0=未上架 1=上架
     */
    VirtualResult updateOnsaleState(Integer id, Integer onsaleState, String updateById, String updateBy);

    /**
     * 新权益上架/下架 0=未上架 1=上架
     */
    VirtualResult<UpdateOnSaleStateVo> newUpdateOnSaleState(Integer id, Integer onSaleState,
            String updateById, String updateBy);

    /**
     * 合作方下拉列表
     */
    VirtualResult getAllVirtualProductSupplier();

    /**
     * 根据sku批量获取虚拟商品,sku逗号隔开
     */
    VirtualResult<List<VirtualGoods>> getVirtualGoodsBySkus(String skus);

    /**
     * 充值账号类型下拉列表
     */
    VirtualResult<List<VirtualRechargeType>> getAllVirtualRechargeType();

}
