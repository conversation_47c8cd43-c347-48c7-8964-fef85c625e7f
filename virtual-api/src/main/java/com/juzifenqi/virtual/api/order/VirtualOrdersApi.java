package com.juzifenqi.virtual.api.order;

import com.juzifenqi.virtual.bean.bo.VirtualOrdersBo;
import com.juzifenqi.virtual.bean.pojo.VirtualOrders;
import com.juzifenqi.virtual.bean.pojo.VirtualOrdersCard;
import com.juzifenqi.virtual.bean.pojo.VirtualOrdersTrail;
import com.juzifenqi.virtual.bean.system.VirtualResult;
import com.juzifenqi.virtual.bean.vo.VirtualOrderPayInfoVo;
import java.util.List;
import java.util.Map;

/**
 * 权益结算订单
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/21 8:35 AM
 */
public interface VirtualOrdersApi {

    /**
     * 取消会员时判断用户是否已使用权益
     */
    VirtualResult<Integer> countUesdOrdersByOrderSn(String plusOrderSn);

    /**
     * 取消会员时判断用户是否已使用权益
     */
    VirtualResult<Integer> countUesdOrdersByOrderSn(String plusOrderSn, Integer modelId);

    /**
     * 根据订单号查询结算订单
     */
    VirtualResult<VirtualOrders> getVirtualOrdersByOrderSn(String orderSn);

    /**
     * 获取期限内，通过该会员单号下的订单
     *
     * @param plusOrderSn 开通会员单号
     */
    VirtualResult<List<VirtualOrders>> countOrderByPlus(String plusOrderSn, Integer modelId);

    /**
     * 获取期限内，通过该会员单号下的订单
     *
     * @param plusOrderSn 开通会员单号
     */
    VirtualResult<List<VirtualOrders>> countOrderByPlus(String plusOrderSn);


    /**
     * 是否有充值中订单（待充值下单，下单成功，待充值查证，充值成功，存疑订单）
     *
     * @param plusOrderSn 开通会员单号
     */
    VirtualResult<Integer> countProcessingOrder(String plusOrderSn, Integer modelId);

    /**
     * 是否有充值中订单（待充值下单，下单成功，待充值查证，充值成功，存疑订单）
     */
    VirtualResult<Integer> countProcessingOrder(String plusOrderSn);

    /**
     * 充值中订单列表（待充值下单，下单成功，待充值查证，充值成功，存疑订单）
     *
     * @param plusOrderSn 开通会员单号
     */
    VirtualResult<List<VirtualOrders>> selectProcessingOrder(String plusOrderSn, Integer modelId);


    /**
     * 充值中订单列表（待充值下单，下单成功，待充值查证，充值成功，存疑订单）
     *
     * @param plusOrderSn 开通会员单号
     */
    VirtualResult<List<VirtualOrders>> selectProcessingOrder(String plusOrderSn);

    /**
     * 获取商城下单未支付订单
     *
     * @param plusOrderSn 开通会员单号
     * @param productSku 商品sku
     */
    VirtualResult<List<VirtualOrders>> getNeedPayOrder(String plusOrderSn, String productSku,
            Integer modelId);


    /**
     * 获取商城下单未支付订单
     *
     * @param plusOrderSn 开通会员单号
     * @param productSku 商品sku
     */
    VirtualResult<List<VirtualOrders>> getNeedPayOrder(String plusOrderSn, String productSku);

    /**
     * 会员权益下单，需要等支付成功回调
     */
    VirtualResult<Boolean> ordersSubmit(VirtualOrders virtualOrders);


    /**
     * 多条件查询结算订单列表(分页)-浩瀚后台使用
     */
    VirtualResult<List<VirtualOrders>> querySettlementOrderList(VirtualOrdersBo virtualOrdersBo);

    /**
     * 充值状态下拉列表
     */
    VirtualResult<List<Map<String, Object>>> getOrderStatusDic();

    /**
     * 查询已完成结算订单列表信息-会员-后台展示使用权益信息
     */
    VirtualResult<List<VirtualOrders>> getSettlementOrderList(VirtualOrdersBo virtualOrdersBo);

    /**
     * 会员虚拟下单-无需支付，直接子轩下单
     */
    VirtualResult submitOrder(VirtualOrders orders);

    /**
     * 获取用户虚拟订单列表。目前是对外输出C端用户使用
     */
    VirtualResult<List<VirtualOrders>> getListByUser(VirtualOrdersBo bo);

    /**
     * 获取卡密信息
     */
    VirtualResult<VirtualOrdersCard> getUserCard(VirtualOrdersBo bo);

    /**
     * 获取订单轨迹
     */
    VirtualResult<List<VirtualOrdersTrail>> getTrailList(String orderSn);

    /**
     * 通过开通会员单号 取消充值成功的权益订单
     *
     * @param plusOrderSn 开通会员单号
     */
    VirtualResult cancelOrdersByPlus(String plusOrderSn);

    /**
     * 查询全部订单列表信息-会员-后台展示使用权益信息
     */
    VirtualResult<List<VirtualOrders>> getAllOrderList(VirtualOrdersBo virtualOrdersBo);

    /**
     * 获取收银台支付请求信息
     * <p>订单中心调用</p>
     */
    VirtualResult<VirtualOrderPayInfoVo> getOrderPayInfo(String orderSn);

}
