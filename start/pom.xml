<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.juzifenqi</groupId>
        <artifactId>virtual-center</artifactId>
        <version>ykd-1.0.0-SNAPSHOT</version>
    </parent>
    <artifactId>start</artifactId>
    <packaging>jar</packaging>
    <version>ykd-1.0.0-SNAPSHOT</version>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>com.juzifenqi</groupId>
            <artifactId>virtual-core</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
            <version>2.1.4</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.junit.vintage</groupId>
                    <artifactId>junit-vintage-engine</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.skywalking</groupId>
            <artifactId>apm-toolkit-logback-1.x</artifactId>
            <version>6.0.0-GA</version>
        </dependency>
        <!-- arthas -->
        <dependency>
            <groupId>com.taobao.arthas</groupId>
            <artifactId>arthas-spring-boot-starter</artifactId>
            <version>3.5.0</version>
        </dependency>
    </dependencies>

    <profiles>
        <profile>
            <id>local</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <profileActive>local</profileActive>
            </properties>
        </profile>
        <profile>
            <id>dev1</id>
            <properties>
                <profileActive>dev1</profileActive>
            </properties>
        </profile>
        <profile>
            <id>dev2</id>
            <properties>
                <profileActive>dev2</profileActive>
            </properties>
        </profile>
        <profile>
            <id>test</id>
            <properties>
                <profileActive>test</profileActive>
            </properties>
        </profile>
        <profile>
            <id>test2</id>
            <properties>
                <profileActive>test2</profileActive>
            </properties>
        </profile>
        <profile>
            <id>pre</id>
            <properties>
                <profileActive>pre</profileActive>
            </properties>
        </profile>
        <profile>
            <id>lt</id>
            <properties>
                <profileActive>lt</profileActive>
            </properties>
        </profile>
        <profile>
            <id>prd</id>
            <properties>
                <profileActive>prd</profileActive>
            </properties>
        </profile>
    </profiles>

    <build>
        <finalName>virtual-center</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <version>2.8.2</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
