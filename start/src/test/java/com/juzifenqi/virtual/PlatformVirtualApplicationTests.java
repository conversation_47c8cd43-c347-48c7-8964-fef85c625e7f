package com.juzifenqi.virtual;

import com.alibaba.fastjson.JSON;
import com.juzifenqi.virtual.api.admin.VirtualGoodsApi;
import com.juzifenqi.virtual.api.order.VirtualHotelOrderApi;
import com.juzifenqi.virtual.bean.pojo.VirtualGoods;
import com.juzifenqi.virtual.bean.pojo.VirtualSupplierHotelOrder;
import com.juzifenqi.virtual.bean.system.VirtualResult;
import com.juzifenqi.virtual.dao.VirtualGoodsDao;
import java.math.BigDecimal;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest
@Slf4j
public class PlatformVirtualApplicationTests {

    @Autowired
    private VirtualHotelOrderApi api;
    @Autowired
    private VirtualGoodsApi goodsApi;
    @Autowired
    private VirtualGoodsDao goodsDao;

    @Test
    public void test() {
        /*VirtualResult<VirtualSupplierHotelOrder> virtualHotelOrder = api.getVirtualHotelOrder(
                "12121");
        System.out.println(JSON.toJSONString(virtualHotelOrder));*/

        VirtualResult<String> hotelListUrl = api.getHotelListUrl("12121");
        System.out.println(JSON.toJSONString(hotelListUrl));

    }

    @Test
    public void test2(){
        VirtualGoods goods = new VirtualGoods();
        goods.setRechargeTypeName("酒店直充");
        goods.setRechargeType(3);
        goods.setCreateBy("1111");
        goods.setSupplierName("黑卡");
        goods.setSupplierItemPrice(BigDecimal.ONE);
        goods.setSupplierId(3);
        goods.setSupplierItemId("12121");
        goods.setRechargeName("手机号");
        goods.setProductName("哈哈哈");
        goods.setProductId(110986);
        goods.setRechargeCode(1);
        goods.setPrivatePrice(BigDecimal.ONE);
        goods.setProductSku("N64KGX2SBD");
        goods.setStock(10L);
        goods.setSupplierAccessType(1);
        goods.setSupplierAccessUrl("baidu.com");
        goodsApi.addProfit(goods);
    }


    @Test
    public void test3(){
        VirtualGoods virtualGoodsById = goodsDao.getVirtualGoodsById(40);
        virtualGoodsById.setSupplierAccessType(2);
        virtualGoodsById.setSupplierAccessUrl("hfhfhfhfh");
        virtualGoodsById.setUpdateBy("de");
        goodsApi.modifyProfit(virtualGoodsById);
    }
}
