package com.juzifenqi.virtual;

import com.alibaba.fastjson.JSONObject;
import com.juzifenqi.virtual.api.order.VirtualOrdersApi;
import com.juzifenqi.virtual.bean.system.VirtualResult;
import com.juzifenqi.virtual.bean.vo.VirtualOrderPayInfoVo;
import com.juzifenqi.virtual.component.enums.FlowPayStateEnum;
import com.juzifenqi.virtual.core.models.VirtualOrderModel;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * @description
 * <AUTHOR>
 * @Date 2024/9/23
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest
@Slf4j
public class VirtualOrderTest {

    @Autowired
    private VirtualOrderModel virtualOrderModel;
    @Autowired
    private VirtualOrdersApi virtualOrdersApi;

    @Test
    public void getOrderPayInfo() {
        VirtualResult<VirtualOrderPayInfoVo> vo = virtualOrdersApi.getOrderPayInfo("992409201732245028");
        System.out.println(JSONObject.toJSONString(vo));
    }

    @Test
    public void updateOrderPayFlow() {
        virtualOrderModel.updateOrderPayFlow( null, null, FlowPayStateEnum.PAY_SUCCESS);
    }

    @Test
    public void updateOrderPayFlowForPayTimeout() {
        virtualOrderModel.updateOrderPayFlowForPayTimeout("1234567812");
    }

}
