package com.juzifenqi.virtual;

import com.alibaba.cloudapi.sdk.model.ApiResponse;
import com.alibaba.fastjson.JSON;
import com.juzifenqi.virtual.api.order.VirtualOrdersApi;
import com.juzifenqi.virtual.bean.system.Response;
import com.juzifenqi.virtual.component.util.DES3;
import com.juzifenqi.virtual.core.job.OrderCardsJob;
import com.juzifenqi.virtual.core.job.GetRechargeStatusJob;
import com.juzifenqi.virtual.core.job.OrderPushRechargeJob;
import com.juzifenqi.virtual.core.services.impl.third.xiangshu.XiangshuConfig;
import com.juzifenqi.virtual.core.services.impl.third.xiangshu.XiangshuUtil;
import com.juzifenqi.virtual.core.services.impl.third.xiangshu.model.BasicCouponReq;
import com.juzifenqi.virtual.core.services.impl.third.xiangshu.model.DistributeCouponDataReq;
import com.juzifenqi.virtual.core.services.impl.third.xiangshu.model.DistributeCouponReq;
import com.juzifenqi.virtual.core.services.impl.third.xiangshu.model.InvalidCouponReq;
import java.util.Arrays;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest
@Slf4j
public class PlatformVirtualTests {

    @Resource
    private OrderPushRechargeJob rechargeJob;

    @Resource
    private GetRechargeStatusJob queryJob;

    @Resource
    private OrderCardsJob decodeJob;

    @Autowired
    private XiangshuConfig xiangshuConfig;

    private XiangshuUtil xsUtil;

    @Autowired
    private VirtualOrdersApi virtualOrdersApi;

    @Test
    public void recharge() {
//        log.info("======调用[{}]下单失败处理：orderSn[{}] msg[{}]", "福禄", "12345", "我错了");
        rechargeJob.execute("1");
    }

    @Test
    public void query() {
        queryJob.execute("1");
    }

    @Test
    public void decode() {
        decodeJob.getFuluErrorOrderCardsJob("1");
    }

    @Test
    public void des3() {
        log.info("des3=======" + DES3.decrypt("O0fQwV+UPUn6vWQldq1nVnD39u2HDFPsVcOaHoVf0wr/GY09fe/eOXF6fNFyaTc78Db8jDmtaMd+RWVDBL4IEw=="));
    }

    private String token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpZCI6NjUxNTU3LCJleHAiOjE3MDQzNjI1NjgsInBsYXRmb3JtIjoiZXh0cmEiLCJuYW1lc3BhY2VfaWQiOjIyMSwic291cmNlIjoiYXBpIn0.DZgWCa6wKynSMrRIL7t2-xDgIuOZolJQWn_-3_E8adk";

    private String txid = "333333";

    @Test
    public void xiangshuLogin() {
        //创建橡树调用器
        xsUtil = new XiangshuUtil(xiangshuConfig.getXsHost(), xiangshuConfig.getXsHostKey(),
                xiangshuConfig.getXsHostSecret());
        Response<String> apiResponse = xsUtil.login("13683307201","13683307201");
        if (apiResponse.isOk()) {
            System.out.println(JSON.toJSONString(apiResponse.getData()));
        } else {
            System.out.println(JSON.toJSONString(apiResponse.getCode() + apiResponse.getMsg()));
        }
    }

    @Test
    public void xiangshuCoupon() {
        //创建橡树调用器
        xsUtil = new XiangshuUtil(xiangshuConfig.getXsHost(), xiangshuConfig.getXsHostKey(),
                xiangshuConfig.getXsHostSecret());
        DistributeCouponReq req = new DistributeCouponReq();
        DistributeCouponDataReq dataReq = new DistributeCouponDataReq();
        dataReq.setCount(1);
        dataReq.setExpiration("2023-12-30 23:59:59");
        dataReq.setBatchId("20231121151412231756");
        req.setToken(token);
        req.setTxId(txid);
        req.setCouponDataReqList(Arrays.asList(dataReq));
        Response<String> apiResponse = xsUtil.distributeCoupon(req);
        if (apiResponse.isOk()) {
            System.out.println(JSON.toJSONString(apiResponse.getData()));
        } else {
            System.out.println(JSON.toJSONString(apiResponse.getCode() + apiResponse.getMsg()));
        }
    }

    @Test
    public void xiangshuQueryRecharge() {
        //创建橡树调用器
        xsUtil = new XiangshuUtil(xiangshuConfig.getXsHost(), xiangshuConfig.getXsHostKey(),
                xiangshuConfig.getXsHostSecret());
        BasicCouponReq req = new DistributeCouponReq();
        req.setToken(token);
        req.setTxId("1");
        Response<String> apiResponse = xsUtil.queryDistributeCouponResult(req);
        if (apiResponse.isOk()) {
            System.out.println(JSON.toJSONString(apiResponse.getData()));
        } else {
            System.out.println(JSON.toJSONString(apiResponse.getCode() + apiResponse.getMsg()));
        }
    }

    @Test
    public void xiangshuQuery() {
        //创建橡树调用器
        xsUtil = new XiangshuUtil(xiangshuConfig.getXsHost(), xiangshuConfig.getXsHostKey(),
                xiangshuConfig.getXsHostSecret());
        BasicCouponReq req = new DistributeCouponReq();
        req.setToken(token);
        Response<String> apiResponse = xsUtil.queryCoupon(req);
        if (apiResponse.isOk()) {
            System.out.println(JSON.toJSONString(apiResponse.getData()));
        } else {
            System.out.println(JSON.toJSONString(apiResponse.getCode() + apiResponse.getMsg()));
        }
    }

    @Test
    public void xiangshuInvalid() {
        //创建橡树调用器
        xsUtil = new XiangshuUtil(xiangshuConfig.getXsHost(), xiangshuConfig.getXsHostKey(),
                xiangshuConfig.getXsHostSecret());
        InvalidCouponReq req = new InvalidCouponReq();
        req.setToken(token);
        req.setCouponIds(Arrays.asList("1"));
        Response<String> apiResponse = xsUtil.invalidCoupon(req);
        if (apiResponse.isOk()) {
            System.out.println(JSON.toJSONString(apiResponse.getData()));
        } else {
            System.out.println(JSON.toJSONString(apiResponse.getCode() + apiResponse.getMsg()));
        }
    }

    @Test
    public void cancelOrder() {
        //创建橡树调用器
        virtualOrdersApi.cancelOrdersByPlus("1234565432");
    }
}
