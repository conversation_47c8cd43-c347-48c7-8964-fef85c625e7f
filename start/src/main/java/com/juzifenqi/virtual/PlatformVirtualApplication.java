package com.juzifenqi.virtual;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ImportResource;

@MapperScan("com.juzifenqi.virtual.dao")
@ImportResource(locations = {"classpath:dubbo/dubbo-*.xml"})
@SpringBootApplication
public class PlatformVirtualApplication {

    public static void main(String[] args) {
        SpringApplication application = new SpringApplication(PlatformVirtualApplication.class);
        application.run(args);
    }

}
