package com.juzifenqi.virtual.web.api.third;

import com.juzifenqi.virtual.bean.pojo.third.xiangshu.request.XsHotelOrderNotice;
import com.juzifenqi.virtual.bean.pojo.third.xiangshu.request.XsRechargeStatusNotice;
import com.juzifenqi.virtual.core.services.impl.third.xiangshu.XiangshuBusHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 橡树回调
 *
 * <AUTHOR>
 * @date 2023-07-18 10:33:23
 */
@RestController
@RequestMapping("/xiangshu")
public class XsController {

    @Autowired
    private XiangshuBusHandler xiangshuBusHandler;

    @PostMapping("/rechargeStatusNotice")
    public String rechargeStatusNotice(@RequestBody XsRechargeStatusNotice xsRechargeStatusNotice) {
        return xiangshuBusHandler.rechargeStatusNotice(xsRechargeStatusNotice);
    }

    @PostMapping("/hotelOrderNotify")
    public String hotelOrderNotify(@RequestBody XsHotelOrderNotice notice) {
        return xiangshuBusHandler.supplierHotelOrderNotify(notice);
    }
}
