package com.juzifenqi.virtual.web.api.third;

import com.alibaba.fastjson.JSONObject;
import com.juzifenqi.virtual.core.services.impl.third.fulu.FuluBusHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.AbstractEnvironment;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 福禄 回调
 *
 * <AUTHOR>
 * @date 2023-11-09 16:33:23
 */
@RestController
@RequestMapping("/fulu")
public class FlController {

    @Autowired
    private FuluBusHandler fuluBusHandler;

    @Autowired
    private AbstractEnvironment environment;

    /**
     * 充值结果回调
     */
    @PostMapping("/rechargeStatusNotice")
    public String rechargeStatusNotice(@RequestBody JSONObject jsonObject) {
        return fuluBusHandler.rechargeStatusNotice(jsonObject);
    }

    /**
     * 商品信息变更回调
     */
    @PostMapping("/productChangeNotice")
    public String productChangeNotice(@RequestBody JSONObject jsonObject) {
        return fuluBusHandler.productChangeNotice(jsonObject);
    }

    /**
     * 退款成功回调
     */
    @PostMapping("/orderRefundNotice")
    public String orderRefundNotice(@RequestBody JSONObject jsonObject) {
        return fuluBusHandler.orderRefundNotice(jsonObject);
    }

    /**
     * 生成签名接口-非prd环境可用
     */
    @PostMapping("/createSign")
    public String createSign(@RequestBody JSONObject param) {
        String env = environment.getProperty("spring.profiles.active");
        if ("prd".equals(env)) {
            return "prd环境该接口不可用";
        }
        return fuluBusHandler.createSign(param);
    }
}
