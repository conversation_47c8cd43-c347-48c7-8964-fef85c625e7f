package com.juzifenqi.virtual.web.api.third;

import com.juzifenqi.virtual.bean.pojo.third.zixuan.request.ZxRechargeStatusNotice;
import com.juzifenqi.virtual.core.services.impl.third.zixuan.ZixuanUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 子轩回调
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/22 2:54 下午
 */
@RestController
@RequestMapping("/zixuan")
public class ZxController {

    @Autowired
    private ZixuanUtil zixuanUtil;

    @PostMapping("/rechargeStatusNotice")
    public String rechargeStatusNotice(ZxRechargeStatusNotice zxRechargeStatusNotice) {
        return zixuanUtil.rechargeStatusNotice(zxRechargeStatusNotice);
    }

}
