package com.juzifenqi.virtual;

import com.groot.utils.extra.IpUtils;
import java.lang.management.ManagementFactory;
import java.lang.management.RuntimeMXBean;
import java.util.Date;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.AbstractEnvironment;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @verson 1.0
 * @date 2020/9/7 4:43 下午
 */
@RestController
public class MonitorController {

    @Autowired
    AbstractEnvironment environment;

    @RequestMapping("/monitor")
    public String monitor() {
        String env = environment.getProperty("spring.profiles.active");
        RuntimeMXBean bean = ManagementFactory.getRuntimeMXBean();
        long startTime = bean.getStartTime();
        Date startDate = new Date(startTime);
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(" 系统名称 ：virtual-center 启动成功...").append(" \n");
        stringBuilder.append(" 当前环境：").append(env).append(" \n");
        stringBuilder.append(" 当前时间 ：").append(startDate).append(" \n");
        stringBuilder.append(" 本地IP ：").append(IpUtils.getServerIp()).append(" \n");
        return stringBuilder.toString();
    }
}
