<?xml version="1.0" encoding="UTF-8"?>
<!-- - Copyright 1999-2011 Alibaba Group. - - Licensed under the Apache License, 
	Version 2.0 (the "License"); - you may not use this file except in compliance 
	with the License. - You may obtain a copy of the License at - - http://www.apache.org/licenses/LICENSE-2.0 
	- - Unless required by applicable law or agreed to in writing, software - 
	distributed under the License is distributed on an "AS IS" BASIS, - WITHOUT 
	WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. - See the 
	License for the specific language governing permissions and - limitations 
	under the License. -->
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:dubbo="http://dubbo.apache.org/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans-2.5.xsd
        http://dubbo.apache.org/schema/dubbo
        http://dubbo.apache.org/schema/dubbo/dubbo.xsd">

    <dubbo:registry protocol="${dubbo.registry.protocol}" address="${dubbo.registry.address}"/>

    <dubbo:reference check="false" protocol="dubbo" id="iProductGoodsService"
                     interface="com.juzifenqi.product.service.IProductGoodsService"
                     group="${dubbo.product.group}" retries="${dubbo.reference.retries}" timeout="${dubbo.reference.timeout}"/>

    <dubbo:reference check="false" protocol="dubbo" id="iProductService"
                     interface="com.juzifenqi.product.service.IProductService"
                     group="${dubbo.product.group}" retries="${dubbo.reference.retries}" timeout="${dubbo.reference.timeout}"/>

    <dubbo:reference check="false" protocol="dubbo" id="oldBeanJzfqIOrdersOtherService"
                     interface="com.juzifenqi.service.OldBeanJzfqIOrdersOtherService"
                     group="${dubbo.order.group}" retries="${dubbo.reference.retries}" timeout="${dubbo.reference.timeout}" version="1.0"/>

    <dubbo:reference check="false" protocol="dubbo" id="iOrderService"
            interface="com.juzifenqi.order.service.IOrderService"
            group="${dubbo.order.group}" retries="${dubbo.reference.retries}" timeout="${dubbo.reference.timeout}" version="1.0"/>

    <dubbo:reference id="memberQueryApi" retries="0" check="false" protocol="dubbo"
            group="${dubbo.member.group}"
            interface="com.juzifenqi.member.v2.api.MemberQueryApi"/>

    <dubbo:reference check="false" protocol="dubbo" id="plusProfitsAdminApi"
            interface="com.juzifenqi.plus.api.admin.IPlusProfitsAdminApi"
            group="${dubbo.abyss.group}" retries="${dubbo.reference.retries}" timeout="${dubbo.reference.timeout}"/>

    <dubbo:reference check="false" protocol="dubbo" id="iMemberPlusQueryApi"
            interface="com.juzifenqi.plus.api.IMemberPlusQueryApi"
            group="${dubbo.abyss.group}" retries="${dubbo.reference.retries}"
            timeout="${dubbo.reference.timeout}"/>

    <dubbo:reference check="false" protocol="dubbo" id="iPlusOrderSeparateApi"
            interface="com.juzifenqi.plus.api.IPlusOrderSeparateApi"
            group="${dubbo.abyss.group}" retries="${dubbo.reference.retries}"
            timeout="${dubbo.reference.timeout}"/>

</beans>
