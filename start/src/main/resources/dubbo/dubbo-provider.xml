<?xml version="1.0" encoding="UTF-8"?>
<!-- - Copyright 1999-2011 Alibaba Group. - - Licensed under the Apache License, 
	Version 2.0 (the "License"); - you may not use this file except in compliance 
	with the License. - You may obtain a copy of the License at - - http://www.apache.org/licenses/LICENSE-2.0 
	- - Unless required by applicable law or agreed to in writing, software - 
	distributed under the License is distributed on an "AS IS" BASIS, - WITHOUT 
	WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. - See the 
	License for the specific language governing permissions and - limitations 
	under the License. -->
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:dubbo="http://dubbo.apache.org/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans-2.5.xsd
        http://dubbo.apache.org/schema/dubbo
        http://dubbo.apache.org/schema/dubbo/dubbo.xsd">

    <dubbo:application name="${dubbo.application.name}" serialize-check-status="WARN"/>

    <dubbo:protocol name="dubbo" port="${dubbo.protocol.port}"/>

    <dubbo:registry id="juzi-trigger-zookeeper" protocol="${dubbo.registry.protocol}"
            address="${dubbo.registry.address}"/>

    <dubbo:service ref="virtualGoodsApiImpl"
            interface="com.juzifenqi.virtual.api.admin.VirtualGoodsApi"
            protocol="dubbo"
            group="${dubbo.group}" delay="${dubbo.service.delay}"/>

    <dubbo:service ref="virtualOrdersApiImpl"
            interface="com.juzifenqi.virtual.api.order.VirtualOrdersApi"
            protocol="dubbo"
            group="${dubbo.group}" delay="${dubbo.service.delay}"/>

    <dubbo:service ref="virtualHotelOrderApiImpl"
            interface="com.juzifenqi.virtual.api.order.VirtualHotelOrderApi"
            protocol="dubbo"
            group="${dubbo.group}" delay="${dubbo.service.delay}"/>

    <!-- 统一环境变量定义,上面后期删除 -->
    <dubbo:service ref="virtualGoodsApiImpl"
            interface="com.juzifenqi.virtual.api.admin.VirtualGoodsApi"
            protocol="dubbo"
            group="${dubbo.provider.group}" delay="${dubbo.service.delay}"/>

    <dubbo:service ref="virtualOrdersApiImpl"
            interface="com.juzifenqi.virtual.api.order.VirtualOrdersApi"
            protocol="dubbo"
            group="${dubbo.provider.group}" delay="${dubbo.service.delay}"/>

    <dubbo:service ref="virtualHotelOrderApiImpl"
            interface="com.juzifenqi.virtual.api.order.VirtualHotelOrderApi"
            protocol="dubbo"
            group="${dubbo.provider.group}" delay="${dubbo.service.delay}"/>

</beans>
