server.port=8080
#MQ
MQ_ACCESSKEY=42ae72c6fd3aa64d31149b677238
MQ_SECRETKEY=22881f2508075fb2
MQ_ADDRESS=dev-mq-server01.juzishuke.com:9876
mq.topic.virtual.pay.notice=TOPIC_DEV1_VIP_OPENCARD
mq.gid.virtual.pay.notice=GID_DEV1_PAY_VIRTUAL_ORDER
mq.tag.virtual.pay.notice=*
mq.topic.virtual.result=TOPIC_DEV1_PLUS_VIRTUAL_ORDER_RESULT

spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.url=*****************************************************************************************************************************************************************************************************
spring.datasource.username=shop_rw_new
spring.datasource.password=eRg8dNLAfxpenRGO

#log
#logging.config=classpath:logback/logback-docker.xml

#arthas
arthas.appName=virtual-center-dev1
arthas.tunnelServer=ws://************:7777/ws
arthas.telnetPort=-1
arthas.httpPort=-1

#dubbo
dubbo.application.name=virtual-center-provider
dubbo.registry.protocol=zookeeper
dubbo.registry.address=dev-zk.juzishuke.com:2181
dubbo.protocol.name=dubbo
dubbo.protocol.port=-1
dubbo.service.delay=60
dubbo.reference.retries=0
dubbo.reference.timeout=10000
dubbo.group=virtual-center-local
dubbo.product.group=product-group-dev1
dubbo.order.group=super-order-group-dev
dubbo.super.group=super-plus-wff
dubbo.member.group=member-platform-dev1
dubbo.abyss.group=dev1

##éç½®ä¸­å¿ä¿¡æ¯
conf.evn=dev1
conf.project=juziFramework

#mybatis
mybatis.mapperLocations=classpath:mapper/**/*.xml
mybatis.configuration.mapUnderscoreToCamelCase=true
logging.level.com.juzifenqi.virtual.dao=debug

#åé¡µpageHelper
pagehelper.helper-dialect=mysql
pagehelper.reasonable=true
pagehelper.support-methods-arguments=true

#xxl-job
xxl.job.admin.addresses=http://test1-xxljob.juzishuke.com/xxl-job-admin2
xxl.job.executor.appname=virtual-center-dev1
xxl.job.executor.port=10910

#zixuan
third.zixuan.url=https://**************:8443
third.zixuan.appId=N0ILLLbnFs
third.zixuan.appSecret=cIgowzBuFDSlckDK
third.zixuan.callbackUrl=http://dev1-virtual-center.juzifenqi.cn/zixuan/rechargeStatusNotice

#dingding
dingtalk.urlPrefix=https://oapi.dingtalk.com/robot/send
dingtalk.secret.secretEnable=true
dingtalk.accessToken=8641d55084235815bb3d9717a581e6eb77218d9c54ab05cc7c46bb2eea0a1e27
dingtalk.secret.secretToken=SECaae63e4ed3caec2c7db4c5dc50f5b32cd432b10aaed80a9ec98b3c7a323b9320


nacos.config.server-addr=test-nacos.juzishuke.com:8848
nacos.config.namespace=dev1
nacos.config.username=nacos
nacos.config.password=nacos

third.xiangshu.url=api-stag.xiangshuheika.com
third.xiangshu.hostKey=204254550
third.xiangshu.hostSecret=XXgRcA7R6x9ZdSvjwNi7hJ50w91s7X3n
third.xiangshu.callbackUrl=http://dev1-virtual-center.juzishuke.com/xiangshu/rechargeStatusNotice
third.xiangshu.profitKey=223445545
third.xiangshu.profitSecret=asdsafdf

feishu.secret=111111
feishu.url=https://open.feishu.cn/open-apis/bot/v2/hook/4b7ad1ff-c776-4b50-bd4b-97090c08c216
feishu.busSecret=1111
feishu.busUrl=https://open.feishu.cn/open-apis/bot/v2/hook/e98bd66f-a7d9-4440-be16-7676feacd746

mq.topic.order.status.change.notice=TOPIC_DEV_ORDER_NODE_PUSH
mq.gid.order.status.change.notice=GID_DEV_PLUS_VIRTUAL_ORDER_NODE_PUSH

#redis
spring.redis.database=4
spring.redis.host=test-shop-redis-server01.juzishuke.com
spring.redis.port=6379
spring.redis.password=rXhGsYptRzjgmMqq
spring.redis.lettuce.pool.max-active=-1
spring.redis.lettuce.pool.max-wait=-1
spring.redis.lettuce.shutdown-timeout=100
spring.redis.lettuce.pool.max-idle=8
spring.redis.lettuce.pool.min-idle=0

#åéç­ä¿¡Mq
mq.topic.send.msg=TOPIC_DEV_SEND_MSG