server.port=8080
#MQ
MQ_ACCESSKEY=42ae72c3d6fd3aass64d31149b677238
MQ_SECRETKEY=22881f250daa21mk8075fb2
MQ_ADDRESS=*************:9876
mq.topic.virtual.pay.notice=TOPIC_PRE_VIP_OPENCARD
mq.gid.virtual.pay.notice=GID_PRE_PAY_VIRTUAL_ORDER
mq.tag.virtual.pay.notice=*
mq.topic.virtual.result=TOPIC_PRE_PLUS_VIRTUAL_ORDER_RESULT

spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.url=******************************************************************************************************************************************************************************************************
spring.datasource.username=juzi_plus_wr
spring.datasource.password=5254eQ1817d8

#log
logging.config=classpath:logback/logback-docker.xml

#arthas
arthas.appName=virtual-center-pre
arthas.tunnelServer=ws://************:9999/ws
arthas.telnetPort=-1
arthas.httpPort=-1

#dubbo
dubbo.application.name=virtual-center-provider
dubbo.registry.protocol=zookeeper
dubbo.registry.address=************:2181
dubbo.protocol.name=dubbo
dubbo.protocol.port=-1
dubbo.service.delay=60
dubbo.reference.retries=0
dubbo.reference.timeout=10000
dubbo.group=virtual-center-pre
dubbo.provider.group=pre

dubbo.product.group=product-group-pre
dubbo.order.group=super-order-group-pre3
dubbo.super.group=super-plus-pre
dubbo.member.group=member-platform-pre
dubbo.abyss.group=pre

##éç½®ä¸­å¿ä¿¡æ¯
conf.evn=pre
conf.project=juziFramework

#mybatis
mybatis.mapperLocations=classpath:mapper/**/*.xml
mybatis.configuration.mapUnderscoreToCamelCase=true
logging.level.com.juzifenqi.virtual.dao=debug

#åé¡µpageHelper
pagehelper.helper-dialect=mysql
pagehelper.reasonable=true
pagehelper.support-methods-arguments=true

#xxl-job
xxl.job.admin.addresses=http://test-xxl-job-admin.juhuiwangluokeji.com/xxl-job-admin
xxl.job.executor.appname=virtual-center-pre
xxl.job.executor.port=10999

#zixuan
third.zixuan.url=https://**************:8443
third.zixuan.appId=N0ILLLbnFs
third.zixuan.appSecret=cIgowzBuFDSlckDK
third.zixuan.callbackUrl=http://pre-virtual-center.juzishuke.com/zixuan/rechargeStatusNotice

#dingding
dingtalk.urlPrefix=https://oapi.dingtalk.com/robot/send
dingtalk.secret.secretEnable=true
dingtalk.accessToken=8641d55084235815bb3d9717a581e6eb77218d9c54ab05cc7c46bb2eea0a1e27
dingtalk.secret.secretToken=SECaae63e4ed3caec2c7db4c5dc50f5b32cd432b10aaed80a9ec98b3c7a323b9320



nacos.config.server-addr=test-nacos.juhuiwangluokeji.com:8848
nacos.config.namespace=pre
nacos.config.username=nacos
nacos.config.password=nacos

third.xiangshu.url=api-stag.xiangshuheika.com
third.xiangshu.hostKey=204254550
third.xiangshu.hostSecret=XXgRcA7R6x9ZdSvjwNi7hJ50w91s7X3n
third.xiangshu.callbackUrl=http://pre-virtual-center.juzishuke.com/xiangshu/rechargeStatusNotice
third.xiangshu.profitKey=223445545
third.xiangshu.profitSecret=asdsafdf

feishu.secret=111111
feishu.url=https://open.feishu.cn/open-apis/bot/v2/hook/4b7ad1ff-c776-4b50-bd4b-97090c08c216
feishu.busSecret=111
feishu.busUrl=https://open.feishu.cn/open-apis/bot/v2/hook/e98bd66f-a7d9-4440-be16-7676feacd746
mq.topic.order.status.change.notice=TOPIC_PRE_ORDER_NODE_PUSH
mq.gid.order.status.change.notice=GID_PRE_PLUS_VIRTUAL_ORDER_NODE_PUSH

#ç¦ç¦ä¸æ¹éç½®:
third.fulu.url=http://pre-openapi.fulu.com/api/getway
third.fulu.appKey=i4esv1l+76l/7NQCL3QudG90Fq+YgVfFGJAWgT+7qO1Bm9o/adG/1iwO2qXsAXNB
third.fulu.appSecret=0a091b3aa4324435aab703142518a8f7

#redis
spring.redis.database=9
spring.redis.host=pre-ywjs-redis.juhuiwangluokeji.com
spring.redis.port=6379
spring.redis.password=yw_redis_rw:e93rDdr3hvK5AyWh
spring.redis.lettuce.pool.max-active=-1
spring.redis.lettuce.pool.max-wait=-1
spring.redis.lettuce.shutdown-timeout=100
spring.redis.lettuce.pool.max-idle=8
spring.redis.lettuce.pool.min-idle=0

#åéç­ä¿¡Mq
mq.topic.send.msg=TOPIC_PRE_SEND_MSG

mq.topic.order.refund=1
mq.gid.order.refund=1
mq.tag.order.refund=1

switch.white.user=
defaultSupplierId=31