package com.juzifenqi.virtual;

import com.alibaba.fastjson.JSONObject;
import com.groot.utils.http.OKHttp3SimpleUtils;
import com.juzifenqi.virtual.component.enums.DateEnum;
import com.juzifenqi.virtual.component.util.DateUtils;
import java.security.MessageDigest;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

@Slf4j
public class ZixuanUtilTest {

    public static String createLinkString(Map params) {

        List<String> keys = new ArrayList<>(params.keySet());
        Collections.sort(keys);

        String prestr = "";

        for (int i = 0; i < keys.size(); i++) {
            String key = keys.get(i);
            String value = (String) params.get(key);

            if (i == keys.size() - 1) {//拼接时，不包括最后一个&字符
                prestr = prestr + key + "=" + value;
            } else {
                prestr = prestr + key + "=" + value + "&";
            }
        }

        return prestr;
    }

    /**
     * MD5指纹算法
     */
    public static String md5(String str) {
        if (str == null) {
            return null;
        }

        try {
            MessageDigest messageDigest = MessageDigest.getInstance("MD5");
            messageDigest.update(str.getBytes());
            return bytesToHexString(messageDigest.digest());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }

    /**
     * 将二进制转换成16进制
     */
    public static String bytesToHexString(byte[] src) {
        StringBuilder stringBuilder = new StringBuilder("");
        if (src == null || src.length <= 0) {
            return null;
        }
        for (int i = 0; i < src.length; i++) {
            int v = src[i] & 0xFF;
            String hv = Integer.toHexString(v);
            if (hv.length() < 2) {
                stringBuilder.append(0);
            }
            stringBuilder.append(hv);
        }
        return stringBuilder.toString();
    }

    @Test
    public void orderPushRecharge() {
        String orderSn = "**********";
        String timestamp = DateUtils.dateToStr(new Date(), DateEnum.DATE_BANK_SEQ_MILL);
        HashMap<String, Object> params = new HashMap<>();
        params.put("appId", "N0ILLLbnFs");
        params.put("appSecret", "cIgowzBuFDSlckDK");
        params.put("outOrderId", orderSn);
        params.put("uuid", "***********");
        params.put("itemId", "100001");//直充测试商品100001
        params.put("itemPrice", "1.000");//直充测试商品价格1元
        params.put("amount", "1");
        params.put("callbackUrl", null);
        params.put("timestamp", timestamp);
        String signStr = createLinkString(params);
        log.info("订单:{},排序拼接参数signStr:{}", orderSn, signStr);
        String sign = md5(signStr);
        log.info("订单:{},md5签名值:{}", orderSn, sign);
        params.put("sign", sign);
        params.remove("appSecret");//appSecret只在签名时使用，请不要作为参数进行网络传输。
        log.info("子轩下单开始,订单:{}", orderSn);

        String methodUrl = "https://120.79.157.192:8443" + "/api/order/submit";
        log.info("======调取子轩接口：直充下单接口===开始," + "url={}, param={}", methodUrl,
                JSONObject.toJSONString(params));
        JSONObject resultJson = OKHttp3SimpleUtils.postByForm(methodUrl, params);
        log.info("======调取子轩接口：直充下单接口===返回结果,result={}", resultJson.toJSONString());
        JSONObject jsonObject = resultJson.getJSONObject("responseVal");

    }


}
