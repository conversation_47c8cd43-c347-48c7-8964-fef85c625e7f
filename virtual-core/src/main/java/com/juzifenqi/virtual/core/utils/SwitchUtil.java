package com.juzifenqi.virtual.core.utils;

import com.juzifenqi.virtual.core.conf.ConfigProperties;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * 系统切换白名单
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/9/7 10:18
 */
@Slf4j
@Component
public class SwitchUtil {

    @Autowired
    private ConfigProperties configProperties;

    /**
     * 判断新逻辑白名单用户，为空则全走新逻辑，配置了则配置的用户走新逻辑
     */
    public boolean isNew(Integer userId) {
        List<Integer> whiteUser = configProperties.switchWhiteUser;
        return CollectionUtils.isEmpty(whiteUser) || whiteUser.contains(userId);
    }
}
