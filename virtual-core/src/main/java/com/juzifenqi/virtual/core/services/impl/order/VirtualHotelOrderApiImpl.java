package com.juzifenqi.virtual.core.services.impl.order;

import com.alibaba.fastjson.JSON;
import com.groot.utils.exception.LogUtil;
import com.juzifenqi.virtual.api.order.VirtualHotelOrderApi;
import com.juzifenqi.virtual.bean.pojo.VirtualOrderSupplierCoupon;
import com.juzifenqi.virtual.bean.pojo.VirtualOrders;
import com.juzifenqi.virtual.bean.pojo.VirtualSupplierHotelOrder;
import com.juzifenqi.virtual.bean.system.VirtualResult;
import com.juzifenqi.virtual.bean.vo.HotelOrderDetailVo;
import com.juzifenqi.virtual.bean.vo.HotelUrlVo;
import com.juzifenqi.virtual.bean.vo.VirtualOrdersWorkerVo;
import com.juzifenqi.virtual.component.enums.VirtualErrorEnum;
import com.juzifenqi.virtual.component.exception.VirtualException;
import com.juzifenqi.virtual.core.services.impl.third.HandlerContext;
import com.juzifenqi.virtual.dao.VirtualOrderSupplierCouponDao;
import com.juzifenqi.virtual.dao.VirtualOrdersDao;
import com.juzifenqi.virtual.dao.VirtualSupplierHotelOrderDao;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * 酒店订单
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/30 16:32
 */
@Slf4j
@Service
public class VirtualHotelOrderApiImpl implements VirtualHotelOrderApi {

    @Autowired
    private VirtualSupplierHotelOrderDao  hotelOrderDao;
    @Autowired
    private VirtualOrdersDao              ordersDao;
    @Autowired
    private HandlerContext                context;
    @Autowired
    private VirtualOrderSupplierCouponDao couponDao;

    @Override
    public VirtualResult<List<VirtualSupplierHotelOrder>> getVirtualHotelOrder(String orderSn) {
        log.info("获取有效酒店订单列表：{}", orderSn);
        VirtualResult<List<VirtualSupplierHotelOrder>> result = new VirtualResult<>();
        // 获取虚拟权益订单关联有效的酒店信息
        List<VirtualSupplierHotelOrder> order = hotelOrderDao.selectHotelOrderByOrderSn(orderSn);
        // 只留下未使用优惠券的酒店订单
        order.removeIf(e -> StringUtils.isNotBlank(e.getCouponId()));
        // 获取虚拟权益订单领取的酒店券id关联的有效酒店订单信息
        List<VirtualOrderSupplierCoupon> coupons = couponDao.selectByOrderSns(
                Collections.singletonList(orderSn));
        if (CollectionUtils.isEmpty(coupons)) {
            log.info("获取有效酒店订单列表未获取到已领取的酒店券：{}", orderSn);
            result.success("查询成功", order);
            return result;
        }
        List<VirtualSupplierHotelOrder> hotelOrders = hotelOrderDao.selectHotelOrderByCouponIds(
                coupons.stream().map(VirtualOrderSupplierCoupon::getCouponId)
                        .collect(Collectors.toList()));
        log.info("获取虚拟权益对应酒店券id使用的酒店订单列表：{}", JSON.toJSONString(hotelOrders));
        if (!CollectionUtils.isEmpty(hotelOrders)) {
            order.addAll(hotelOrders);
        }
        result.success("查询成功", order);
        return result;
    }

    @Override
    public VirtualResult<String> getHotelListUrl(String orderSn) {
        VirtualResult<String> result = new VirtualResult<>();
        try {
            log.info("获取去预定酒店连接入参：{}", orderSn);
            VirtualOrders orders = ordersDao.getByOrderSn(orderSn);
            if (orders == null) {
                result.error(VirtualErrorEnum.ERROR_100100.getCode(), "无效的虚拟订单号");
                return result;
            }
            VirtualOrdersWorkerVo vo = new VirtualOrdersWorkerVo();
            vo.setVirtualOrder(orders);
            vo.setSupplierId(orders.getSupplierId());
            HotelUrlVo hotelUrl = context.getHotelUrl(vo);
            if (hotelUrl == null) {
                result.error(VirtualErrorEnum.ERROR_100100.getCode(), "未获取到跳转url");
                return result;
            }
            result.success("获取成功", hotelUrl.getHotelUrl());
        } catch (Exception e) {
            result.error(VirtualErrorEnum.ERROR_100100.getCode(), "获取预定酒店链接失败");
            if (e instanceof VirtualException) {
                result.error(VirtualErrorEnum.ERROR_100100.getCode(), e.getMessage());
            }
            LogUtil.printLog(e, "获取预定酒店url异常");
        }
        return result;
    }

    @Override
    public VirtualResult<HotelOrderDetailVo> getHotelOrderDetail(String orderSn) {
        log.info("获取跳转酒店详情需要的参数：{}", orderSn);
        VirtualResult<HotelOrderDetailVo> result = new VirtualResult<>();
        try {
            VirtualOrders orders = ordersDao.getByOrderSn(orderSn);
            if (orders == null) {
                result.error(VirtualErrorEnum.ERROR_100100.getCode(), "无效的虚拟订单号");
                return result;
            }
            VirtualOrdersWorkerVo vo = new VirtualOrdersWorkerVo();
            vo.setVirtualOrder(orders);
            vo.setSupplierId(orders.getSupplierId());
            HotelOrderDetailVo detail = context.getHotelOrderDetail(vo);
            if (detail == null) {
                result.error(VirtualErrorEnum.ERROR_100100.getCode(), "未获取到酒店订单参数");
                return result;
            }
            result.success("获取成功", detail);
        } catch (Exception e) {
            result.error(VirtualErrorEnum.ERROR_100100.getCode(), "获取酒店订单详情参数失败");
            if (e instanceof VirtualException) {
                result.error(VirtualErrorEnum.ERROR_100100.getCode(), e.getMessage());
            }
            LogUtil.printLog(e, "获取酒店订单详情参数异常");
        }
        return result;
    }
}
