package com.juzifenqi.virtual.core.services.impl.third.xiangshu.model;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * 领券返回data对象
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/30 17:36
 */
@Data
public class XiangshuDistributeCouponDataResp implements Serializable {
    private static final long serialVersionUID = 1297864475820298810L;

    /**
     * 批次号
     */
    private String batch_id;

    /**
     * 领取状态 success成功 fail 失败
     */
    private String status;

    /**
     * 幂等id
     */
    private String txid;

    /**
     * 失败原因
     */
    private String reason;

    /**
     * 优惠券ids
     */
    private List<String> coupon_ids;
}
