package com.juzifenqi.virtual.core.services.impl.third;

import com.juzifenqi.virtual.bean.pojo.VirtualOrders;
import com.juzifenqi.virtual.bean.vo.CardDecodeRespVo;
import com.juzifenqi.virtual.bean.vo.HotelOrderDetailVo;
import com.juzifenqi.virtual.bean.vo.HotelUrlVo;
import com.juzifenqi.virtual.bean.vo.RechargeRespVo;
import com.juzifenqi.virtual.bean.vo.VirtualOrdersCardVo;
import com.juzifenqi.virtual.bean.vo.VirtualOrdersWorkerVo;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 橡树三方业务处理类
 *
 * <AUTHOR>
 * @date 2023-07-14 10:33:23
 */
@Component
@Slf4j
public abstract class AbstractStrategyHandler {

    /**
     * 虚拟权益下单（调用三方充值）
     */
    public RechargeRespVo orderRecharge(VirtualOrdersWorkerVo paramVo) {
        return null;
    }

    /**
     * 充值结果查证
     */
    public RechargeRespVo getRechargeStatus(VirtualOrdersWorkerVo paramVo) {
        return null;
    }

    /**
     * 解析卡密
     */
    public CardDecodeRespVo decodeCard(VirtualOrdersCardVo paramVo) {
        return null;
    }

    /**
     * 获取去预定酒店跳转的url
     */
    public HotelUrlVo getHotelUrl(VirtualOrdersWorkerVo paramVo) {
        return null;
    }

    /**
     * 获取查看酒店订单详情需要的参数
     */
    public HotelOrderDetailVo getHotelOrderDetail(VirtualOrdersWorkerVo paramVo) {
        return null;
    }

    /**
     * 取消虚拟权益订单
     */
    public Boolean cancelVirtualOrder(VirtualOrders paramVo) {
        return null;
    }
}
