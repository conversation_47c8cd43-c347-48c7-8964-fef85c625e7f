package com.juzifenqi.virtual.core.job;

import com.juzifenqi.virtual.bean.pojo.VirtualOrders;
import com.juzifenqi.virtual.bean.pojo.VirtualOrdersWorker;
import com.juzifenqi.virtual.bean.vo.VirtualOrdersWorkerVo;
import com.juzifenqi.virtual.component.util.DateUtils;
import com.juzifenqi.virtual.core.services.impl.third.HandlerContext;
import com.juzifenqi.virtual.dao.VirtualOrdersDao;
import com.juzifenqi.virtual.dao.VirtualOrdersWorkerDao;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.util.ShardingUtil;
import java.util.Date;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * 充值结果主动查证定时任务
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/20 9:37 下午
 */
@Component
@Slf4j
public class GetRechargeStatusJob {

    @Autowired
    private VirtualOrdersWorkerDao virtualOrdersWorkerDao;
    @Autowired
    private VirtualOrdersDao       virtualOrdersDao;
    @Autowired
    private HandlerContext         handlerContext;

    public static final Integer MAX_VERIFY_TIME_DURATION = 3;//三分钟后主动去查

    @XxlJob("getRechargeStatusJob")
    public ReturnT<String> execute(String param) {
        try {
            ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
            // 当前机器下标
            int index = shardingVO.getIndex();
            int limit = StringUtils.isNotBlank(param) ? Integer.parseInt(param) : 100;
            // 计算数据查询范围
            int start = index * limit;
            log.info("充值结果主动查证任务开始，处理数量：{}，当前机器下标：{}，开始查询下标：{}", limit,
                    index, start);
            Date virifyTime = DateUtils.minusMinutes(new Date(), MAX_VERIFY_TIME_DURATION);
            List<VirtualOrdersWorker> virtualOrdersWorkers = virtualOrdersWorkerDao.getAllNeedVerifyOrdersByTimeLimit(
                    virifyTime, start, limit);
            log.info("当前待查证充值状态的订单数量：size:{}", virtualOrdersWorkers.size());
            if (CollectionUtils.isEmpty(virtualOrdersWorkers)) {
                return ReturnT.SUCCESS;
            }
            for (VirtualOrdersWorker virtualOrdersWorker : virtualOrdersWorkers) {
                String orderSn = virtualOrdersWorker.getOrderSn();
                try {
                    log.info("批量处理-单条主动查证开始，orderSn：{}", orderSn);
                    VirtualOrders order = virtualOrdersDao.getVirtualOrdersByOrderSn(orderSn);
                    if (order == null) {
                        log.info("单条主动查证未查询到有效订单，orderSn：{}", orderSn);
                        continue;
                    }
                    VirtualOrdersWorkerVo paramVo = new VirtualOrdersWorkerVo();
                    BeanUtils.copyProperties(virtualOrdersWorker, paramVo);
                    paramVo.setSupplierId(order.getSupplierId());
                    paramVo.setVirtualOrder(order);
                    handlerContext.getRechargeStatus(paramVo);
                    log.info("批量处理-单条主动查证完成，orderSn：{}", orderSn);
                } catch (Exception e) {
                    log.info("批量处理-单条主动查证出现未知异常，orderSn：{}", orderSn);
                }
            }
            log.info("充值结果主动查证任务结束");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("充值结果主动查证任务执行发生异常", e);
        }
        return ReturnT.FAIL;
    }
}
