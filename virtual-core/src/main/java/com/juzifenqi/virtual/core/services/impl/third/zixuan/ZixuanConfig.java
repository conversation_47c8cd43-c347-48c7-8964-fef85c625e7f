package com.juzifenqi.virtual.core.services.impl.third.zixuan;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 子轩三方交互配置类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/20 9:38 下午
 */
@Component
public class ZixuanConfig {

    /**
     * 服务器地址 (ip + port)
     */
    @NacosValue(value = "${third.zixuan.url}", autoRefreshed = true)
    private String url;

    /**
     * 应用ID
     */
    @NacosValue(value = "${third.zixuan.appId}", autoRefreshed = true)
    private String appId;

    /**
     * 密钥 appSecret只在签名时使用，请不要作为参数进行网络传输。
     */
    @NacosValue(value = "${third.zixuan.appSecret}", autoRefreshed = true)
    private String appSecret;


    /**
     * 订单状态回调地址，桔子提供
     */
    @NacosValue(value = "${third.zixuan.callbackUrl}", autoRefreshed = true)
    private String callbackUrl;

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getAppSecret() {
        return appSecret;
    }

    public void setAppSecret(String appSecret) {
        this.appSecret = appSecret;
    }

    public String getCallbackUrl() {
        return callbackUrl;
    }

    public void setCallbackUrl(String callbackUrl) {
        this.callbackUrl = callbackUrl;
    }
}
