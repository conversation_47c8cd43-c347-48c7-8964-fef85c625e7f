package com.juzifenqi.virtual.core.conf;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 会员权益Mq配置
 */
@Data
@Component
public class VirtualMqConfig {

    /**
     * 商城虚拟商品支付成功
     */
    @NacosValue(value = "${mq.topic.virtual.pay.notice}", autoRefreshed = true)
    private String virtualPayNoticeTopic;

    @NacosValue(value = "${mq.gid.virtual.pay.notice}", autoRefreshed = true)
    private String virtualPayNoticeGid;


    /**
     * 订单状态变更
     */
    @NacosValue(value = "${mq.topic.order.status.change.notice}", autoRefreshed = true)
    private String orderStatusChangeTopic;

    @NacosValue(value = "${mq.gid.order.status.change.notice}", autoRefreshed = true)
    private String orderStatusChangeGid;

    /**
     * 子轩充值结果
     */
    @NacosValue(value = "${mq.topic.virtual.result}", autoRefreshed = true)
    private String virtualResultTopic;

    /**
     * 发送短信
     */
    @Value("${mq.topic.send.msg}")
    private String topicSendMsg;

    /**
     * 订单中心退款mq
     */
    @Value("${mq.topic.order.refund}")
    private String topicOrderRefund;
    @Value("${mq.gid.order.refund}")
    private String gidOrderRefund;
}
