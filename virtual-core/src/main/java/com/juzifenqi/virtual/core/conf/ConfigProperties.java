package com.juzifenqi.virtual.core.conf;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import java.util.List;
import org.springframework.stereotype.Component;

/**
 * 配置文件
 *
 * <AUTHOR>
 * @date 2024/9/9 11:09
 */
@Component
public class ConfigProperties {

    /**
     * 新系统切换用户白名单
     */
    @NacosValue(value = "${switch.white.user}", autoRefreshed = true)
    public List<Integer> switchWhiteUser;

    /**
     * 分流异常、开关关闭、配置为空的情况下的兜底分流主体
     */
    @NacosValue(value = "${defaultSupplierId:}", autoRefreshed = true)
    public Integer defaultSupplierId;
}
