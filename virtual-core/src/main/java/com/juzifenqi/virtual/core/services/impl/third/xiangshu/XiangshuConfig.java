package com.juzifenqi.virtual.core.services.impl.third.xiangshu;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 橡树三方交互配置类
 *
 * <AUTHOR>
 * @date 2023-07-14 10:33:23
 */
@Component
public class XiangshuConfig {


    /**
     * 接口域名:"api-stag.xiangshuheika.com" (不加http://)
     */
    @Value("${third.xiangshu.url}")
    private String xsHost;

    /**
     * api网关的key
     */
    @Value("${third.xiangshu.hostKey}")
    private String xsHostKey;

    /**
     * api网关的secret
     */
    @Value("${third.xiangshu.hostSecret}")
    private String xsHostSecret;

    /**
     * 订单状态回调地址，桔子提供
     */
    @Value("${third.xiangshu.callbackUrl}")
    private String callbackUrl;

    /**
     * 权益appkey
     */
    @Value("${third.xiangshu.profitKey}")
    private String profitKey;

    /**
     * 权益appSecret
     */
    @Value("${third.xiangshu.profitSecret}")
    private String profitSecret;


    public String getXsHost() {
        return xsHost;
    }

    public void setXsHost(String xsHost) {
        this.xsHost = xsHost;
    }

    public String getXsHostKey() {
        return xsHostKey;
    }

    public void setXsHostKey(String xsHostKey) {
        this.xsHostKey = xsHostKey;
    }

    public String getXsHostSecret() {
        return xsHostSecret;
    }

    public void setXsHostSecret(String xsHostSecret) {
        this.xsHostSecret = xsHostSecret;
    }

    public String getCallbackUrl() {
        return callbackUrl;
    }

    public void setCallbackUrl(String callbackUrl) {
        this.callbackUrl = callbackUrl;
    }

    public String getProfitKey() {
        return profitKey;
    }

    public void setProfitKey(String profitKey) {
        this.profitKey = profitKey;
    }

    public String getProfitSecret() {
        return profitSecret;
    }

    public void setProfitSecret(String profitSecret) {
        this.profitSecret = profitSecret;
    }
}
