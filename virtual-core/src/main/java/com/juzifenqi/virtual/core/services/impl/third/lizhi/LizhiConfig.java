package com.juzifenqi.virtual.core.services.impl.third.lizhi;

import lombok.Data;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import org.springframework.stereotype.Component;

/**
 * 荔枝三方交互配置类
 * <AUTHOR>
 */
@Data
@Component
public class LizhiConfig {
    /**
     * 服务器地址 (ip + port)
     */
    @NacosValue(value = "${third.lizhi.url}", autoRefreshed = true)
    private String url;

    /**
     * 应用ID
     */
    @NacosValue(value = "${third.lizhi.appId}", autoRefreshed = true)
    private String appId;

    /**
     * 密钥 appSecret只在签名时使用，请不要作为参数进行网络传输
     */
    @NacosValue(value = "${third.lizhi.appSecret}", autoRefreshed = true)
    private String appSecret;

}