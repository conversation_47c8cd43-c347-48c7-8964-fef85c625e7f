package com.juzifenqi.virtual.core.services.impl.third.common;

import com.alibaba.fastjson.JSONObject;
import com.groot.utils.exception.LogUtil;
import com.juzi.smsgroup.vo.SmsMsgV2Dto;
import com.juzifenqi.order.vo.OrderCancelRefundResultVO;
import com.juzifenqi.order.vo.OrderCancelRefundVO;
import com.juzifenqi.order.vo.RefundInfo;
import com.juzifenqi.virtual.bean.bo.PlusShuntSupplierBo;
import com.juzifenqi.virtual.bean.pojo.*;
import com.juzifenqi.virtual.bean.vo.VirtualOrderRechargeSuccVo;
import com.juzifenqi.virtual.bean.vo.VirtualOrdersWorkerVo;
import com.juzifenqi.virtual.component.enums.*;
import com.juzifenqi.virtual.component.exception.VirtualException;
import com.juzifenqi.virtual.component.models.VirtualOrdersTrailModel;
import com.juzifenqi.virtual.component.util.RobotUtil;
import com.juzifenqi.virtual.core.common.Constant;
import com.juzifenqi.virtual.core.mq.producer.MqProducer;
import com.juzifenqi.virtual.core.utils.RedisUtils;
import com.juzifenqi.virtual.core.utils.SerialNoUtils;
import com.juzifenqi.virtual.core.utils.SwitchUtil;
import com.juzifenqi.virtual.dao.*;
import com.juzifenqi.virtual.manager.OrderManager;
import java.math.BigDecimal;
import java.util.*;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * 统一三方业务处理类
 *
 * <AUTHOR>
 * @date 2023-11-08 13:43:23
 */
@Component
@Slf4j
public class CommonBusHandler {

    @Resource
    private VirtualOrdersWorkerDao  virtualOrdersWorkerDao;
    @Resource
    private VirtualOrdersDao        virtualOrdersDao;
    @Resource
    private VirtualOrdersTrailModel ordersTrailModel;
    @Resource
    private RobotUtil               robotUtil;
    @Resource
    private OrderManager            orderManager;
    @Resource
    private MqProducer              mqProducer;
    @Resource
    private VirtualOrdersCardDao    virtualOrdersCardDao;
    @Resource
    private VirtualOrderPayFlowDao virtualOrderPayFlowDao;
    @Resource
    private VirtualOrdersDoubtDao         virtualOrdersDoubtDao;
    @Autowired
    private VirtualOrderSupplierCouponDao virtualOrderSupplierCouponDao;
    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private SwitchUtil                switchUtil;
    @Autowired
    private VirtualOrderRefundInfoDao virtualOrderRefundInfoDao;

    /**
     * 最大卡密解析次数
     */
    public static final Integer MAX_RETRY_DECODE_TIMES = 5;

    /**
     * 分流主体缓存
     */
    public static final String SUPPLIER_CONFIG = "super_plus:PLUS_SHUNT_SUPPLIER_";

    /**
     * 桔子分流缓存key
     */
    public static final Integer JUZI_SUPPLIER_CONFIG_KEY = 0;

    /**
     * 操作人id
     */
    public static final Integer OPERATING_ID = 0;

    /**
     * 操作人名称
     */
    public static final String OPERATING_NAME = "system";

    /**
     * 下单失败处理
     *
     * @param workerId 任务ID
     * @param order 订单信息
     * @param channel 渠道信息
     * @param msg 备注信息
     */
    public void handleSubmitOrderFail(Integer workerId, VirtualOrders order, String channel,
            String msg) {
        log.info("=====>调用{}下单失败 处理开始：orderSn[{}] msg[{}]", channel, order.getOrderSn(),
                msg);
        String title = channel + "下单失败";
        try {
            // 1.更新worker表
            VirtualOrdersWorker worker = new VirtualOrdersWorker();
            worker.setId(workerId);
            worker.setOrderSubmitStatus(OrderSubmitStatusEnum.ORDER_FAIL.getCode());
            worker.setOrderSubmitTime(new Date());
            worker.setRemark(title + ":" + msg);
            virtualOrdersWorkerDao.updateVirtualOrdersWorkerById(worker);
            // 2.更新订单状态
            VirtualOrders virtualOrders = new VirtualOrders();
            virtualOrders.setId(order.getId());
            virtualOrders.setOrderStatus(OrderStatusEnum.STATUS_ORDER_FAIL.getCode());
            virtualOrders.setRemark(worker.getRemark());
            // 20231110 zjf 充值状态赋值
            virtualOrders.setRechargeStatus(RechargeStatusEnum.RECHARGE_FAIL.getCode());
            virtualOrdersDao.updateVirtualOrdersById(virtualOrders);
            // 是否0元订单
            boolean zeroOrder = order.getOrderMoney().compareTo(BigDecimal.ZERO) == 0;
            // 3.新增轨迹 轨迹数据不展示三方名称
            String remark = zeroOrder ? "下单失败,0元订单不退款" : "下单失败";
            ordersTrailModel.saveOrdersTrail(order.getOrderSn(),
                    OrderStatusEnum.STATUS_ORDER_NO_PUSH.getCode(),
                    OrderStatusEnum.STATUS_ORDER_FAIL.getCode(), remark);
            // 4.取消订单退款 0元订单不调用订单中心接口
            if (!zeroOrder) {
                cancelOrder(order, title);
            }
        } catch (Exception e) {
            LogUtil.printLog(e, title + "处理异常");
        }
        log.info("=====>调用{}下单失败 处理结束：orderSn[{}]", channel, order.getOrderSn());
    }

    /**
     * 下单成功处理
     *
     * @param workerId 任务ID
     * @param order 订单信息
     * @param channel 渠道信息
     * @param supplierOrderSn 三方单号
     */
    public void handleSubmitOrderSuc(Integer workerId, VirtualOrders order, String channel,
            String supplierOrderSn) {
        log.info("=====>调用{}下单成功 处理开始：orderSn[{}]", channel, order.getOrderSn());
        String title = channel + "下单成功";
        try {
            // 1.更新worker表
            VirtualOrdersWorker worker = new VirtualOrdersWorker();
            worker.setId(workerId);
            worker.setOrderSubmitStatus(OrderSubmitStatusEnum.ORDER_SUCCESS.getCode());
            worker.setOrderSubmitTime(new Date());
            worker.setOrderRechargeStatus(OrderRechargeStatusEnum.RECHARGE_NO_VERIFY.getCode());
            worker.setRemark(StringUtils.isNotEmpty(supplierOrderSn) ? title
                    : channel + "下单接口请求超时,视作下单成功");
            virtualOrdersWorkerDao.updateVirtualOrdersWorkerById(worker);

            // 2.更新订单状态
            VirtualOrders virtualOrders = new VirtualOrders();
            virtualOrders.setId(order.getId());
            virtualOrders.setOrderStatus(OrderStatusEnum.STATUS_ORDER_SUCCESS.getCode());
            virtualOrders.setRemark(OrderStatusEnum.STATUS_ORDER_SUCCESS.getDesc());
            virtualOrders.setSupplierOrderSn(supplierOrderSn);
            virtualOrdersDao.updateVirtualOrdersById(virtualOrders);

            // 3.新增轨迹 轨迹数据不展示三方名称
            ordersTrailModel.saveOrdersTrail(order.getOrderSn(),
                    OrderStatusEnum.STATUS_ORDER_NO_PUSH.getCode(),
                    OrderStatusEnum.STATUS_ORDER_SUCCESS.getCode(), "下单成功");
        } catch (Exception e) {
            LogUtil.printLog(e, title + "处理异常");
        }
        log.info("=====>调用{}下单成功 处理结束：orderSn[{}]", channel, order.getOrderSn());
    }

    /**
     * 查证处理成功
     *
     * @param order 订单信息
     */
    public void handleSucVerify(Integer workerId, VirtualOrders order, String title,
            VirtualOrderRechargeSuccVo vo, Boolean isSubmit) {
        log.info("=====>调用{}-充值成功 处理开始,orderSn={}", title, order.getOrderSn());
        title += "-充值成功";
        try {
            // 1.更新worker表
            VirtualOrdersWorker worker = new VirtualOrdersWorker();
            worker.setId(workerId);
            worker.setOrderRechargeStatus(OrderRechargeStatusEnum.RECHARGE_SUCCESS.getCode());
            worker.setOrderRechargeTime(new Date());
            worker.setRemark(title);
            if (isSubmit) {
                worker.setOrderSubmitStatus(OrderSubmitStatusEnum.ORDER_SUCCESS.getCode());
                worker.setOrderSubmitTime(worker.getOrderRechargeTime());
            }
            virtualOrdersWorkerDao.updateVirtualOrdersWorkerById(worker);

            // 2.更新订单状态
            VirtualOrders virtualOrders = new VirtualOrders();
            virtualOrders.setId(order.getId());
            virtualOrders.setOrderStatus(OrderStatusEnum.STATUS_RECHARGE_SUCCESS.getCode());
            virtualOrders.setRechargeStatus(RechargeStatusEnum.RECHARGE_SUCCESS.getCode());
            virtualOrders.setRemark(OrderStatusEnum.STATUS_RECHARGE_SUCCESS.getDesc());
            virtualOrdersDao.updateVirtualOrdersById(virtualOrders);

            // 3.记录订单轨迹
            int oldStatus = isSubmit ? OrderStatusEnum.STATUS_ORDER_NO_PUSH.getCode()
                    : OrderStatusEnum.STATUS_ORDER_SUCCESS.getCode();
            ordersTrailModel.saveOrdersTrail(order.getOrderSn(), oldStatus,
                    OrderStatusEnum.STATUS_RECHARGE_SUCCESS.getCode(), "充值成功");
            // 4.卡密类型订单 保存virtual_orders_card 解析卡密信息
            if (order.getRechargeType() == RechargeTypeEnum.CARD.getCode()) {
                VirtualOrdersCard card = Objects.isNull(vo) ? null : vo.getCard();
                if (card != null) {
                    // 存在card说明已经通过查证接口获取卡密信息，直接保存成功记录即可
                    // 保险起见，查询是否存在card记录，存在则更新，不存在则新增
                    VirtualOrdersCard virtualCard = virtualOrdersCardDao.selectByOrderSn(
                            order.getOrderSn());
                    if (virtualCard != null) {
                        // 更新卡密信息
                        card.setId(virtualCard.getId());
                        virtualOrdersCardDao.updateVirtualOrdersCardById(card);
                    } else {
                        // 新增卡密信息
                        card.setOrderSn(order.getOrderSn());
                        card.setRetryNum(0);
                        virtualOrdersCardDao.insertVirtualOrdersCard(card);
                    }
                } else {
                    // 如果是回执，没有卡密信息，则新增记录，后续Job处理解析
                    VirtualOrdersCard virtualOrdersCard = new VirtualOrdersCard();
                    virtualOrdersCard.setOrderSn(order.getOrderSn());
                    virtualOrdersCard.setCardState(VirtualCardStateEnum.DECODE_WAIT.getCode());
                    virtualOrdersCard.setRetryNum(0);
                    virtualOrdersCard.setCreateTime(new Date());
                    virtualOrdersCardDao.insertVirtualOrdersCard(virtualOrdersCard);
                }
            }
            // 5.酒店券直充类型订单 保存virtual_order_supplier_coupon 领券信息
            if (order.getRechargeType() == RechargeTypeEnum.HOTEL.getCode()) {
                VirtualOrderSupplierCoupon coupon = Objects.isNull(vo) ? null : vo.getCoupon();
                if (!Objects.isNull(coupon)) {
                    if (coupon.getId() != null) {
                        // 如果id存在则说明是查证信息，反更新领券信息
                        virtualOrderSupplierCouponDao.updateVirtualOrderSupplierCouponById(coupon);
                    } else {
                        // 保存领券信息
                        virtualOrderSupplierCouponDao.saveVirtualOrderSupplierCoupon(coupon);
                    }
                }
            }
            // 6.推送充值成功mq
            sendRechargeResultMq(order, MqResultCodeEnum.SUCCESS);
        } catch (Exception e) {
            LogUtil.printLog(e, title + " 处理异常");
        }
        log.info("=====>调用{} 处理结束,orderSn={}", title, order.getOrderSn());
    }

    /**
     * 充值失败处理
     *
     * @param message 失败原因
     */
    public void handleFailVerify(String title, String message, Integer workerId,
            VirtualOrders order, Boolean isSubmit) {
        log.info("=====>调用{}-充值失败 处理开始,orderSn={} msg={}", title, order.getOrderSn(),
                message);
        title += "-充值失败";
        try {
            // 1.更新worker状态
            VirtualOrdersWorker worker = new VirtualOrdersWorker();
            worker.setId(workerId);
            worker.setOrderRechargeStatus(OrderRechargeStatusEnum.RECHARGE_FAIL.getCode());
            if (isSubmit) {
                // 同步充值接口需要处理订单提交状态
                worker.setOrderSubmitStatus(OrderSubmitStatusEnum.ORDER_FAIL.getCode());
            }
            worker.setOrderRechargeTime(new Date());
            worker.setRemark(title + ":" + message);
            virtualOrdersWorkerDao.updateVirtualOrdersWorkerById(worker);

            // 2.更新订单状态
            VirtualOrders virtualOrders = new VirtualOrders();
            virtualOrders.setId(order.getId());
            virtualOrders.setOrderStatus(OrderStatusEnum.STATUS_RECHARGE_FAIL.getCode());
            virtualOrders.setRechargeStatus(RechargeStatusEnum.RECHARGE_FAIL.getCode());
            virtualOrders.setRemark(OrderStatusEnum.STATUS_RECHARGE_FAIL.getDesc());
            virtualOrdersDao.updateVirtualOrdersById(virtualOrders);

            // 是否0元订单
            boolean zeroOrder = order.getOrderMoney().compareTo(BigDecimal.ZERO) == 0;
            String remark = zeroOrder ? "充值失败,0元订单不退款" : "充值失败";
            // 3.记录订单轨迹 轨迹数据不展示三方名称
            int oldStatus = isSubmit ? OrderStatusEnum.STATUS_ORDER_NO_PUSH.getCode()
                    : OrderStatusEnum.STATUS_ORDER_SUCCESS.getCode();
            ordersTrailModel.saveOrdersTrail(order.getOrderSn(), oldStatus,
                    OrderStatusEnum.STATUS_RECHARGE_FAIL.getCode(), remark);
            // 4.失败订单退款 0元订单不调用订单中心接口
            if (!zeroOrder) {
                cancelOrder(order, title);
            }
            // 5.推送充值失败mq
            sendRechargeResultMq(order, MqResultCodeEnum.FAIL);
        } catch (Exception e) {
            LogUtil.printLog(e, title + " 处理异常");
        }
        log.info("=====>调用{} 处理结束,orderSn={}", title, order.getOrderSn());
    }

    /**
     * 未有明确查询结果存疑处理
     *
     * @param message 备注
     * @param order 订单信息
     * @param count 查证最大次数
     */
    public void handleUnKnowVerify(String title, String message, VirtualOrdersWorkerVo paramVo,
            VirtualOrders order, Integer count) {
        log.info("=====>{}-充值状态未知 处理开始,orderSn={} msg={}", title, order.getOrderSn(),
                message);
        title += "存疑";
        try {
            if (paramVo.getGetRetryCount() + 1 == count) {
                // 存疑处理
                // 1.更新worker状态
                VirtualOrdersWorker worker = new VirtualOrdersWorker();
                worker.setId(paramVo.getId());
                worker.setOrderRechargeStatus(OrderRechargeStatusEnum.RECHARGE_FAIL.getCode());
                worker.setOrderRechargeTime(new Date());
                worker.setGetRetryCount(count);
                worker.setRemark(title + ":" + message);
                virtualOrdersWorkerDao.updateVirtualOrdersWorkerById(worker);

                // 2.更新订单状态
                VirtualOrders virtualOrders = new VirtualOrders();
                virtualOrders.setId(order.getId());
                virtualOrders.setOrderStatus(OrderStatusEnum.STATUS_RECHARGE_FAIL.getCode());
                virtualOrders.setRemark(OrderStatusEnum.STATUS_RECHARGE_FAIL.getDesc());
                virtualOrders.setRechargeStatus(RechargeStatusEnum.RECHARGE_FAIL.getCode());
                virtualOrdersDao.updateVirtualOrdersById(virtualOrders);

                // 是否0元订单
                boolean zeroOrder = order.getOrderMoney().compareTo(BigDecimal.ZERO) == 0;
                String remark = zeroOrder ? "查证次数已达上限,默认失败,0元订单不退款"
                        : "查证次数已达上限,默认失败";
                // 3.记录订单轨迹
                ordersTrailModel.saveOrdersTrail(order.getOrderSn(),
                        OrderStatusEnum.STATUS_ORDER_SUCCESS.getCode(),
                        OrderStatusEnum.STATUS_RECHARGE_UNKNOWN.getCode(), remark);

                // 4.保存存疑订单
                VirtualOrdersDoubt virtualOrdersDoubt = new VirtualOrdersDoubt();
                virtualOrdersDoubt.setUserId(order.getUserId());
                virtualOrdersDoubt.setChannelId(order.getChannelId());
                virtualOrdersDoubt.setOrderSn(order.getOrderSn());
                virtualOrdersDoubt.setReason(title + ":" + message);
                virtualOrdersDoubt.setCreateTime(new Date());
                virtualOrdersDoubtDao.saveVirtualOrdersDoubt(virtualOrdersDoubt);

                // 5.存疑订单退款 0元订单不调用订单中心接口
                if (!zeroOrder) {
                    cancelOrder(order, title);
                }
                // 6.推送充值失败mq
                sendRechargeResultMq(order, MqResultCodeEnum.FAIL);
                // 7.飞书告警提示存疑订单
                String msg = title + ",订单:" + order.getOrderSn() + ",详情:" + message;
                robotUtil.pushMsg(msg);
            } else {
                VirtualOrdersWorker worker = new VirtualOrdersWorker();
                worker.setId(paramVo.getId());
                worker.setGetRetryCount(paramVo.getGetRetryCount() + 1);
                worker.setRemark("查证还未有明确结果,等待下次调度重试:" + message);
                virtualOrdersWorkerDao.updateVirtualOrdersWorkerById(worker);
                log.info("=====>{} 查证充值状态还未有明确结果 {},订单:{},第{}次查证重试", title,
                        message, order.getOrderSn(), worker.getGetRetryCount());
            }
        } catch (Exception e) {
            LogUtil.printLog(e, title + "处理异常");
        }
        log.info("=====>{}-充值状态未知 处理结束,orderSn={}", title, order.getOrderSn());
    }

    /**
     * 卡密解析失败处理
     *
     * @param card 解析卡密记录
     * @param title 标题
     * @param msg 错误内容
     */
    public void handleFailDecode(VirtualOrdersCard card, String title, String msg) {
        log.info("=====>调用{}-卡密解析失败 处理开始,orderSn={}", title, card.getOrderSn());
        try {
            // 更新card表状态和重试次数
            virtualOrdersCardDao.updateVirtualOrdersCardFailById(card.getId(),
                    VirtualCardStateEnum.DECODE_FAIL.getCode());
            log.info("=====>{} 失败,订单:{},第{}次重试 错误信息{}", title, card.getOrderSn(),
                    card.getRetryNum(), msg);
            // 达到最大重试次数,飞书报警
            if (card.getRetryNum() + 1 == MAX_RETRY_DECODE_TIMES) {
                // 飞书告警提示卡密解析失败订单
                String message = title + ",订单:" + card.getOrderSn() + ",详情:卡密解析失败 " + msg;
                robotUtil.pushMsg(message);
            }
        } catch (Exception e) {
            LogUtil.printLog(e, title + "处理异常");
        }
        log.info("=====>调用{}-卡密解析失败 处理结束,orderSn={}", title, card.getOrderSn());
    }

    /**
     * 卡密解析成功
     *
     * @param card 卡信息
     */
    public void handleSuccessDecode(String title, VirtualOrdersCard card) {
        log.info("=====>调用{}-卡密解析成功 处理开始,orderSn={}", title, card.getOrderSn());
        try {
            // 更新card表
            virtualOrdersCardDao.updateVirtualOrdersCardById(card);
        } catch (Exception e) {
            LogUtil.printLog(e, title + "处理异常");
        }
        log.info("=====>调用{}-卡密解析成功 处理结束,orderSn={}", title, card.getOrderSn());
    }

    /**
     * 发送充值结果mq
     *
     * @param mqResultCodeEnum 1=充值成功 2=充值失败 3=存疑
     */
    public void sendRechargeResultMq(VirtualOrders order, MqResultCodeEnum mqResultCodeEnum) {
        Map<String, Object> map = new HashMap<>(6);
        map.put("userId", order.getUserId());
        map.put("channelId", order.getChannelId());
        // 充值成功1 失败2
        map.put("resultCode", mqResultCodeEnum.getCode());
        map.put("plusOrderSn", order.getPlusOrderSn());
        map.put("orderSn", order.getOrderSn());
        mqProducer.sendRechargeResult(JSONObject.toJSONString(map));
    }

    /**
     * 订单退款
     *
     * @param order 订单
     * @param msg 消息内容
     */
    public void cancelOrder(VirtualOrders order, String msg) {
        boolean isNew = switchUtil.isNew(order.getUserId());
        String orderSn = order.getOrderSn();
        if (isNew) {
            // 调订单中心原路退
            originalRefund(order, msg, orderSn);
        } else {
            Boolean cancelOrder = orderManager.cancelOrderForCusys(orderSn, msg, "system");
            if (Objects.isNull(cancelOrder) || !cancelOrder) {
                // 用户订单退款失败飞书告警,不回滚状态
                robotUtil.pushMsg(msg + "-给用户订单退款失败,订单:" + orderSn);
            }
        }
    }

    /**
     * 调订单中心原路退
     */
    private void originalRefund(VirtualOrders order, String msg, String orderSn) {
        try {
            String serialNo = SerialNoUtils.generateApplySerialNo(Constant.SERIAL_NO_PREFIX_YLT,
                    orderSn);
            // 保存虚拟权益退款信息
            Long id = saveVirtualOrderRefundInfo(orderSn, serialNo);
            // 组装订单中心退款信息参数
            BigDecimal refundAmount = order.getOrderMoney();
            OrderCancelRefundVO vo = buildParam(msg, orderSn, serialNo, refundAmount, order.getChannelId());
            // 请求订单中心退款
            OrderCancelRefundResultVO result = orderManager.closeOrderRefund(vo);
            if (result == null) {
                robotUtil.pushBusMsg("权益订单原路退款,调订单中心取消订单异常");
                return;
            }
            // 调订单中心取消同步返回失败,修改订单退款信息状态
            if (RefundStateEnum.F.getCode().equals(result.getStatus())) {
                virtualOrderRefundInfoDao.updateRefundState(serialNo,
                        RefundInfoStateEnum.FAIL.getCode());
            }
            // 更新支付退款流水号,调订单可能不会同步返回支付侧流水号
            String paySerialNo = result.getSerialNumber();
            if (StringUtils.isNotBlank(paySerialNo)) {
                virtualOrderRefundInfoDao.updatePaySerialNoById(id, result.getSerialNumber());
            }
        } catch (Exception e) {
            log.info("虚拟权益订单退款失败:", e);
            robotUtil.pushMsg("虚拟权益订单退款失败,权益订单号:" + orderSn);
        }
    }

    /**
     * 组装订单中心退款信息参数
     */
    private OrderCancelRefundVO buildParam(String msg, String orderSn, String serialNo,
            BigDecimal refundAmount, Integer channelId) {
        OrderCancelRefundVO vo = new OrderCancelRefundVO();
        vo.setOrderSn(orderSn);
        vo.setOperatingId(OPERATING_ID);
        vo.setOperatingName(OPERATING_NAME);
        vo.setCancelReason(msg);
        RefundInfo refundInfo = new RefundInfo();
        refundInfo.setThirdPayNum(serialNo);
        refundInfo.setRefundType(0);
        refundInfo.setPartRefund(0);
        refundInfo.setSource(Constant.PAY_SOURCE_VIRTUAL);
        refundInfo.setRefundAmount(refundAmount);
        refundInfo.setApplication(String.valueOf(channelId));
        refundInfo.setPayProductCode(PayProductCodeEnum.TK.getCode());
        // 查询业务场景,businessScene,查桔子
//        String result = redisUtils.get(SUPPLIER_CONFIG + JUZI_SUPPLIER_CONFIG_KEY);
//        PlusShuntSupplierBo shuntSupplierBo = JSONObject.parseObject(result,
//                PlusShuntSupplierBo.class);
//        if (shuntSupplierBo == null || shuntSupplierBo.getPay() == null) {
//            throw new VirtualException("查询分流主体缓存配置异常");
//        }
        String businessScene = getPayBusinessScene(orderSn);
        if (StringUtils.isBlank(businessScene)) {
            throw new VirtualException("查询分流主体businessScene异常");
        }
        refundInfo.setBusinessScene(businessScene);
        vo.setRefundInfo(refundInfo);
        return vo;
    }

    private String getPayBusinessScene(String orderSn) {
        List<VirtualOrderPayFlow> virtualOrderPayFlows = virtualOrderPayFlowDao.listByOrderSnAndPayState(
                orderSn, PayStateEnum.PAY_SUCCESS.getCode());
        if (CollectionUtils.isNotEmpty(virtualOrderPayFlows)) {
            return virtualOrderPayFlows.get(0).getBusinessScene();
        }
        return null;
    }

    /**
     * 保存虚拟权益订单退款信息
     */
    public Long saveVirtualOrderRefundInfo(String orderSn, String refundSerialNo) {
        VirtualOrderRefundInfo refundInfo = new VirtualOrderRefundInfo();
        refundInfo.setOrderSn(orderSn);
        refundInfo.setRefundSerialNo(refundSerialNo);
        refundInfo.setRefundState(RefundInfoStateEnum.DOING.getCode());
        refundInfo.setOptUserId(OPERATING_ID);
        refundInfo.setOptUserName(OPERATING_NAME);
        virtualOrderRefundInfoDao.save(refundInfo);
        return refundInfo.getId();
    }

    /**
     * 给业务发送飞书告警
     *
     * @param msg 消息内容
     */
    public void pushBusMsg(String msg) {
        robotUtil.pushBusMsg(msg);
    }

    /**
     * 异步发送短信
     */
    public void asyncSendMsg(String[] content, String mobileDes, String signCode,
            String templateCode) {
        try {
            SmsMsgV2Dto smsMsgV2Dto = new SmsMsgV2Dto();
            smsMsgV2Dto.setMobile(mobileDes);
            smsMsgV2Dto.setSignCode(signCode);
            smsMsgV2Dto.setTemplateCode(templateCode);
            smsMsgV2Dto.setContent(content);
            mqProducer.sendPhoneMsg(smsMsgV2Dto);
        } catch (Exception e) {
            log.info("发送异步短信异常 ", e);
        }
    }
}
