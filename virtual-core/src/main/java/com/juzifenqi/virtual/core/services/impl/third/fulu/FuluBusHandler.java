package com.juzifenqi.virtual.core.services.impl.third.fulu;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.groot.utils.exception.LogUtil;
import com.juzifenqi.virtual.api.admin.VirtualGoodsApi;
import com.juzifenqi.virtual.bean.pojo.VirtualGoods;
import com.juzifenqi.virtual.bean.pojo.VirtualOrders;
import com.juzifenqi.virtual.bean.pojo.VirtualOrdersCallback;
import com.juzifenqi.virtual.bean.pojo.VirtualOrdersCard;
import com.juzifenqi.virtual.bean.pojo.VirtualOrdersWorker;
import com.juzifenqi.virtual.bean.pojo.VirtualThirdNotify;
import com.juzifenqi.virtual.bean.pojo.third.fulu.request.FuluOrderRefundNotice;
import com.juzifenqi.virtual.bean.pojo.third.fulu.request.FuluRechargeStatusNotice;
import com.juzifenqi.virtual.bean.pojo.third.fulu.request.ProductChangeNotice;
import com.juzifenqi.virtual.bean.system.Response;
import com.juzifenqi.virtual.bean.system.VirtualResult;
import com.juzifenqi.virtual.bean.vo.CardDecodeRespVo;
import com.juzifenqi.virtual.bean.vo.RechargeRespVo;
import com.juzifenqi.virtual.bean.vo.VirtualOrderRechargeSuccVo;
import com.juzifenqi.virtual.bean.vo.VirtualOrdersCardVo;
import com.juzifenqi.virtual.bean.vo.VirtualOrdersWorkerVo;
import com.juzifenqi.virtual.component.enums.DateEnum;
import com.juzifenqi.virtual.component.enums.OrderRechargeStatusEnum;
import com.juzifenqi.virtual.component.enums.OrderStatusEnum;
import com.juzifenqi.virtual.component.enums.PayStateEnum;
import com.juzifenqi.virtual.component.enums.RechargeStatusEnum;
import com.juzifenqi.virtual.component.enums.RechargeTypeEnum;
import com.juzifenqi.virtual.component.enums.SupplierEnum;
import com.juzifenqi.virtual.component.enums.ThirdNotifyTypeEnum;
import com.juzifenqi.virtual.component.enums.VirtualCardStateEnum;
import com.juzifenqi.virtual.component.models.VirtualOrdersTrailModel;
import com.juzifenqi.virtual.component.util.DES3;
import com.juzifenqi.virtual.component.util.DateUtils;
import com.juzifenqi.virtual.component.util.RobotUtil;
import com.juzifenqi.virtual.core.services.impl.third.AbstractStrategyHandler;
import com.juzifenqi.virtual.core.services.impl.third.common.CommonBusHandler;
import com.juzifenqi.virtual.core.services.impl.third.common.MqResultCodeEnum;
import com.juzifenqi.virtual.core.services.impl.third.fulu.model.OrderReq;
import com.juzifenqi.virtual.dao.VirtualGoodsDao;
import com.juzifenqi.virtual.dao.VirtualOrdersCallbackDao;
import com.juzifenqi.virtual.dao.VirtualOrdersDao;
import com.juzifenqi.virtual.dao.VirtualOrdersWorkerDao;
import com.juzifenqi.virtual.dao.VirtualThirdNotifyDao;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;


/**
 * 福禄三方业务处理类
 *
 * <AUTHOR>
 * @date 2023-11-08 13:43:23
 */
@Component
@Slf4j
public class FuluBusHandler extends AbstractStrategyHandler {

    @Autowired
    private CommonBusHandler         commonBusHandler;
    @Autowired
    private VirtualOrdersWorkerDao   virtualOrdersWorkerDao;
    @Autowired
    private VirtualOrdersCallbackDao virtualOrdersCallbackDao;
    @Autowired
    private VirtualOrdersDao         virtualOrdersDao;
    @Autowired
    private VirtualGoodsDao          virtualGoodsDao;
    @Autowired
    private FuluConfig               config;
    @Autowired
    private VirtualGoodsApi          virtualGoodsApi;
    @Autowired
    private VirtualThirdNotifyDao    virtualThirdNotifyDao;
    @Autowired
    private RobotUtil                robotUtil;
    @Autowired
    private VirtualOrdersTrailModel  ordersTrailModel;
    private FuluUtil                 fuluUtil;

    private final String channel = SupplierEnum.SUPPLIER_FULU.getName();

    /**
     * RechargeStatus : success：成功
     */
    private static final String RECHARGE_STATUS_SUC = "success";

    /**
     * RechargeStatus : failed：失败
     */
    private static final String RECHARGE_STATUS_FAIL = "failed";

    /**
     * 下架
     */
    private static final String OFFLINE = "下架";

    /**
     * 上架
     */
    private static final String ONLINE = "上架";

    /**
     * 库存充足
     */
    private static final String STOCK_FULL = "充足";

    /**
     * 库存告警
     */
    private static final String STOCK_WARN = "警报";

    /**
     * 库存缺货
     */
    private static final String STOCK_NO = "断货";

    /**
     * 最大查证次数
     */
    public static final Integer MAX_RETRY_QUERY_TIMES = 10;

    @PostConstruct
    public void intMethod() {
        log.info("初始化{}配置类：url:{},appKey:{},appSecret:{}", channel, config.getUrl(),
                config.getAppKey(), config.getAppSecret());
        //创建福禄调用器
        fuluUtil = new FuluUtil(config.getUrl(), config.getAppKey(), config.getAppSecret());
    }

    /**
     * 福禄回调通知成功响应
     */
    public static final String CALLBACK_RSP_SUCCESS = "success";

    /**
     * 福禄回调通知失败响应
     */
    public static final String CALLBACK_RSP_FAIL = "fail";

    /**
     * 福禄-虚拟权益下单（调用三方充值）
     */
    @Override
    public RechargeRespVo orderRecharge(VirtualOrdersWorkerVo paramVo) {
        long start = System.currentTimeMillis();
        VirtualOrders order = paramVo.getVirtualOrder();
        RechargeTypeEnum rechargeTypeEnum = RechargeTypeEnum.getType(order.getRechargeType());
        if (rechargeTypeEnum == null) {
            log.info("{}下单,充值方式枚举为空 订单号：{}", channel, order.getOrderSn());
            return null;
        }
        String title = channel + rechargeTypeEnum.getName();
        log.info("=====>{}-调用三方充值开始 orderSn:{} param:{}", title, paramVo.getOrderSn(),
                JSON.toJSONString(paramVo));
        /**
         * 福禄下单流程
         * 直充-直充下单
         * 卡密-校验库存-卡密下单
         */
        String supplierOrderSn = null;//福禄订单号
        try {
            OrderReq submitReq = new OrderReq();
            submitReq.setProductId(order.getSupplierItemId());
            submitReq.setBuyNum(1);
            submitReq.setCustomerOrderNo(order.getOrderSn());
            submitReq.setRechargeType(order.getRechargeType());
            // 2.下单
            switch (rechargeTypeEnum) {
                case CARD:
                    // 校验库存
                    OrderReq stockReq = new OrderReq();
                    stockReq.setProductId(order.getSupplierItemId());
                    stockReq.setBuyNum(1);
                    Response<JSONObject> stockResp = fuluUtil.stockCheck(stockReq);
                    if (!stockResp.isOk()) {
                        commonBusHandler.handleSubmitOrderFail(paramVo.getId(), order, title,
                                stockResp.getMsg());
                        return null;
                    }
                    // 库存判断
                    JSONObject stockRespData = stockResp.getData();
                    if (stockRespData == null) {
                        commonBusHandler.handleSubmitOrderFail(paramVo.getId(), order, title,
                                stockResp.getMsg());
                        return null;
                    }
                    boolean enough =
                            stockReq.getProductId().equals(stockRespData.getString("product_id"))
                                    && STOCK_FULL.equals(stockRespData.getString("stock_status"));
                    if (!enough) {
                        // 库存不足
                        commonBusHandler.handleSubmitOrderFail(paramVo.getId(), order, title,
                                stockResp.getMsg());
                        return null;
                    }
                    break;
                case DIRECT:
                    // 直充账号
                    submitReq.setChargeAccount(order.getRechargeAccount());
                    break;
                default:
                    log.info("{}下单,不支持的充值类型 订单号：{}", title, order.getOrderSn());
                    return null;
            }
            Response<JSONObject> submitResp = fuluUtil.createOrder(submitReq);
            if (!submitResp.isOk()) {
                // 下单失败
                commonBusHandler.handleSubmitOrderFail(paramVo.getId(), order, title, submitResp.getMsg());
                return null;
            }
            // 下单成功记录下福禄三方订单号
            supplierOrderSn = submitResp.getData().getString("order_id");
            commonBusHandler.handleSubmitOrderSuc(paramVo.getId(), order, title, supplierOrderSn);
        } catch (Exception e) {
            // 接口请求异常,一般是超时 SocketTimeoutException
            LogUtil.printLog("=====>{}发生异常,订单:{},详情:{}", title, order.getOrderSn(), e);
            commonBusHandler.handleSubmitOrderSuc(paramVo.getId(), order, title, supplierOrderSn);
            robotUtil.pushMsg(title + "下单异常,视为成功处理,请知晓！");
        }
        long end = System.currentTimeMillis();
        log.info("=====>处理一笔{} 订单耗时: {} ms", title, end - start);
        return null;
    }

    /**
     * 充值结果查证
     */
    @Override
    public RechargeRespVo getRechargeStatus(VirtualOrdersWorkerVo paramVo) {
        String title = channel + "主动充值查证";
        log.info("=====>{}-调用三方开始 orderSn:{} param:{}", title, paramVo.getOrderSn(),
                JSON.toJSONString(paramVo));
        long start = System.currentTimeMillis();
        VirtualOrders order = paramVo.getVirtualOrder();
        String orderSn = order.getOrderSn();
        try {
            OrderReq queryReq = new OrderReq();
            queryReq.setCustomerOrderNo(order.getOrderSn());
            Response<JSONObject> queryResp = fuluUtil.queryOrder(queryReq);
            if (!queryResp.isOk()) {
                // 查证失败
                log.info("=====>调用{}失败-接口失败,orderSn={}", title, orderSn);
                // 订单状态-未知
                commonBusHandler.handleUnKnowVerify(title, queryResp.getMsg(), paramVo, order,
                        MAX_RETRY_QUERY_TIMES);
                return null;
            }
            JSONObject queryRespData = queryResp.getData();
            if (queryRespData == null) {
                log.info("=====>调用{}失败-返回为空,orderSn={}", title, orderSn);
                // 订单状态-未知
                commonBusHandler
                        .handleUnKnowVerify(title, "查证返回为空", paramVo, order, MAX_RETRY_QUERY_TIMES);
                return null;
            }
            log.info("=====>调用{}接口:返回结果,result={}", title, JSON.toJSONString(queryResp.getData()));
            // 订单状态
            String status = queryRespData.getString("order_state");
            if (StringUtils.isEmpty(status)) {
                log.info("=====>调用{}失败-查证订单状态为空,orderSn={}", title, orderSn);
                // 查证状态-未知
                commonBusHandler.handleUnKnowVerify(title, "查证订单状态为空", paramVo, order,
                        MAX_RETRY_QUERY_TIMES);
                return null;
            }
            // success：成功，processing：处理中，failed：失败，untreated：未处理
            if (RECHARGE_STATUS_SUC.equals(status)) {
                log.info("=====>调用{}成功-充值成功,orderSn={}", title, orderSn);
                // 如果是卡密订单，则直接解析卡密信息
                VirtualOrderRechargeSuccVo succVo = null;
                if (order.getRechargeType() == RechargeTypeEnum.CARD.getCode()) {
                    succVo = new VirtualOrderRechargeSuccVo(analysisCard(null, queryRespData));
                }
                commonBusHandler.handleSucVerify(paramVo.getId(), order, title, succVo, false);
                return null;
            }
            if (RECHARGE_STATUS_FAIL.equals(status)) {
                log.info("=====>调用{}失败-充值失败,orderSn={}", title, orderSn);
                commonBusHandler.handleFailVerify(title, "业务失败", paramVo.getId(), order, false);
                return null;
            }
            log.info("=====>调用{}订单状态为,status={}", title, status);
            commonBusHandler.handleUnKnowVerify(title, "查证订单状态为:" + status, paramVo, order,
                    MAX_RETRY_QUERY_TIMES);
        } catch (Exception e) {
            LogUtil.printLog("=====>{}发生异常,订单:{}", title, order.getOrderSn(), e);
        }
        long end = System.currentTimeMillis();
        log.info("=====>{}-处理一笔订单耗时: {} ms", title, end - start);
        return null;
    }

    /**
     * 解析卡密
     */
    @Override
    public CardDecodeRespVo decodeCard(VirtualOrdersCardVo paramVo) {
        String title = channel + "解析卡密";
        log.info("=====>{}-调用三方开始 orderSn:{} param:{}", title,
                paramVo.getVirtualOrdersCard().getOrderSn(), JSON.toJSONString(paramVo));
        long start = System.currentTimeMillis();
        VirtualOrdersCard card = paramVo.getVirtualOrdersCard();
        String orderSn = card.getOrderSn();
        try {
            OrderReq queryReq = new OrderReq();
            queryReq.setCustomerOrderNo(card.getOrderSn());
            Response<JSONObject> queryResp = fuluUtil.queryOrder(queryReq);
            if (!queryResp.isOk()) {
                // 查证失败
                commonBusHandler.handleFailDecode(card, title, queryResp.getMsg());
                return null;
            }
            JSONObject queryRespData = queryResp.getData();
            if (queryRespData == null) {
                log.info("=====>调用{}失败-返回为空,orderSn={}", title, orderSn);
                // 查证状态-未知
                commonBusHandler.handleFailDecode(card, title, "接口返回为空");
                return null;
            }
            log.info("=====>调用{}接口:返回结果,result={}", title, JSON.toJSONString(queryResp.getData()));
            // 订单状态
            String status = queryRespData.getString("order_state");
            if (StringUtils.isEmpty(status)) {
                log.info("=====>调用{}失败-查证订单状态为空,orderSn={}", title, orderSn);
                // 查证状态-未知
                commonBusHandler.handleFailDecode(card, title, "查证订单状态为空");
                return null;
            }
            // success：成功，processing：处理中，failed：失败，untreated：未处理
            if (!RECHARGE_STATUS_SUC.equals(status)) {
                log.info("=====>调用{}失败-查证订单状态错误,orderSn={}", title, orderSn);
                commonBusHandler.handleFailDecode(card, title, "查证订单状态错误,错误状态为" + status);
                return null;
            }
            // 解析卡密信息
            VirtualOrdersCard updateCard = analysisCard(card.getId(), queryRespData);
            log.info("=====>调用{}成功,orderSn={}", title, orderSn);
            commonBusHandler.handleSuccessDecode(title, updateCard);
        } catch (Exception e) {
            LogUtil.printLog("=====>{}发生异常,订单:{}", title, card.getOrderSn(), e);
        }
        long end = System.currentTimeMillis();
        log.info("=====>{}-处理一笔订单耗时: {} ms", title, end - start);
        return null;
    }

    /**
     * 解析卡密信息
     */
    private VirtualOrdersCard analysisCard(Integer cardId, JSONObject cardObj) {
        // 解析卡密信息
        VirtualOrdersCard card = new VirtualOrdersCard();
        card.setId(cardId);
        card.setCardState(VirtualCardStateEnum.DECODE_SUCCESS.getCode());
        // 承运商流水号
        card.setOperatorSerialNumber(cardObj.getString("operator_serial_number"));
        JSONArray cards = cardObj.getJSONArray("cards");
        if (!cards.isEmpty()) {
            // 如果返回多张,解析第一张,目前一次只会存在一张卡
            JSONObject cardResp = cards.getJSONObject(0);
            // 类型 接口返回结果 0.普通卡密 1.二维码 2.短链
            // 数据库类型 1_普通卡密 2_二维码 3_短链
            card.setCardType(cardResp.getIntValue("card_type") + 1);
            // 卡密有效期 格式2019-06-30 11:15:32
            if (StringUtils.isNotEmpty(cardResp.getString("card_deadline"))) {
                card.setCardDeadline(DateUtils.getDate(cardResp.getString("card_deadline"), DateEnum.DATE_FORMAT));
            }
            // 卡号 先解密再加密
            String cardNoAes = cardResp.getString("card_number");
            if (StringUtils.isNotEmpty(cardNoAes)) {
                // AES解密
                String cardNo = FuluAESEncrypt.Aes256Decode(cardNoAes, config.getAppSecret().getBytes());
                // 桔子加密
                card.setCardNo(DES3.encrypt(cardNo));
            }
            // 密码 先解密再加密
            String cardPwdAes = cardResp.getString("card_pwd");
            if (StringUtils.isNotEmpty(cardPwdAes)) {
                // AES解密
                String cardPwd = FuluAESEncrypt.Aes256Decode(cardPwdAes, config.getAppSecret().getBytes());
                // 数据库类型 1_普通卡密 2_二维码 3_短链
                // 桔子加密
                if (card.getCardType() == 2) {
                    // 二维码
                    card.setCardQrcode(DES3.encrypt(cardPwd));
                } else if (card.getCardType() == 3) {
                    // 短链
                    card.setCardLink(DES3.encrypt(cardPwd));
                } else {
                    // 普通卡密
                    card.setCardPwd(DES3.encrypt(cardPwd));
                }
            }
        }
        return card;
    }

    /**
     * 福禄 充值回调处理
     */
    public String rechargeStatusNotice(JSONObject jsonObject) {
        String title = channel + "充值回调";
        if (jsonObject == null) {
            log.info("=====>{}失败-回调对象为空", title);
            return CALLBACK_RSP_FAIL;
        }
        log.info("=====>{}开始:{},日期:{}", title, jsonObject.toJSONString(),
                DateUtils.dateToStr(new Date(), DateEnum.DATE_FORMAT));
        try {
            // 1.回调数据验签 用jsonObject验签
            // 先验签，因为null也参与了签名，不能直接转对象，否则回调增加参数了会有问题
            if (!fuluUtil.verifySign(jsonObject, jsonObject.getString("sign"))) {
                // 验签失败
                log.info("=====>{}失败-验签失败", title);
                robotUtil.pushMsg("福禄充值结果回调验签失败，单号：" + jsonObject.getString("customer_order_no"));
                return CALLBACK_RSP_FAIL;
            }
            // 2.解析业务对象
            FuluRechargeStatusNotice statusNotice = jsonObject.toJavaObject(FuluRechargeStatusNotice.class);
            if (statusNotice == null) {
                log.info("=====>{}失败-对象转换失败", title);
                return CALLBACK_RSP_FAIL;
            }
            if (StringUtils.isEmpty(statusNotice.getOrder_status()) || StringUtils.isEmpty(statusNotice.getCustomer_order_no()) || StringUtils
                    .isEmpty(statusNotice.getOrder_id())) {
                log.info("=====>{}失败-请求参数不合法,必填字段", title);
                return CALLBACK_RSP_FAIL;
            }
            String orderSn = statusNotice.getCustomer_order_no();
            // 3.查询Worker信息
            VirtualOrdersWorker ordersWorker = virtualOrdersWorkerDao.getVirtualOrdersWorkerByOrderSn(orderSn);
            if (ordersWorker == null) {
                log.info("=====>{}失败-订单任务不存在,传参桔子订单号:{}", title, orderSn);
                return CALLBACK_RSP_FAIL;
            }
            // 4.校验Worker状态
            if (ordersWorker.getOrderRechargeStatus() != OrderRechargeStatusEnum.RECHARGE_NO_VERIFY.getCode()) {
                log.info("=====>{}主动查证已经有结果了,不处理回调,订单号:{}", title, orderSn);
                orderCallback(statusNotice, CALLBACK_RSP_SUCCESS, title + " 主动查证已经有结果");
                return CALLBACK_RSP_SUCCESS;
            }
            // 5.处理状态回调
            String dealStatus = dealStatusNotice(statusNotice, ordersWorker.getId());
            orderCallback(statusNotice, dealStatus, title + " 处理成功");
            return CALLBACK_RSP_SUCCESS;
        } catch (Exception e) {
            // 回调处理异常
            LogUtil.printLog("=====>{}发生异常,订单:{},详情:{}", title,
                    jsonObject.getString("customer_order_no"), e);
            return CALLBACK_RSP_FAIL;
        }
    }

    /**
     * 记录充值回调状态
     */
    private void orderCallback(FuluRechargeStatusNotice statusNotice, String response,
            String remark) {
        // 记录回调结果
        VirtualOrdersCallback virtualOrdersCallback = new VirtualOrdersCallback();
        virtualOrdersCallback.setOrderSn(statusNotice.getCustomer_order_no());
        virtualOrdersCallback.setSupplierOrderSn(statusNotice.getOrder_id());
        virtualOrdersCallback.setCallbackOrderStatus(statusNotice.getOrder_status());
        virtualOrdersCallback.setCallbackOrderDesc(statusNotice.getRecharge_description());
        virtualOrdersCallback.setResponse(response);
        virtualOrdersCallback.setRemark(remark);
        virtualOrdersCallbackDao.saveVirtualOrdersCallback(virtualOrdersCallback);
    }

    /**
     * 处理充值回调信息
     */
    private String dealStatusNotice(FuluRechargeStatusNotice statusNotice, Integer workerId) {
        String orderSn = statusNotice.getCustomer_order_no();
        String title = channel + "充值回调通知";
        log.info("{}处理开始,订单号:{}", title, orderSn);
        boolean success = RECHARGE_STATUS_SUC.equals(statusNotice.getOrder_status());
        VirtualOrders order = virtualOrdersDao.getVirtualOrdersByOrderSn(orderSn);
        if (success) {
            // 充值成功
            commonBusHandler.handleSucVerify(workerId, order, title, null, false);
        } else {
            // 充值失败
            commonBusHandler
                    .handleFailVerify(title, statusNotice.getRecharge_description(), workerId, order, false);
        }
        log.info("处理{} {},订单:{}", title, success ? "充值成功" : "充值失败", order.getOrderSn());
        return CALLBACK_RSP_SUCCESS;
    }

    /**
     * 商品信息变更回调处理
     */
    public String productChangeNotice(JSONObject jsonObject) {
        if (jsonObject == null) {
            log.info("福禄商品信息失败-回调对象为空");
            return CALLBACK_RSP_FAIL;
        }
        try {
            String body = JSON.toJSONString(jsonObject, SerializerFeature.WriteMapNullValue);
            log.info("福禄商品信息变更回调入参：{}", body);
            // 2023.12.8 huxf 去掉验签逻辑，存在50.00变成50.0的情况，JAVA自动转换
            // 解析业务对象
            ProductChangeNotice notice = jsonObject.toJavaObject(ProductChangeNotice.class);
            if (notice == null) {
                log.info("福禄商品信息失败-对象转换失败");
                return CALLBACK_RSP_FAIL;
            }
            Integer productId = notice.getProduct_id();
            // 是否桔子配置的商品
            List<VirtualGoods> goods = virtualGoodsDao.getGoodsBySupplierItemId(productId);
            if (CollectionUtils.isEmpty(goods)) {
                log.info("福禄商品信息变更回调，非桔子配置商品：{}", productId);
                return RECHARGE_STATUS_SUC;
            }
            // 记录回调日志
            VirtualThirdNotify notify = new VirtualThirdNotify();
            notify.setSupplierId(SupplierEnum.SUPPLIER_FULU.getCode());
            notify.setSupplierName(SupplierEnum.SUPPLIER_FULU.getName());
            notify.setSupplierProductId(String.valueOf(productId));
            notify.setNotifyBody(body);
            notify.setProductId(StringUtils
                    .join(goods.stream().map(VirtualGoods::getId).collect(Collectors.toList()), ","));
            notify.setNotifyType(
                    notice.getChanged_type() == 1 ? ThirdNotifyTypeEnum.PRODUCT_STATE_CHANGE.getCode()
                            : ThirdNotifyTypeEnum.PRODUCT_PRICE_CHANGE.getCode());
            notify.setNotifyDesc(
                    notice.getChanged_type() == 1 ? ThirdNotifyTypeEnum.PRODUCT_STATE_CHANGE.getName()
                            : ThirdNotifyTypeEnum.PRODUCT_PRICE_CHANGE.getName());
            virtualThirdNotifyDao.saveVirtualThirdNotify(notify);
            // 商品上架需要飞书预警，但是不做上架处理
            if (notice.getChanged_type() == 1 && StringUtils
                    .equals(notice.getProduct_sale_status(), ONLINE)) {
                log.info("福禄商品上架预警：{}", productId);
                for (VirtualGoods good : goods) {
                    robotUtil.pushBusMsg("【" + good.getProductName() + "】【" + good.getProductSku()
                            + "】福禄商品上架通知，请知晓！");
                }
                return RECHARGE_STATUS_SUC;
            }
            // 变更类型 1、商品状态变更 2、商品价格变更 （商品下架或价格变更，下架桔子虚拟商品）
            if ((notice.getChanged_type() == 1 && StringUtils.equals(notice.getProduct_sale_status(), OFFLINE))
                    || notice.getChanged_type() == 2) {
                log.info("福禄商品下架处理开始：{}", productId);
                for (VirtualGoods good : goods) {
                    VirtualResult virtualResult = virtualGoodsApi
                            .updateOnsaleState(good.getId(), 0, "0", "system-fulu");
                    log.info("福禄商品下架处理结果：{}", JSON.toJSONString(virtualResult));
                    if (notice.getChanged_type() == 1) {
                        robotUtil.pushBusMsg(
                                "【" + good.getProductName() + "】【" + good.getProductSku()
                                        + "】福禄商品下架通知，桔子虚拟权益商品下架" + (virtualResult.isSuccess() ? "成功"
                                        : "失败") + "，" + (virtualResult.isSuccess() ? "请知晓！" : "请及时处理！"));
                    } else {
                        robotUtil.pushBusMsg(
                                "【" + good.getProductName() + "】【" + good.getProductSku()
                                        + "】福禄商品价格变更通知，价格变更为：" + notice.getProduct_price() + "，桔子虚拟权益商品下架" + (virtualResult.isSuccess() ? "成功" : "失败")
                                        + "，" + (virtualResult.isSuccess() ? "请知晓！" : "请及时处理！"));
                    }
                }
            }
            return RECHARGE_STATUS_SUC;
        } catch (Exception e) {
            LogUtil.printLog(e, "福禄商品信息变更处理异常");
            robotUtil.pushMsg("福禄商品信息变更处理异常，福禄商品id：" + jsonObject.getString("product_id"));
            return CALLBACK_RSP_FAIL;
        }
    }

    /**
     * 退款成功回调
     */
    public String orderRefundNotice(JSONObject jsonObject) {
        if (jsonObject == null) {
            log.info("福禄退款成功回调失败-回调对象为空");
            return CALLBACK_RSP_FAIL;
        }
        try {
            String body = JSON.toJSONString(jsonObject, SerializerFeature.WriteMapNullValue);
            log.info("福禄退款成功回调入参：{}", body);
            // 2023.12.8 huxf 去掉验签逻辑，存在50.00变成50.0的情况，JAVA自动转换
            // 解析业务对象
            FuluOrderRefundNotice notice = jsonObject.toJavaObject(FuluOrderRefundNotice.class);
            if (notice == null) {
                log.info("福禄退款成功回调失败-对象转换失败");
                return CALLBACK_RSP_FAIL;
            }
            if (!StringUtils.equals("success", notice.getRefund_status())) {
                log.info("福禄退款成功回调,非退款成功状态不处理：{}", notice.getCustomer_order_no());
                return CALLBACK_RSP_SUCCESS;
            }
            // 桔子订单号
            String orderSn = notice.getCustomer_order_no();
            // 是否桔子订单
            VirtualOrders orders = virtualOrdersDao.getByOrderSn(orderSn);
            if (orders == null) {
                log.info("福禄退款成功,非桔子订单：{}", orderSn);
                return RECHARGE_STATUS_SUC;
            }
            // 记录回调日志
            VirtualThirdNotify notify = new VirtualThirdNotify();
            notify.setSupplierId(SupplierEnum.SUPPLIER_FULU.getCode());
            notify.setSupplierName(SupplierEnum.SUPPLIER_FULU.getName());
            notify.setNotifyBody(body);
            notify.setNotifyType(ThirdNotifyTypeEnum.ORDER_REFUND.getCode());
            notify.setNotifyDesc(ThirdNotifyTypeEnum.ORDER_REFUND.getName());
            notify.setOrderSn(orderSn);
            notify.setSupplierOrderSn(notice.getOrder_id());

            // 是否0元订单
            boolean zeroOrder = orders.getOrderMoney().compareTo(BigDecimal.ZERO) == 0;
            // 0元订单不退款
            if (zeroOrder) {
                log.info("福禄退款成功,0元订单不退款处理：{}", orderSn);
                notify.setNotifyDesc(notify.getNotifyDesc() + ",0元订单不退款");
            }
            virtualThirdNotifyDao.saveVirtualThirdNotify(notify);
            // 记录订单轨迹，充值成功 -> 充值失败
            ordersTrailModel.saveOrdersTrail(orderSn, OrderStatusEnum.STATUS_RECHARGE_SUCCESS.getCode(),
                    OrderStatusEnum.STATUS_RECHARGE_FAIL.getCode(), "福禄退款成功回调");

            // 更新任务表和订单表状态：充值失败
            VirtualOrdersWorker worker = new VirtualOrdersWorker();
            worker.setOrderSn(orderSn);
            worker.setOrderRechargeStatus(OrderRechargeStatusEnum.RECHARGE_FAIL.getCode());
            worker.setOrderRechargeTime(new Date());
            worker.setRemark("福禄退款成功,变更状态为充值失败");
            virtualOrdersWorkerDao.updateVirtualOrdersByOrderSn(worker);

            // 2.更新订单状态
            VirtualOrders virtualOrders = new VirtualOrders();
            virtualOrders.setId(orders.getId());
            virtualOrders.setOrderStatus(OrderStatusEnum.STATUS_RECHARGE_FAIL.getCode());
            virtualOrders.setRemark(OrderStatusEnum.STATUS_RECHARGE_FAIL.getDesc());
            virtualOrders.setRechargeStatus(RechargeStatusEnum.RECHARGE_FAIL.getCode());
            if (!zeroOrder) {
                log.info("福禄退款回调订单金额大于0更改支付状态=已退款：{}", orderSn);
                virtualOrders.setPayStatus(PayStateEnum.REFUND.getCode());
            }
            virtualOrdersDao.updateVirtualOrdersById(virtualOrders);
            if (!zeroOrder) {
                log.info("福禄退款回调订单金额大于0开始退款：{}", orderSn);
                // 取消订单
                commonBusHandler.cancelOrder(orders, "福禄退款成功回调");
            }
            // 通知会员系统充值失败
            commonBusHandler.sendRechargeResultMq(orders, MqResultCodeEnum.FAIL);
            return RECHARGE_STATUS_SUC;
        } catch (Exception e) {
            LogUtil.printLog(e, "福禄退款成功回调处理异常");
            robotUtil.pushMsg("福禄退款成功回调处理异常，单号：" + jsonObject.getString("customer_order_no"));
            return CALLBACK_RSP_FAIL;
        }
    }

    /**
     * 生成签名参数-非prd环境可用
     */
    public String createSign(JSONObject param) {
        try {
            if (param == null) {
                return "生成签名参数为空";
            }
            param.put("sign", fuluUtil.signOut(param));
            return param.toJSONString();
        } catch (Exception e) {
            // 回调处理异常
            LogUtil.printLog("生成签名异常", e);
            return "生成签名异常";
        }
    }

    /**
     * 福禄已上架商品库存报警
     */
    public void stockWarn() {
        List<VirtualGoods> onlineProduct = virtualGoodsDao.getOnlineProduct(SupplierEnum.SUPPLIER_FULU.getCode());
        if (CollectionUtils.isEmpty(onlineProduct)) {
            log.info("福禄已上架商品库存报警,上架商品列表为空");
            return;
        }
        String productIds = JSON.toJSONString(onlineProduct.stream().map(VirtualGoods::getProductId)
                .collect(Collectors.toList()));
        log.info("福禄商品库存报警开始,商品id：{}", productIds);
        for (VirtualGoods goods : onlineProduct) {
            String productId = goods.getSupplierItemId();
            try {
                Response<JSONObject> response = fuluUtil.queryProduct(productId);
                if (!response.isOk() || response.getData() == null) {
                    robotUtil.pushMsg("库存报警,调用福禄接口失败,福禄商品id：" + productId + ",原因：" + response.getMsg());
                    continue;
                }
                JSONObject data = response.getData();
                String stockStatus = data.getString("stock_status");
                if (StringUtils.equals(STOCK_NO, stockStatus) || StringUtils
                        .equals(STOCK_WARN, stockStatus)) {
                    robotUtil.pushBusMsg(
                            "【" + goods.getProductName() + "】【" + goods.getProductSku() + "】库存【"
                                    + stockStatus + "】，请及时处理！");
                }
            } catch (Exception e) {
                LogUtil.printLog(e, "福禄商品库存报警异常");
                robotUtil.pushMsg("库存报警处理异常,福禄商品id：" + productId);
            }
        }
    }
}
