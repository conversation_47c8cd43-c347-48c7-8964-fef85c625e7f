package com.juzifenqi.virtual.core.models;

import com.alibaba.fastjson.JSONObject;
import com.juzifenqi.plus.api.IPlusOrderSeparateApi;
import com.juzifenqi.plus.dto.resp.PlusAbyssResult;
import com.juzifenqi.plus.dto.resp.order.PlusShuntSupplierSceneResp;
import com.juzifenqi.virtual.bean.entity.shunt.PlusShuntSupplierEntity;
import com.juzifenqi.virtual.bean.pojo.VirtualOrderPayFlow;
import com.juzifenqi.virtual.bean.pojo.VirtualOrders;
import com.juzifenqi.virtual.bean.system.VirtualResult;
import com.juzifenqi.virtual.bean.vo.VirtualOrderPayInfoVo;
import com.juzifenqi.virtual.component.enums.FlowPayStateEnum;
import com.juzifenqi.virtual.component.enums.PayStateEnum;
import com.juzifenqi.virtual.component.exception.VirtualException;
import com.juzifenqi.virtual.component.util.SerialNoUtils;
import com.juzifenqi.virtual.core.conf.ConfigProperties;
import com.juzifenqi.virtual.core.constant.CommonConstant;
import com.juzifenqi.virtual.core.constant.PaySourceConstant;
import com.juzifenqi.virtual.dao.VirtualOrderPayFlowDao;
import com.juzifenqi.virtual.dao.VirtualOrdersDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 虚拟订单
 *
 * @description
 * <AUTHOR>
 * @Date 2024/9/19
 */
@Component
@Slf4j
public class VirtualOrderModel {

    @Autowired
    private VirtualOrdersDao       virtualOrdersDao;
    @Autowired
    private VirtualOrderPayFlowDao virtualOrderPayFlowDao;
    @Autowired
    private OrderShuntModel        orderShuntModel;
    @Autowired
    private ConfigProperties       configProperties;
    @Autowired
    private IPlusOrderSeparateApi iPlusOrderSeparateApi;

    private final Integer SEPARATE_STATE_SUCCESS=2;

    /**
     * 获取支付请求信息
     */
    public VirtualOrderPayInfoVo getOrderPayInfo(String orderSn) {
        log.info("开始生成请求支付信息 orderSn {}", orderSn);
        if (StringUtils.isBlank(orderSn)) {
            throw new VirtualException(VirtualResult.ERROR_CODE, "订单号不能为空");
        }
        VirtualOrders virtualOrders = virtualOrdersDao.getByOrderSn(orderSn);
        if (Objects.isNull(virtualOrders)) {
            log.info("查询的订单不存在 查询单号{}", orderSn);
            throw new VirtualException(VirtualResult.ERROR_CODE, "查询的订单不存在");
        }
        if (!Objects.equals(PayStateEnum.WAIT_PAY.getCode(), virtualOrders.getPayStatus())) {
            log.info("查询的订单非待支付状态 查询单号{} 订单状态{}", orderSn,
                    virtualOrders.getPayStatus());
            throw new VirtualException(VirtualResult.ERROR_CODE, "查询的订单非待支付状态");
        }
        BigDecimal orderMoney = virtualOrders.getOrderMoney();
        if (Objects.nonNull(orderMoney)) {
            log.info("生成请求支付信息订单金额保留两位小数四舍五入：{}", orderSn);
            virtualOrders.setOrderMoney(orderMoney.setScale(2, RoundingMode.HALF_UP));
        }
        // 生成支付请求流水
        VirtualOrderPayFlow orderPayFlow = createPayFlow(virtualOrders);
        virtualOrderPayFlowDao.insert(orderPayFlow);

        // 拼接返回对象
        VirtualOrderPayInfoVo vo = new VirtualOrderPayInfoVo();
        vo.setApplication(String.valueOf(virtualOrders.getChannelId()));
        vo.setBusinessScene(orderPayFlow.getBusinessScene());
        vo.setUserId(String.valueOf(virtualOrders.getUserId()));
        vo.setApplySerialNo(orderPayFlow.getApplySerialNo());
        vo.setTradeType("R");
        vo.setSource(PaySourceConstant.PAY_SOURCE_MEMBER);
        vo.setTotalAmount(orderPayFlow.getOrderAmount());
        vo.setOrderSn(virtualOrders.getOrderSn());
        vo.setTradeName("用户主动支付");
        log.info("结束生成请求支付信息 {}", JSONObject.toJSONString(vo));
        return vo;
    }

    /**
     * 生成支付请求流水
     */
    private VirtualOrderPayFlow createPayFlow(VirtualOrders virtualOrders) {
        Integer shuntSupplyId = null;
        String businessScene = null;
        PlusShuntSupplierSceneResp resp = iPlusOrderSeparateApi.getShuntSupplierIdAndScene(virtualOrders.getPlusOrderSn(), SEPARATE_STATE_SUCCESS).getResult();
        if (Objects.nonNull(resp)) {
            shuntSupplyId = resp.getShuntSupplierId();
            businessScene = resp.getBusinessScene();
        }

        if (Objects.isNull(shuntSupplyId) || Objects.isNull(businessScene)) {
            shuntSupplyId = configProperties.defaultSupplierId;
            PlusShuntSupplierEntity shuntSupplierCache = orderShuntModel.getSupplierCache(
                    shuntSupplyId);
            if (Objects.isNull(shuntSupplierCache) || Objects.isNull(shuntSupplierCache.getPay())
                    || StringUtils.isBlank(shuntSupplierCache.getPay().getBusinessScene())) {
                throw new VirtualException(VirtualResult.ERROR_CODE, "查询分流主体缓存数据异常");
            }
            businessScene = shuntSupplierCache.getPay().getBusinessScene();
        }
        VirtualOrderPayFlow virtualOrderPayFlow = new VirtualOrderPayFlow();
        virtualOrderPayFlow.setOrderSn(virtualOrders.getOrderSn());
        virtualOrderPayFlow.setUserId(virtualOrders.getUserId());
        virtualOrderPayFlow.setApplySerialNo(
                SerialNoUtils.generateApplySerialNo(CommonConstant.SERIAL_NO_PREFIX_XN,
                        virtualOrders.getOrderSn()));
        virtualOrderPayFlow.setShuntSupplierId(shuntSupplyId);
        virtualOrderPayFlow.setOrderAmount(virtualOrders.getOrderMoney());
        virtualOrderPayFlow.setBusinessScene(businessScene);
        virtualOrderPayFlow.setPayState(FlowPayStateEnum.WAIT_PAY.getCode());
        virtualOrderPayFlow.setRemark("");
        return virtualOrderPayFlow;
    }

    /**
     * 更新请求流水状态
     */
    public void updateOrderPayFlow(String applySerialNo, String paySerialNo,
            FlowPayStateEnum payStateEnum) {
        // 返回了请求流水号
        if (StringUtils.isNotBlank(applySerialNo)) {
            VirtualOrderPayFlow payFlow = virtualOrderPayFlowDao.getByApplySerialNo(applySerialNo,
                    FlowPayStateEnum.WAIT_PAY.getCode());
            if (Objects.nonNull(payFlow)) {
                payFlow.setPaySerialNo(paySerialNo);
                payFlow.setPayCallbackTime(new Date());
                payFlow.setPayState(payStateEnum.getCode());
                virtualOrderPayFlowDao.update(payFlow);
            }
        }
    }

    /**
     * 更新请求流水状态-支付超时
     */
    public void updateOrderPayFlowForPayTimeout(String orderSn) {
        List<VirtualOrderPayFlow> payFlowDoList = virtualOrderPayFlowDao.listByOrderSnAndPayState(
                orderSn, FlowPayStateEnum.WAIT_PAY.getCode());
        if (!CollectionUtils.isEmpty(payFlowDoList)) {
            payFlowDoList.forEach(item -> {
                item.setPayCallbackTime(new Date());
                item.setPayState(FlowPayStateEnum.PAY_FAIL.getCode());
                item.setRemark("支付超时闭单");
                virtualOrderPayFlowDao.update(item);
            });
        }
    }

}
