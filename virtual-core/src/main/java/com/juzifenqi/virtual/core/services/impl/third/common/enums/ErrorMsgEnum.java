package com.juzifenqi.virtual.core.services.impl.third.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 福禄 业务错误枚举
 *
 * <AUTHOR>
 * @date 2023-11-08 17:33:23
 */
@Getter
@AllArgsConstructor
public enum ErrorMsgEnum {

    PARAMS_CANNOT_BE_NULL(10000, "请求参数[%s]不能为空"),
    RESP_ERROR(20000, "返回结果错误[%s]"),
    BUS_ERROR(20001, "接口调用失败[%s%s]"),
    STOCK_LESS(20002, "商品库存不足"),
    SYS_ERROR(29999, "系统调用异常"),
    ;

    private final int code;

    private final String message;
}
