package com.juzifenqi.virtual.core.services.impl.admin;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.juzifenqi.plus.api.admin.IPlusProfitsAdminApi;
import com.juzifenqi.plus.dto.req.profits.CreatePlusProgramLmkVirtualReq;
import com.juzifenqi.plus.dto.req.profits.UpdateProfitVirtualReq;
import com.juzifenqi.plus.dto.resp.PlusAbyssResult;
import com.juzifenqi.product.entity.Product;
import com.juzifenqi.product.entity.ProductGoods;
import com.juzifenqi.virtual.api.admin.VirtualGoodsApi;
import com.juzifenqi.virtual.bean.bo.VirtualGoodsBo;
import com.juzifenqi.virtual.bean.pojo.VirtualGoods;
import com.juzifenqi.virtual.bean.pojo.VirtualProductSupplier;
import com.juzifenqi.virtual.bean.pojo.VirtualRechargeType;
import com.juzifenqi.virtual.bean.system.VirtualResult;
import com.juzifenqi.virtual.bean.vo.UpdateOnSaleStateVo;
import com.juzifenqi.virtual.bean.vo.VirtualGoodsVo;
import com.juzifenqi.virtual.component.enums.RechargeTypeEnum;
import com.juzifenqi.virtual.component.enums.SupplierEnum;
import com.juzifenqi.virtual.core.models.VirtualGoodsModel;
import com.juzifenqi.virtual.dao.VirtualGoodsDao;
import com.juzifenqi.virtual.dao.VirtualProductSupplierDao;
import com.juzifenqi.virtual.dao.VirtualRechargeTypeDao;
import com.juzifenqi.virtual.manager.ProductManager;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 权益管理
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/20 9:37 下午
 */
@Service
@Slf4j
public class VirtualGoodsApiImpl implements VirtualGoodsApi {

    @Autowired
    private VirtualGoodsDao           virtualGoodsDao;
    @Autowired
    private VirtualProductSupplierDao virtualProductSupplierDao;
    @Autowired
    private ProductManager            productManager;
    @Autowired
    private VirtualGoodsModel         virtualGoodsModel;
    @Autowired
    private VirtualRechargeTypeDao    virtualRechargeTypeDao;
    @Autowired
    private IPlusProfitsAdminApi      plusProfitsAdminApi;


    @Override
    public VirtualResult<List<VirtualGoodsVo>> queryProfitList(VirtualGoodsBo virtualGoodsBo) {
        log.info("查询权益列表入参:{}", JSONObject.toJSONString(virtualGoodsBo));
        VirtualResult<List<VirtualGoodsVo>> result = new VirtualResult<>();
        int memberPlusProgramCount = virtualGoodsModel.getVirtualGoodsCount(virtualGoodsBo);
        List<VirtualGoodsVo> memberPlusProgramList = virtualGoodsModel.getVirtualGoodsList(
                virtualGoodsBo);
        result.success(memberPlusProgramCount, memberPlusProgramList);
        log.info("权益列表={}", JSON.toJSONString(result));
        return result;
    }

    @Override
    public VirtualResult addProfit(VirtualGoods virtualGoods) {
        VirtualResult result = new VirtualResult();
        if (StringUtils.isBlank(virtualGoods.getProductSku()) || StringUtils.isBlank(
                virtualGoods.getSupplierName()) || virtualGoods.getSupplierId() == null
                || StringUtils.isBlank(virtualGoods.getSupplierItemId())
                || virtualGoods.getSupplierItemPrice() == null || StringUtils.isBlank(
                virtualGoods.getCreateBy()) || virtualGoods.getRechargeType() == null
                || StringUtils.isBlank(virtualGoods.getRechargeTypeName())
                || StringUtils.isBlank(virtualGoods.getImgUrl())) {
            result.error(VirtualResult.ERROR_CODE, "必填参数不能为空");
            return result;
        }
        // 充值方式为直充 判断充值类型
        if (virtualGoods.getRechargeType() == RechargeTypeEnum.DIRECT.getCode() && (
                virtualGoods.getRechargeCode() == null || StringUtils.isBlank(
                        virtualGoods.getRechargeName()))) {
            result.error(VirtualResult.ERROR_CODE, "充值方式为直充，必须选择充值类型");
            return result;
        }
        // 20231130 zjf 橡树酒店券直充，跳转类型和url不能为空
        if (virtualGoods.getSupplierId() == SupplierEnum.SUPPLIER_XS.getCode()
                && virtualGoods.getRechargeType() == RechargeTypeEnum.HOTEL.getCode() && (
                virtualGoods.getSupplierAccessType() == null || StringUtils.isBlank(
                        virtualGoods.getSupplierAccessUrl()))) {
            result.error(VirtualResult.ERROR_CODE, "橡树酒店券直充，必须选择酒店跳转地址");
            return result;
        }
        ProductGoods goodsBySku = productManager.getProductGoodsBySku(virtualGoods.getProductSku());
        if (goodsBySku == null) {
            result.error(VirtualResult.ERROR_CODE, "保存失败，未查询到有效商品SKU");
            return result;
        }
        Product productById = productManager.getProductById(goodsBySku.getProductId());
        if (productById == null) {
            result.error(VirtualResult.ERROR_CODE, "保存失败，未查询到有效的产品");
            return result;
        }
        VirtualGoods virtualGoodsBySku = virtualGoodsDao.getVirtualGoodsBySku(
                virtualGoods.getProductSku());
        if (virtualGoodsBySku != null) {
            result.error(VirtualResult.ERROR_CODE, "保存失败，当前sku已经配置了虚拟商品");
            return result;
        }

        VirtualGoods goods = new VirtualGoods();
        goods.setProductId(productById.getId());
        goods.setProductName(productById.getName1());
        goods.setProductSku(virtualGoods.getProductSku());
        goods.setSupplierId(virtualGoods.getSupplierId());
        goods.setSupplierName(virtualGoods.getSupplierName());
        goods.setSupplierItemId(virtualGoods.getSupplierItemId());
        goods.setSupplierItemPrice(virtualGoods.getSupplierItemPrice());
        goods.setStock(virtualGoods.getStock() == null ? 99999 : virtualGoods.getStock());
        goods.setOnsaleState(0);//默认未上架
        goods.setCreateBy(virtualGoods.getCreateBy());
        goods.setRechargeCode(virtualGoods.getRechargeCode());
        goods.setRechargeName(virtualGoods.getRechargeName());
        goods.setRechargeType(virtualGoods.getRechargeType());
        goods.setRechargeTypeName(virtualGoods.getRechargeTypeName());
        goods.setSupplierAccessUrl(virtualGoods.getSupplierAccessUrl());
        goods.setSupplierAccessType(virtualGoods.getSupplierAccessType());
        goods.setImgUrl(virtualGoods.getImgUrl());
        BigDecimal privatePrice = virtualGoods.getPrivatePrice();
        if (privatePrice == null || privatePrice.compareTo(productById.getMarketPrice()) > 0) {
            //扣减保护价不添,或者大于市场价-默认取值商品的市场价
            privatePrice = productById.getMarketPrice();
        }
        goods.setPrivatePrice(privatePrice);
        Integer ret = virtualGoodsDao.saveVirtualGoods(goods);
        if (ret > 0) {
            result.success("新增成功", null);
            return result;
        }
        result.error(VirtualResult.ERROR_CODE, "新增失败");
        return result;
    }


    @Override
    public VirtualResult modifyProfit(VirtualGoods virtualGoods) {
        VirtualResult result = new VirtualResult();
        if (virtualGoods.getId() == null || StringUtils.isBlank(virtualGoods.getProductSku())
                || StringUtils.isBlank(virtualGoods.getSupplierName())
                || virtualGoods.getSupplierId() == null || StringUtils.isBlank(
                virtualGoods.getSupplierItemId()) || virtualGoods.getSupplierItemPrice() == null
                || StringUtils.isBlank(virtualGoods.getUpdateBy())
                || virtualGoods.getRechargeType() == null || StringUtils.isBlank(
                virtualGoods.getRechargeTypeName())
                || StringUtils.isBlank(virtualGoods.getImgUrl())) {
            result.error(VirtualResult.ERROR_CODE, "必填参数不能为空");
            return result;
        }
        // 充值方式为直充 判断充值类型
        if (virtualGoods.getRechargeType() == RechargeTypeEnum.DIRECT.getCode() && (
                virtualGoods.getRechargeCode() == null || StringUtils.isBlank(
                        virtualGoods.getRechargeName()))) {
            result.error(VirtualResult.ERROR_CODE, "充值方式为直充，必须选择充值类型");
            return result;
        }
        // 20231130 zjf 橡树酒店券直充，跳转类型和url不能为空
        if (virtualGoods.getSupplierId() == SupplierEnum.SUPPLIER_XS.getCode()
                && virtualGoods.getRechargeType() == RechargeTypeEnum.HOTEL.getCode() && (
                virtualGoods.getSupplierAccessType() == null || StringUtils.isBlank(
                        virtualGoods.getSupplierAccessUrl()))) {
            result.error(VirtualResult.ERROR_CODE, "橡树酒店券直充，必须选择酒店跳转地址");
            return result;
        }
        ProductGoods goodsBySku = productManager.getProductGoodsBySku(virtualGoods.getProductSku());
        if (goodsBySku == null) {
            result.error(VirtualResult.ERROR_CODE, "修改失败，未查询到有效商品SKU");
            return result;
        }
        Product productById = productManager.getProductById(goodsBySku.getProductId());
        if (productById == null) {
            result.error(VirtualResult.ERROR_CODE, "修改失败，未查询到有效的产品");
            return result;
        }
        BigDecimal privatePrice = virtualGoods.getPrivatePrice();
        if (privatePrice == null || privatePrice.compareTo(productById.getMarketPrice()) > 0) {
            //扣减保护价不添,或者大于市场价-默认取值商品的市场价
            privatePrice = productById.getMarketPrice();
        }
        virtualGoods.setPrivatePrice(privatePrice);
        Integer ret = virtualGoodsDao.updateVirtualGoodsById(virtualGoods);
        if (ret > 0) {
            result.success("更新成功", null);
            try {
                // 同步营销图片信息，刷新缓存
                UpdateProfitVirtualReq req = new UpdateProfitVirtualReq();
                req.setImgUrl(virtualGoods.getImgUrl());
                req.setProductSku(virtualGoods.getProductSku());
                req.setOptName(virtualGoods.getUpdateBy());
                req.setOptId(virtualGoods.getUpdateByUserId());
                PlusAbyssResult abyssResult = plusProfitsAdminApi.updateProfitVirtualImgUrl(req);
                if (Objects.isNull(abyssResult) || !abyssResult.getSuccess()){
                    result.setResult(Optional.ofNullable(abyssResult).map(PlusAbyssResult::getMessage).orElse("营销图片同步失败"));
                }
            }catch (Exception e){
                log.info("营销图片同步失败，请重试", e);
                result.success("营销图片同步失败", null);
            }
            return result;
        }
        result.error(VirtualResult.ERROR_CODE, "更新失败");
        return result;
    }

    @Override
    public VirtualResult deleteProfit(Integer id) {
        VirtualResult result = new VirtualResult();
        if (id == null) {
            result.error(VirtualResult.ERROR_CODE, "必填参数不能为空");
            return result;
        }

        VirtualGoods oneVirtualGoods = virtualGoodsDao.getVirtualGoodsById(id);
        if (oneVirtualGoods.getOnsaleState() == 1) {
            result.error(VirtualResult.ERROR_CODE, "已上架的权益不能删除");
            return result;
        }

        Integer ret = virtualGoodsDao.deleteVirtualGoodsById(id);
        if (ret > 0) {
            result.success("删除成功", null);
            return result;
        }
        result.error(VirtualResult.ERROR_CODE, "删除失败");
        return result;
    }

    @Override
    public VirtualResult updateOnsaleState(Integer id, Integer onsaleState, String updateById,
            String updateBy) {
        log.info("上下架入参:id-{},onsaleState-{},updateById-{},updateBy-{}", id, onsaleState,
                updateById, updateBy);
        VirtualResult result = new VirtualResult();
        if (id == null || onsaleState == null) {
            result.error(VirtualResult.ERROR_CODE, "必填参数不能为空");
            return result;
        }
        try {
            modifyAndNotice(id, onsaleState, updateById, updateBy);
            result.success("上下架状态修改成功", null);
            return result;
        } catch (Exception e) {
            log.error("系统异常:{}", e.getMessage());
            result.error(VirtualResult.ERROR_CODE, "上下架状态修改失败");
            return result;
        }


    }

    @Transactional(rollbackFor = Exception.class)
    void modifyAndNotice(Integer id, Integer onsaleState, String updateById, String updateBy)
            throws Exception {
        Integer ret = virtualGoodsDao.updateOnsale(id, onsaleState, updateBy);
        if (ret > 0) {
            try {
                //上下架完通知会员状态修改
                VirtualGoods oneVirtualGoods = virtualGoodsDao.getVirtualGoodsById(id);
                Integer optId = Integer.valueOf(updateById);
                // 20230508 zjf 上下架修改权益状态
                CreatePlusProgramLmkVirtualReq lmkVirtual = new CreatePlusProgramLmkVirtualReq();
                lmkVirtual.setVirtualStatus(onsaleState);
                lmkVirtual.setSku(oneVirtualGoods.getProductSku());
                lmkVirtual.setUpdateUserName(updateBy);
                lmkVirtual.setUpdateUserId(optId);
                log.info("修改虚拟权益状态开始：{}", JSONObject.toJSONString(lmkVirtual));
                PlusAbyssResult result = plusProfitsAdminApi.updateVirtualState(lmkVirtual);
                log.info("修改虚拟权益状态结束：{}", JSONObject.toJSONString(result));
            } catch (Exception e) {
                log.error("通知会员异常:" + e);
                throw new Exception("上下架状态修改失败,通知会员失败");
            }
        } else {
            throw new Exception("上下架状态修改失败");
        }
    }

    @Override
    public VirtualResult<UpdateOnSaleStateVo> newUpdateOnSaleState(Integer id, Integer onSaleState,
            String updateById, String updateBy) {
        log.info("新上下架入参:id-{},onSaleState-{},updateById-{},updateBy-{}", id, onSaleState,
                updateById, updateBy);
        VirtualResult<UpdateOnSaleStateVo> result = new VirtualResult<>();
        if (id == null || onSaleState == null) {
            result.error(VirtualResult.ERROR_CODE, "必填参数不能为空");
            return result;
        }
        try {
            UpdateOnSaleStateVo vo = newModifyAndNotice(id, onSaleState, updateBy);
            result.success("上下架状态修改成功", vo);
            return result;
        } catch (Exception e) {
            log.error("新上下架系统异常:{}", e.getMessage());
            result.error(VirtualResult.ERROR_CODE, "上下架状态修改失败");
            return result;
        }
    }

    /**
     * 修改状态
     */
    private UpdateOnSaleStateVo newModifyAndNotice(Integer id, Integer onSaleState, String updateBy)
            throws Exception {
        Integer ret = virtualGoodsDao.updateOnsale(id, onSaleState, updateBy);
        if (ret > 0) {
            // 返回值
            VirtualGoods oneVirtualGoods = virtualGoodsDao.getVirtualGoodsById(id);
            UpdateOnSaleStateVo vo = new UpdateOnSaleStateVo();
            vo.setVirtualStatus(onSaleState);
            vo.setSku(oneVirtualGoods.getProductSku());
            return vo;
        } else {
            throw new Exception("上下架状态修改失败");
        }
    }


    @Override
    public VirtualResult getAllVirtualProductSupplier() {
        List<VirtualProductSupplier> allVirtualProductSupplier = virtualProductSupplierDao.getAllVirtualProductSupplier();
        VirtualResult result = new VirtualResult();
        result.success("查询成功", allVirtualProductSupplier);
        return result;
    }

    @Override
    public VirtualResult<List<VirtualGoods>> getVirtualGoodsBySkus(String skus) {
        List<String> skuList = Arrays.asList(skus.split(","));
        List<VirtualGoods> virtualGoods = virtualGoodsDao.getVirtualGoodsBySkus(skuList);
        VirtualResult<List<VirtualGoods>> result = new VirtualResult<>();
        result.success("查询成功", virtualGoods);
        return result;
    }

    @Override
    public VirtualResult<List<VirtualRechargeType>> getAllVirtualRechargeType() {
        List<VirtualRechargeType> allVirtualRechargeType = virtualRechargeTypeDao.getAllVirtualRechargeType();
        VirtualResult<List<VirtualRechargeType>> result = new VirtualResult<>();
        result.success("查询成功", allVirtualRechargeType);
        return result;
    }
}
