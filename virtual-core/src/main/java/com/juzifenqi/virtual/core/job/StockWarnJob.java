package com.juzifenqi.virtual.core.job;


import com.groot.utils.exception.LogUtil;
import com.juzifenqi.virtual.core.services.impl.third.fulu.FuluBusHandler;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 库存报警
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/13 13:59
 */
@Component
@Slf4j
public class StockWarnJob {

    @Autowired
    private FuluBusHandler fuluBusHandler;

    /**
     * 福禄库存报警job
     */
    @XxlJob("fuluStockWarnJob")
    public ReturnT<String> execute(String param) {
        try {
            fuluBusHandler.stockWarn();
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            LogUtil.printLog(e, "库存报警job异常");
        }
        return ReturnT.FAIL;
    }
}
