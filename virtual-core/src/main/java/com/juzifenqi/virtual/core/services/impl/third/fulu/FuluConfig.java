package com.juzifenqi.virtual.core.services.impl.third.fulu;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 福禄三方交互配置类
 *
 * <AUTHOR>
 * @date 2023-11-08 13:43:23
 */
@Component
@Data
public class FuluConfig {

    /**
     * 接口地址(http/https):"http://pre.openapi.fulu.com/api/getway"
     */
    @Value("${third.fulu.url}")
    private String url;

    /**
     * 开放平台分配给商户的app_key
     */
    @Value("${third.fulu.appKey}")
    private String appKey;

    /**
     * 应用密钥appSecret
     */
    @Value("${third.fulu.appSecret}")
    private String appSecret;
}
