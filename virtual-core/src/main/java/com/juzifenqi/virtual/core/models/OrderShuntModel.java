package com.juzifenqi.virtual.core.models;

import com.alibaba.fastjson.JSONObject;
import com.juzifenqi.virtual.bean.entity.shunt.PlusShuntSupplierEntity;
import com.juzifenqi.virtual.core.constant.RedisConstantPrefix;
import com.juzifenqi.virtual.core.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @description
 * <AUTHOR>
 * @Date 2024/9/21
 */
@Component
@Slf4j
public class OrderShuntModel {

    @Autowired
    private RedisUtils redisUtils;

    /**
     * 从缓存获取分流主体配置信息
     */
    public PlusShuntSupplierEntity getSupplierCache(Integer supplierId) {
        String redisKey = RedisConstantPrefix.SUPPLIER_CONFIG + supplierId;
        String cache = redisUtils.get(redisKey);
        log.info("获取分流/清分主体缓存结果：{}，{}", supplierId, cache);
        if (StringUtils.isBlank(cache)) {
            return null;
        }
        return JSONObject.parseObject(cache, PlusShuntSupplierEntity.class);
    }

}
