package com.juzifenqi.virtual.core.services.impl.third.xiangshu.model;

import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * 券失效返回data对象
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/04 17:36
 */
@Data
public class XiangshuInvalidCouponDataResp implements Serializable {
    private static final long serialVersionUID = 1297864475820298810L;

    /**
     * 优惠券id
     */
    private String coupon_id;

    /**
     * 领取状态 success成功 fail 失败
     */
    private String status;
}
