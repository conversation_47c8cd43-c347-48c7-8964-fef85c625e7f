package com.juzifenqi.virtual.core.job;

import com.juzifenqi.virtual.core.services.impl.third.zixuan.ZixuanUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 查询桔子在子轩余额定时任务
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/20 9:37 下午
 */
@Component
@Slf4j
public class GetZxBalanceJob {

    @Autowired
    private ZixuanUtil zixuanUtil;

    @XxlJob("getZxBalanceJob")
    public ReturnT<String> execute(String param) {
        try {
            log.info("查询桔子在子轩余额任务开始");
            zixuanUtil.getZxBalance();
            log.info("查询桔子在子轩余额任务结束");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("查询桔子在子轩余额任务执行发生异常:{}", e.getMessage());
        }
        return ReturnT.FAIL;
    }

}
