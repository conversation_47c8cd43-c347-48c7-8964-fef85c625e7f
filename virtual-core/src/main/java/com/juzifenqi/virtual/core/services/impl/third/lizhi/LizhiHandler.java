package com.juzifenqi.virtual.core.services.impl.third.lizhi;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.groot.utils.http.OKHttp3SimpleUtils;
import com.juzifenqi.virtual.bean.pojo.VirtualOrders;
import com.juzifenqi.virtual.bean.pojo.VirtualOrdersWorker;
import com.juzifenqi.virtual.bean.vo.RechargeRespVo;
import com.juzifenqi.virtual.bean.vo.VirtualOrdersWorkerVo;
import com.juzifenqi.virtual.component.enums.OrderRechargeStatusEnum;
import com.juzifenqi.virtual.component.enums.OrderStatusEnum;
import com.juzifenqi.virtual.component.enums.OrderSubmitStatusEnum;
import com.juzifenqi.virtual.component.enums.RechargeStatusEnum;
import com.juzifenqi.virtual.component.models.VirtualOrdersTrailModel;
import com.juzifenqi.virtual.core.services.impl.third.AbstractStrategyHandler;
import com.juzifenqi.virtual.dao.VirtualOrdersDao;
import com.juzifenqi.virtual.dao.VirtualOrdersWorkerDao;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.security.MessageDigest;
import java.util.*;

/**
 * 荔枝三方交互工具类
 * 虚拟权益下单和充值结果查证
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class LizhiHandler extends AbstractStrategyHandler {


    @Autowired
    private LizhiConfig config;

    @Autowired
    private VirtualOrdersWorkerDao virtualOrdersWorkerDao;

    @Autowired
    private VirtualOrdersTrailModel ordersTrailModel;

    @Autowired
    private VirtualOrdersDao virtualOrdersDao;

    // 直充下单接口码
    private static final String API_CODE_ORDER = "AP201";
    // 订单查询接口码
    private static final String API_CODE_QUERY = "AP202";
    // 下单前置校验接口码
    private static final String API_CODE_VALIDATE = "AP203";
    // 成功响应码
    private static final String RSP_CODE_SUCCESS = "0000";
    // 充值成功状态
    private static final String STATUS_FINISHED = "FINISHED";
    // 充值失败状态
    private static final String STATUS_CANCEL = "CANCEL";
    // API版本号
    private static final String API_VERSION = "2.0";
    // 签名类型
    private static final String SIGN_TYPE = "MD5";
    // API网关路径
    private static final String API_GATEWAY_PATH = "/api/goods/gateway";
    // 默认下单数量
    private static final String DEFAULT_QUANTITY = "1";

    @Override
    public RechargeRespVo orderRecharge(VirtualOrdersWorkerVo paramVo) {
        log.info("荔枝-调用三方充值：param:{}", JSON.toJSONString(paramVo));
        long start = System.currentTimeMillis();

        // 获取订单信息
        VirtualOrders order = paramVo.getVirtualOrder();
        String orderSn = order.getOrderSn();
        log.info("荔枝下单开始,订单:{}", orderSn);

        RechargeRespVo respVo = new RechargeRespVo();
        boolean success = false;
        String rspMsg = "调用荔枝下单接口失败";
        String supplierOrderSn = null;
        JSONObject resultJson = null;

        try {
            // 调用前置校验接口
            if (!validateBeforeOrder(order)) {
                log.error("荔枝下单前置校验失败，订单:{}", orderSn);
                rspMsg = "下单前置校验失败";
                respVo.setResultContent(rspMsg);
                return handleOrderFailure(paramVo, order, orderSn, rspMsg);
            }

            log.info("荔枝下单前置校验通过，订单:{}", orderSn);

            // 组装参数并调用下单接口
            resultJson = executeOrderRequest(orderSn, order);

            // 解析响应
            if (resultJson != null) {
                String rspCode = resultJson.getString("rspCode");
                rspMsg = resultJson.getString("rspMsg");
                JSONObject data = resultJson.getJSONObject("data");
                supplierOrderSn = data != null ? data.getString("orderId") : null;
                success = RSP_CODE_SUCCESS.equals(rspCode);

                if (!success) {
                    log.warn("荔枝下单返回失败，订单:{}, 错误码:{}, 错误信息:{}", orderSn, rspCode, rspMsg);
                }
            } else {
                log.error("荔枝下单接口返回为空，订单:{}", orderSn);
            }
        } catch (Exception e) {
            log.error("荔枝下单发生异常,订单:{},详情:{}", orderSn, e.getMessage(), e);
            rspMsg = "下单异常: " + e.getMessage();
        } finally {
            // 更新订单状态
            updateOrderStatus(paramVo.getId(), order, orderSn, success, supplierOrderSn, rspMsg);

            long end = System.currentTimeMillis();
            log.info("=====>荔枝下单处理一笔订单耗时: {} ms", end - start);

            respVo.setResultContent(resultJson == null ? null : resultJson.toJSONString());
        }

        return respVo;
    }

    /**
     * 执行下单请求
     */
    private JSONObject executeOrderRequest(String orderSn, VirtualOrders order) {
        // 组装参数
        Map<String, Object> params = buildCommonParams(API_CODE_ORDER);
        params.put("outOrderId", orderSn);
        
        // 解析supplierItemId，格式为spu,sku
        ItemIdPair itemIdPair = parseSupplierItemId(order);
        
        params.put("spuId", itemIdPair.getSpuId());
        params.put("skuId", itemIdPair.getSkuId());
        params.put("chargeAccount", order.getRechargeAccount());

        // 调用接口
        return callLizhiApi("直充下单接口", params);
    }

    /**
     * 处理下单失败情况
     */
    private RechargeRespVo handleOrderFailure(VirtualOrdersWorkerVo paramVo, VirtualOrders order,
                                              String orderSn, String rspMsg) {
        // 更新子表订单状态为失败
        updateWorkerOrderSubmitStatus(paramVo.getId(), false, rspMsg);
        // 更新主表订单状态为失败
        updateMainOrderSubmitStatus(order, false, null);
        // 记录订单流转状态
        recordOrderTrail(orderSn, false, "下单");

        RechargeRespVo respVo = new RechargeRespVo();
        respVo.setResultContent(rspMsg);
        return respVo;
    }

    /**
     * 更新订单状态
     */
    private void updateOrderStatus(Integer workerId, VirtualOrders order, String orderSn,
                                   boolean success, String supplierOrderSn, String rspMsg) {
        // 更新子表订单状态
        updateWorkerOrderSubmitStatus(workerId, success, rspMsg);

        // 更新主表订单状态
        updateMainOrderSubmitStatus(order, success, supplierOrderSn);

        // 记录订单流转状态
        recordOrderTrail(orderSn, success, "下单");

        log.info("荔枝下单{},订单:{}", success ? "成功" : "失败", orderSn);
    }

    @Override
    public RechargeRespVo getRechargeStatus(VirtualOrdersWorkerVo paramVo) {
        log.info("荔枝-充值结果查证：param:{}", JSON.toJSONString(paramVo));
        long start = System.currentTimeMillis();

        // 获取订单信息
        VirtualOrders order = paramVo.getVirtualOrder();
        String orderSn = order.getOrderSn();
        log.info("荔枝充值查证开始,订单:{}", orderSn);

        // 调用查询接口并处理结果
        JSONObject resultJson = queryOrderStatus(orderSn, order);

        // 解析响应结果
        OrderQueryResult queryResult = parseQueryResult(resultJson, orderSn);

        // 更新订单状态
        updateOrderRechargeStatus(paramVo.getId(), order, orderSn, queryResult);

        // 记录处理时间
        long end = System.currentTimeMillis();
        log.info("=====>荔枝充值查证处理一笔订单耗时: {} ms", end - start);

        // 返回结果
        RechargeRespVo respVo = new RechargeRespVo();
        respVo.setResultContent(resultJson == null ? null : resultJson.toJSONString());
        return respVo;
    }

    /**
     * 查询订单状态
     */
    private JSONObject queryOrderStatus(String orderSn, VirtualOrders order) {
        // 组装参数
        Map<String, Object> params = buildCommonParams(API_CODE_QUERY);
        if (StringUtils.isNotEmpty(orderSn)) {
            params.put("outOrderId", orderSn);
        }
        if (StringUtils.isNotEmpty(order.getSupplierOrderSn())) {
            params.put("orderId", order.getSupplierOrderSn());
        }

        try {
            // 调用接口
            return callLizhiApi("订单查询接口", params);
        } catch (Exception e) {
            log.error("荔枝充值状态查证发生异常,订单:{},详情:{}", orderSn, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 解析查询结果
     */
    private OrderQueryResult parseQueryResult(JSONObject resultJson, String orderSn) {
        OrderQueryResult result = new OrderQueryResult();

        if (resultJson == null) {
            log.error("荔枝查单接口返回为空，订单:{}", orderSn);
            return result;
        }

        String rspCode = resultJson.getString("rspCode");
        JSONObject data = resultJson.getJSONObject("data");

        if (data == null) {
            log.warn("荔枝充值查证返回data为空，订单:{}", orderSn);
            return result;
        }

        result.setOrderId(data.getString("orderId"));
        result.setStatus(data.getString("status"));
        result.setOrderDesc(data.getString("orderDesc"));
        result.setSuccess(RSP_CODE_SUCCESS.equals(rspCode) && STATUS_FINISHED.equals(result.getStatus()));
        result.setFail(RSP_CODE_SUCCESS.equals(rspCode) && STATUS_CANCEL.equals(result.getStatus()));

        log.info("荔枝充值查证状态解析，订单:{}, 响应码:{}, 状态:{}", orderSn, rspCode, result.getStatus());

        return result;
    }

    /**
     * 更新订单充值状态
     */
    private void updateOrderRechargeStatus(Integer workerId, VirtualOrders order, String orderSn, OrderQueryResult queryResult) {
        // 更新子表订单状态
        updateWorkerRechargeStatus(workerId, queryResult.isSuccess(), queryResult.isFail(), queryResult.getOrderDesc());

        // 更新主表订单状态
        updateMainOrderRechargeStatus(order, queryResult.isSuccess(), queryResult.isFail(), queryResult.getOrderId(), queryResult.getOrderDesc());

        // 记录订单流转状态
        if (queryResult.isSuccess() || queryResult.isFail()) {
            recordOrderTrail(orderSn, queryResult.isSuccess(), "充值查证");
            log.info("荔枝查证充值{},订单:{}", queryResult.isSuccess() ? "成功" : "失败", orderSn);
        }
    }

    /**
     * 订单查询结果
     */
    @Data
    private static class OrderQueryResult {
        private String orderId;
        private String status;
        private String orderDesc;
        private boolean success;
        private boolean fail;
    }
    
    /**
     * 商品ID对
     */
    @Data
    private static class ItemIdPair {
        private String spuId;
        private String skuId;
        
        public ItemIdPair(String spuId, String skuId) {
            this.spuId = spuId;
            this.skuId = skuId;
        }
    }
    
    /**
     * 解析supplierItemId获取spuId和skuId
     * 
     * @param order 订单信息
     * @return 包含spuId和skuId的对象
     */
    private ItemIdPair parseSupplierItemId(VirtualOrders order) {
        String supplierItemId = order.getSupplierItemId();
        String[] itemParts = supplierItemId.split(",");
        String spuId = itemParts.length > 0 ? itemParts[0] : "";
        String skuId = itemParts.length > 1 ? itemParts[1] : order.getProductSku();
        return new ItemIdPair(spuId, skuId);
    }

    /**
     * 下单前置校验
     * 调用荔枝下单前置校验接口，校验商品信息
     *
     * @param order 订单信息
     * @return 校验是否通过
     */
    private boolean validateBeforeOrder(VirtualOrders order) {
        // 组装参数
        Map<String, Object> params = buildCommonParams(API_CODE_VALIDATE);
        // 解析supplierItemId，格式为spu,sku
        ItemIdPair itemIdPair = parseSupplierItemId(order);
        
        params.put("spuId", itemIdPair.getSpuId());
        params.put("skuId", itemIdPair.getSkuId());
        params.put("quantity", DEFAULT_QUANTITY);

        try {
            // 调用前置校验接口
            JSONObject resultJson = callLizhiApi("下单前置校验接口", params);

            // 解析响应
            if (resultJson != null) {
                String rspCode = resultJson.getString("rspCode");
                String rspMsg = resultJson.getString("rspMsg");
                boolean success = RSP_CODE_SUCCESS.equals(rspCode);

                if (!success) {
                    log.warn("荔枝前置校验失败，错误码:{}, 错误信息:{}", rspCode, rspMsg);
                }

                return success;
            } else {
                log.error("荔枝前置校验接口返回为空");
                return false;
            }
        } catch (Exception e) {
            log.error("荔枝前置校验发生异常，详情:{}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 构建通用请求参数
     */
    private Map<String, Object> buildCommonParams(String apiCode) {
        Map<String, Object> params = new HashMap<>();
        params.put("appId", config.getAppId());
        params.put("version", API_VERSION);
        params.put("apiCode", apiCode);
        params.put("timestamp", String.valueOf(System.currentTimeMillis()));
        params.put("signType", SIGN_TYPE);
        return params;
    }

    /**
     * 调用荔枝接口
     */
    private JSONObject callLizhiApi(String apiName, Map<String, Object> params) {
        // 计算签名并添加到参数中
        String sign = sign(params, config.getAppSecret());
        params.put("sign", sign);

        // 组装请求URL
        String url = config.getUrl() + API_GATEWAY_PATH;

        // 记录请求日志
        log.info("======调取荔枝接口：{}===开始, url={}, param={}", apiName, url,
                JSONObject.toJSONString(params));

        try {
            // 发送请求
            JSONObject resultJson = OKHttp3SimpleUtils.postByJson(url, JSONObject.toJSONString(params));

            // 记录响应日志
            log.info("======调取荔枝接口：{}===返回结果, result={}", apiName,
                    resultJson == null ? "null" : resultJson.toJSONString());
            if (resultJson == null || !"200".equals(resultJson.getString("responseCode"))) {
                return null;
            }
            return resultJson.getJSONObject("responseVal");
        } catch (Exception e) {
            log.error("======调取荔枝接口：{}===发生异常, 详情:{}", apiName, e.getMessage(), e);
        }

        return null;
    }

    /**
     * 按荔枝文档签名规则实现
     */
    private String sign(Map<String, Object> params, String appSecret) {
        // 1. 剔除sign字段和空值参数，按ASCII码升序排序
        Map<String, String> filtered = filterParams(params);
        List<String> keys = new ArrayList<>(filtered.keySet());
        Collections.sort(keys);

        // 2. 拼接参数
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < keys.size(); i++) {
            String key = keys.get(i);
            sb.append(key).append("=").append(filtered.get(key));
            if (i < keys.size() - 1) {
                sb.append("&");
            }
        }
        sb.append("&key=").append(appSecret);

        String signStr = sb.toString();
        log.debug("订单排序拼接参数signStr:{}", signStr);

        // 3. MD5加密
        String sign = md5(signStr).toUpperCase();
        log.debug("md5签名值:{}", sign);
        return sign;
    }

    /**
     * 剔除sign字段和空值参数
     */
    private static Map<String, String> filterParams(Map<String, Object> params) {
        Map<String, String> filtered = new HashMap<>();
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            if ("sign".equals(entry.getKey())) {
                continue;
            }
            Object value = entry.getValue();
            if (value == null) {
                continue;
            }
            if (value instanceof String && ((String) value).isEmpty()) {
                continue;
            }
            filtered.put(entry.getKey(), String.valueOf(value));
        }
        return filtered;
    }

    /**
     * MD5加密
     */
    private String md5(String str) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            md.update(str.getBytes());
            byte[] digest = md.digest();
            StringBuilder hexStr = new StringBuilder();
            for (byte b : digest) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexStr.append('0');
                }
                hexStr.append(hex);
            }
            return hexStr.toString();
        } catch (Exception e) {
            throw new RuntimeException("MD5加密异常", e);
        }
    }

    /**
     * 更新子表订单下单状态
     */
    private void updateWorkerOrderSubmitStatus(Integer workerId, boolean success, String remark) {
        VirtualOrdersWorker worker = new VirtualOrdersWorker();
        worker.setId(workerId);
        worker.setOrderSubmitStatus(success ? OrderSubmitStatusEnum.ORDER_SUCCESS.getCode() : OrderSubmitStatusEnum.ORDER_FAIL.getCode());
        worker.setOrderSubmitTime(new Date());
        if (success) {
            worker.setOrderRechargeStatus(OrderRechargeStatusEnum.RECHARGE_NO_VERIFY.getCode());
        }
        worker.setRemark(success ? "荔枝下单成功" : "荔枝下单失败:" + remark);
        virtualOrdersWorkerDao.updateVirtualOrdersWorkerById(worker);
    }

    /**
     * 更新主表订单下单状态
     */
    private void updateMainOrderSubmitStatus(VirtualOrders order, boolean success, String supplierOrderSn) {
        VirtualOrders updateOrder = new VirtualOrders();
        updateOrder.setId(order.getId());
        updateOrder.setOrderStatus(success ? OrderStatusEnum.STATUS_ORDER_SUCCESS.getCode() : OrderStatusEnum.STATUS_ORDER_FAIL.getCode());
        updateOrder.setRemark(success ? OrderStatusEnum.STATUS_ORDER_SUCCESS.getDesc() : OrderStatusEnum.STATUS_ORDER_FAIL.getDesc());
        if (StringUtils.isNotEmpty(supplierOrderSn)) {
            updateOrder.setSupplierOrderSn(supplierOrderSn);
        }
        // 充值状态赋值
        updateOrder.setRechargeStatus(success ? null : RechargeStatusEnum.RECHARGE_FAIL.getCode());
        virtualOrdersDao.updateVirtualOrdersById(updateOrder);
    }

    /**
     * 更新子表订单充值状态
     */
    private void updateWorkerRechargeStatus(Integer workerId, boolean success, boolean fail, String orderDesc) {
        VirtualOrdersWorker worker = new VirtualOrdersWorker();
        worker.setId(workerId);
        if (success) {
            worker.setOrderRechargeStatus(OrderRechargeStatusEnum.RECHARGE_SUCCESS.getCode());
            worker.setOrderRechargeTime(new Date());
            worker.setRemark("荔枝查证充值成功");
        } else if (fail) {
            worker.setOrderRechargeStatus(OrderRechargeStatusEnum.RECHARGE_FAIL.getCode());
            worker.setOrderRechargeTime(new Date());
            worker.setRemark("荔枝查证充值失败:" + orderDesc);
        } else {
            worker.setRemark("荔枝查证处理中或无结果");
        }
        virtualOrdersWorkerDao.updateVirtualOrdersWorkerById(worker);
    }

    /**
     * 更新主表订单充值状态
     */
    private void updateMainOrderRechargeStatus(VirtualOrders order, boolean success, boolean fail, String orderId, String orderDesc) {
        VirtualOrders updateOrder = new VirtualOrders();
        updateOrder.setId(order.getId());
        if (success) {
            updateOrder.setOrderStatus(OrderStatusEnum.STATUS_RECHARGE_SUCCESS.getCode());
            updateOrder.setRemark(OrderStatusEnum.STATUS_RECHARGE_SUCCESS.getDesc());
            // 充值状态赋值
            updateOrder.setRechargeStatus(RechargeStatusEnum.RECHARGE_SUCCESS.getCode());
        } else if (fail) {
            updateOrder.setOrderStatus(OrderStatusEnum.STATUS_RECHARGE_FAIL.getCode());
            updateOrder.setRemark(OrderStatusEnum.STATUS_RECHARGE_FAIL.getDesc() + (StringUtils.isNotEmpty(orderDesc) ? ":" + orderDesc : ""));
            // 充值状态赋值
            updateOrder.setRechargeStatus(RechargeStatusEnum.RECHARGE_FAIL.getCode());
        } else {
            updateOrder.setRemark("荔枝查证处理中或无结果");
        }
        // 如果返回了平台单号，则更新
        if (StringUtils.isNotEmpty(orderId)) {
            updateOrder.setSupplierOrderSn(orderId);
        }
        virtualOrdersDao.updateVirtualOrdersById(updateOrder);
    }

    /**
     * 记录订单流转状态
     */
    private void recordOrderTrail(String orderSn, boolean success, String operationType) {
        if ("下单".equals(operationType)) {
            ordersTrailModel.saveOrdersTrail(orderSn,
                    OrderStatusEnum.STATUS_ORDER_NO_PUSH.getCode(),
                    success ? OrderStatusEnum.STATUS_ORDER_SUCCESS.getCode() : OrderStatusEnum.STATUS_ORDER_FAIL.getCode(),
                    success ? "荔枝下单成功" : "荔枝下单失败");
        } else if ("充值查证".equals(operationType)) {
            ordersTrailModel.saveOrdersTrail(orderSn,
                    OrderStatusEnum.STATUS_ORDER_SUCCESS.getCode(),
                    success ? OrderStatusEnum.STATUS_RECHARGE_SUCCESS.getCode() : OrderStatusEnum.STATUS_RECHARGE_FAIL.getCode(),
                    success ? "荔枝查证充值成功" : "荔枝查证充值失败");
        }
    }

}