package com.juzifenqi.virtual.core.constant;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/2/8 11:00 AM
 */
public class RedisConstantPrefix {

    /**
     * 由于和 plus-abyss 项目共用一个缓存，所以要保持一致
     */
    public final static String BASE_PREFIX = "super_plus:";

    /**
     * 分流主体缓存
     */
    public final static String SUPPLIER_CONFIG = BASE_PREFIX + "PLUS_SHUNT_SUPPLIER_";


    /**
     * 虚拟订单开始ID
     */
    public final static String VIRTUAL_ORDERS_BEGIN_ID = BASE_PREFIX + "VIRTUAL_ORDERS_BEGIN_ID";

    /**
     * 虚拟订单检查开始ID
     */
    public final static String VIRTUAL_ORDERS_INSPECT_BEGIN_ID = BASE_PREFIX + "VIRTUAL_ORDERS_INSPECT_BEGIN_ID";

}
