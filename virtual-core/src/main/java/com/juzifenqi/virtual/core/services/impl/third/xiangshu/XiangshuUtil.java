package com.juzifenqi.virtual.core.services.impl.third.xiangshu;

import com.alibaba.cloudapi.sdk.client.ApacheHttpClient;
import com.alibaba.cloudapi.sdk.constant.SdkConstant;
import com.alibaba.cloudapi.sdk.enums.HttpMethod;
import com.alibaba.cloudapi.sdk.enums.ParamPosition;
import com.alibaba.cloudapi.sdk.enums.Scheme;
import com.alibaba.cloudapi.sdk.model.ApiRequest;
import com.alibaba.cloudapi.sdk.model.ApiResponse;
import com.alibaba.cloudapi.sdk.model.HttpClientBuilderParams;
import com.alibaba.fastjson.JSON;
import com.google.gson.Gson;
import com.juzifenqi.virtual.bean.system.Response;
import com.juzifenqi.virtual.bean.vo.RechargeParamVo;
import com.juzifenqi.virtual.bean.vo.XsVerifyParamVo;
import com.juzifenqi.virtual.component.enums.RechargeCodeEnum;
import com.juzifenqi.virtual.component.util.RSAUtil;
import com.juzifenqi.virtual.core.services.impl.third.xiangshu.model.BasicCouponReq;
import com.juzifenqi.virtual.core.services.impl.third.xiangshu.model.DistributeCouponReq;
import com.juzifenqi.virtual.core.services.impl.third.xiangshu.model.InvalidCouponReq;
import com.juzifenqi.virtual.core.services.impl.third.xiangshu.model.XiangshuDistributeCouponReq;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

/**
 * 橡树黑卡配置类
 *
 * <AUTHOR>
 * @date 2023-07-14 10:33:23
 */
@Slf4j
public class XiangshuUtil extends ApacheHttpClient {

    /**
     * 联登
     */
    public final static String URI_LOGIN              = "/api/login/once";
    public final static String URI_CREATE_MEMBER      = "/api/core/create_member";
    public final static String URI_NI_WO_DAI_BENEFITS = "/api/core/niwodai_benefits";
    public final static String URI_QUERY_ORDER        = "/api/xpay/query_order";

    /**
     * 入账
     */
    public final static String URI_CREATE_ORDER = "/api/core/jdd_order_create";

    /**
     * 出账
     */
    public final static String URI_REFUND_ORDER = "/api/core/jdd_order_refund";

    /**
     * 权益下单
     */
    public final static String URI_QY_ORDER = "/api/mq/single_benefit_place_order";

    /**
     * 权益订单查询
     */
    public final static String URI_QY_ORDER_QUERY = "/api/mq/single_benefit_order_info";

    /**
     * 领券
     */
    public final static String URI_DISTRIBUTE_COUPON = "/api/activity/distribute_coupon";

    /**
     * 查询领券结果
     */
    public final static String URI_QY_DISTRIBUTE_COUPON = "/api/activity/distribute_coupon/status";

    /**
     * 查询用户优惠券列表
     */
    public final static String URI_QY_USER_COUPONS = "/api/activity/user_coupons";

    /**
     * 接口域名
     */
    String     host;
    /**
     * api网关的key
     */
    String     key;
    /**
     * api网关的secret
     */
    String     secret;
    /**
     * 业务签名所需要的私钥
     */
    PrivateKey privateKey;

    public XiangshuUtil(String host, String key, String secret, String privateKeyStr) {
        this.host = host;
        this.key = key;
        this.secret = secret;
        try {
            this.privateKey = RSAUtil.getPrivateKey(privateKeyStr);
        } catch (Exception e) {
            this.privateKey = null;
        }
        init();
    }


    public XiangshuUtil(String host, String key, String secret) {
        this.host = host;
        this.key = key;
        this.secret = secret;
        init();
    }

    /**
     * 初始化
     */
    private void init() {
        HttpClientBuilderParams httpParam = new HttpClientBuilderParams();
        httpParam.setAppKey(key);
        httpParam.setAppSecret(secret);
        httpParam.setScheme(Scheme.HTTPS);
        httpParam.setHost(host);
        super.init(httpParam);
    }

    /**
     * 订单签名
     */
    private String sign(Map<String, String> params) throws Exception {
        //1.构建待签名的json
        Gson gson = new Gson();
        String dataJson = gson.toJson(params); //map转json字符串
        //2.对json排序，得到待签名的字符串
        String signContent = sortStr(dataJson);
        //3.签名
        return RSAUtil.sign(signContent, privateKey);
    }

    /**
     * 构建签名内容---权益签名
     * <p>从给定的参数Map中构建一个待签名的字符串<p/>
     * <p>创建了一个新的ArrayList，将Map的所有键添加到这个列表中，然后对这个列表进行了排序。<p/>
     * <p>接着，遍历这个列表，对每一个键，将这个键和对应的值以及一个"&"符号追加到一个StringBuilder中。<p/>
     * <p>最后，删除最后一个"&"符号，返回这个StringBuilder的字符串形式<p/>
     */
    private static String buildSignContent(HashMap<String, Object> params) {
        ArrayList<String> keys = new ArrayList<>(params.keySet());
        Collections.sort(keys);
        StringBuilder signStr = new StringBuilder();
        for (String key : keys) {
            signStr.append(key).append("=").append(params.get(key)).append("&");
        }
        signStr.deleteCharAt(signStr.length() - 1);  // Remove trailing '&'
        return signStr.toString();
    }


    /**
     * 生成权益签名
     * <p>对 data 和 appSecret 进行签名，<p/>
     * <p>签名是通过在数据上应用一个由 appSecret 密钥和一个哈希函数（HMAC SHA-256）生成的<p/>
     */
    private static String sign(HashMap<String, Object> params, String appSecret) {
        String signContent = buildSignContent(params);
        try {
            //首先实例化一个 HMAC SHA-256 的 Mac 对象。
            Mac hmacSHA256 = Mac.getInstance("HmacSHA256");
            //然后，将 appSecret 转换为字节
            byte[] keyBytes = appSecret.getBytes(StandardCharsets.UTF_8);
            //并用它来创建一个 SecretKeySpec 对象。
            SecretKeySpec secretKey = new SecretKeySpec(keyBytes, "HmacSHA256");
            //接着，用这个密钥初始化 Mac 对象。
            hmacSHA256.init(secretKey);
            //使用 doFinal 方法对生成的签名内容进行哈希处理，返回哈希值的字节数组。
            byte[] hmacData = hmacSHA256.doFinal(signContent.getBytes(StandardCharsets.UTF_8));
            //分配空间
            StringBuilder sb = new StringBuilder(hmacData.length * 2);
            //然后，将这个字节数组转换为十六进制字符串。这个字符串就是 HMAC 签名。
            for (byte b : hmacData) {
                sb.append(String.format("%02x", b & 0xff));
            }
            return sb.toString();
        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            throw new RuntimeException("Failed to calculate hmac", e);
        }
    }

    /**
     * 创建订单-入账
     *
     * @param token 联登获取的token
     * @param orderSn 桔子单号
     * @param payAmount 支付金额
     * @param payDateTime 支付时间
     * @param payOrderNo 支付流水号
     * @param productName 商品名称
     */
    public ApiResponse createOrder(String token, String orderSn, String productName,
            String payOrderNo, String payAmount, String payDateTime) throws Exception {
        ApiRequest request = new ApiRequest(HttpMethod.POST_FORM, URI_CREATE_ORDER);
        request.addHeader("Authorization", "JWT " + token);

        request.addParam("jdd_order_number", orderSn, ParamPosition.BODY, true);
        request.addParam("product_name", productName, ParamPosition.BODY, true);
        request.addParam("allinpay_order_number", payOrderNo, ParamPosition.BODY, true);
        request.addParam("pay_amount", payAmount, ParamPosition.BODY, true);
        request.addParam("pay_datetime", payDateTime, ParamPosition.BODY, true);
        //签名
        HashMap<String, String> map = new HashMap<String, String>();
        map.put("jdd_order_number", orderSn);
        map.put("product_name", productName);
        map.put("allinpay_order_number", payOrderNo);
        map.put("pay_amount", payAmount);
        map.put("pay_datetime", payDateTime);
        request.addParam("sign", sign(map), ParamPosition.BODY, true);
        return sendSyncRequest(request);
    }


    /**
     * 订单退款-出账
     *
     * @param token 联登获取的token
     * @param orderSn 桔子订单号
     * @param refundAmount 退款金额
     * @param refundTime 退款时间
     * @param refundSource 退款方式，原理退=通联，代付=桔多多
     */
    public ApiResponse refundOrder(String token, String orderSn, String refundAmount,
            String refundTime, String refundSource) throws Exception {
        ApiRequest request = new ApiRequest(HttpMethod.POST_FORM, URI_REFUND_ORDER);
        request.addHeader("Authorization", "JWT " + token);
        request.addParam("jdd_order_number", orderSn, ParamPosition.BODY, true);
        request.addParam("refund_amount", refundAmount, ParamPosition.BODY, true);
        request.addParam("refund_datetime", refundTime, ParamPosition.BODY, true);
        request.addParam("refund_source", refundSource, ParamPosition.BODY, true);
        //签名
        HashMap<String, String> map = new HashMap<String, String>();
        map.put("jdd_order_number", orderSn);
        map.put("refund_amount", refundAmount);
        map.put("refund_datetime", refundTime);
        map.put("refund_source", refundSource);
        request.addParam("sign", sign(map), ParamPosition.BODY, true);
        return sendSyncRequest(request);
    }


    /**
     * 把字符串按ascii排序
     */
    public static String sortStr(String str) {
        char[] cArr = new char[str.length()];
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < str.length(); i++) {
            cArr[i] = str.charAt(i);
        }
        Arrays.sort(cArr);
        for (char c : cArr) {
            sb.append(c);
        }
        return sb.toString().trim();
    }

    /**
     * debug函数，获取响应字符串
     */
    public static String getResultString(ApiResponse response) throws IOException {
        StringBuilder result = new StringBuilder();
        result.append("Response from backend server").append(SdkConstant.CLOUDAPI_LF)
                .append(SdkConstant.CLOUDAPI_LF);
        result.append("ResultCode:").append(SdkConstant.CLOUDAPI_LF).append(response.getCode())
                .append(SdkConstant.CLOUDAPI_LF).append(SdkConstant.CLOUDAPI_LF);
        Map<String, List<String>> headers = response.getHeaders();
        result.append("******ResultHeads*****").append(SdkConstant.CLOUDAPI_LF);
        for (Map.Entry<String, List<String>> entry : headers.entrySet()) {
            result.append(entry.getKey()).append(":").append(entry.getValue().get(0))
                    .append(SdkConstant.CLOUDAPI_LF);
        }
        result.append("******ResultHeads*****").append(SdkConstant.CLOUDAPI_LF);

        result.append(SdkConstant.CLOUDAPI_LF).append("******ResultBody******");
        result.append(SdkConstant.CLOUDAPI_LF)
                .append(new String(response.getBody(), SdkConstant.CLOUDAPI_ENCODING));
        result.append(SdkConstant.CLOUDAPI_LF).append("******ResultBody******");

        return result.toString();
    }

    /**
     * 调用橡树创建权益订单
     *
     * @param paramVo getOrderSn 桔子虚拟权益订单号
     * @param paramVo getProductCode黑卡商品编号
     * @param paramVo getRechargeAccount 充值手账号
     * @param paramVo getRechargeCode 充值code
     * @param paramVo getCallbackUrl 回调地址
     * @param paramVo getProfitKey 权益appkey
     * @param paramVo getProfitSecret 权益appSecret
     */
    public ApiResponse createQyOrder(RechargeParamVo paramVo) {
        String oderNo = paramVo.getOrderSn();
        String productCode = paramVo.getProductCode();
        //充值账号
        String rechargeAccount = paramVo.getRechargeAccount();
        //充值类型：1.手机号 2.抖音账号 3.微博昵称 4.qq号
        Integer rechargeCode = paramVo.getRechargeCode();
        String notifyUrl = paramVo.getCallbackUrl();
        String profitKey = paramVo.getProfitKey();
        String profitSecret = paramVo.getProfitSecret();
        ApiRequest request = new ApiRequest(HttpMethod.POST_FORM, URI_QY_ORDER);
        request.addParam("app_key", profitKey, ParamPosition.BODY, true);
        request.addParam("order_number", oderNo, ParamPosition.BODY, true);
        request.addParam("product_code", productCode, ParamPosition.BODY, true);
        request.addParam("notify_url", notifyUrl, ParamPosition.BODY, true);
        //判断充值类型
        if (rechargeCode == RechargeCodeEnum.RECHARGE_TYPE_MOBILE.getCode()) {
            request.addParam("mobile", rechargeAccount, ParamPosition.BODY, false);
        } else {
            request.addParam("account", rechargeAccount, ParamPosition.BODY, false);
        }
        //签名参数
        HashMap<String, Object> map = new HashMap<>(8);
        map.put("app_key", profitKey);
        map.put("order_number", oderNo);
        map.put("product_code", productCode);
        map.put("notify_url", notifyUrl);
        //判断充值类型
        if (rechargeCode == RechargeCodeEnum.RECHARGE_TYPE_MOBILE.getCode()) {
            map.put("mobile", rechargeAccount);
        } else {
            map.put("account", rechargeAccount);
        }
        String sign = sign(map, profitSecret);
        request.addParam("sign", sign, ParamPosition.BODY, true);
        log.info("调用橡树创建权益订单开始入参:{}", JSON.toJSONString(request));
        ApiResponse apiResponse = sendSyncRequest(request);
        log.info("调用橡树创建权益订单返回结果:{}", JSON.toJSONString(apiResponse));
        return apiResponse;
    }


    /**
     * 查询权益订单
     *
     * @param paramVo getOrderSn 桔子虚拟权益订单号
     * @param paramVo getProfitKey 权益appkey
     * @param paramVo getProfitSecret 权益appSecret
     */
    public ApiResponse queryQyOrder(XsVerifyParamVo paramVo) {
        ApiRequest request = new ApiRequest(HttpMethod.GET, URI_QY_ORDER_QUERY);
        String oderNo = paramVo.getOrderSn();
        String profitKey = paramVo.getProfitKey();
        String profitSecret = paramVo.getProfitSecret();
        request.addParam("app_key", profitKey, ParamPosition.QUERY, true);
        request.addParam("order_number", oderNo, ParamPosition.QUERY, true);
        HashMap<String, Object> map = new HashMap<>();
        map.put("app_key", profitKey);
        map.put("order_number", oderNo);
        String sign = sign(map, profitSecret);
        request.addParam("sign", sign, ParamPosition.QUERY, true);
        log.info("调用橡树查询充值状态开始入参:{}", JSON.toJSONString(request));
        ApiResponse apiResponse = sendSyncRequest(request);
        log.info("调用橡树查询充值状态返回结果:{}", JSON.toJSONString(apiResponse));
        return apiResponse;
    }

    /**
     * 登录请求
     */
    public Response<String> login(String phoneNumber, String code) {
        log.info("调用橡树[登录]接口-封装参数，方法入参：param:{} {}", phoneNumber, code);
        ApiRequest request = new ApiRequest(HttpMethod.POST_FORM, URI_LOGIN);

        // body
        request.addParam("platform", "extra", ParamPosition.BODY, true);
        request.addParam("phone_number", phoneNumber, ParamPosition.BODY, true);
        request.addParam("source", "api", ParamPosition.BODY, false);
        request.addParam("code", code, ParamPosition.BODY, true);
        request.addParam("namespace", "jdd", ParamPosition.BODY, true);
        return httpRequest("登录", request);
    }

    /**
     * 领取优惠券
     *
     * @Param req 领券请求
     * @Param req.token 橡树联登token
     * @Param req.txId 幂等id=虚拟订单号
     * @Param req.item.batchId 批次号=合作方商品id
     * @Param req.item.expiration 优惠券有效期=当前会员的结束时间
     */
    public Response<String> distributeCoupon(DistributeCouponReq req) {
        log.info("调用橡树[领券]接口-封装参数，方法入参：param:{}", JSON.toJSONString(req));
        ApiRequest request = new ApiRequest(HttpMethod.POST_FORM, URI_DISTRIBUTE_COUPON);
        // header
        request.addHeader("Authorization", "JWT " + req.getToken());
        // body
        request.addParam("txid", req.getTxId(), ParamPosition.BODY, true);
        List<XiangshuDistributeCouponReq> list = new ArrayList<>();
        if (!CollectionUtils.isEmpty(req.getCouponDataReqList())) {
            req.getCouponDataReqList().forEach(c -> {
                XiangshuDistributeCouponReq coupon = new XiangshuDistributeCouponReq();
                coupon.setCount(c.getCount());
                coupon.setBatch_id(c.getBatchId());
                coupon.setExpiration(c.getExpiration());
                list.add(coupon);
            });
        }
        request.addParam("data", JSON.toJSONString(list), ParamPosition.BODY, true);
        return httpRequest("领券", request);
    }

    /**
     * 查询领券结果
     *
     * @Param req 查询请求
     * @Param req.token 橡树联登token
     * @Param req.txId 幂等id=虚拟订单号
     */
    public Response<String> queryDistributeCouponResult(BasicCouponReq req) {
        log.info("调用橡树[查询领券结果]接口-封装参数，方法入参：param:{}", JSON.toJSONString(req));
        ApiRequest request = new ApiRequest(HttpMethod.GET, URI_QY_DISTRIBUTE_COUPON);

        // header
        request.addHeader("Authorization", "JWT " + req.getToken());
        // query
        request.addParam("txid", req.getTxId(), ParamPosition.QUERY, true);
        return httpRequest("查询领券结果", request);
    }

    /**
     * 查询用户优惠券列表
     *
     * @Param req 查询请求
     * @Param req.token 橡树联登token
     */
    public Response<String> queryCoupon(BasicCouponReq req) {
        log.info("调用橡树[查询用户优惠券列表]接口-封装参数，方法入参：param:{}",
                JSON.toJSONString(req));
        ApiRequest request = new ApiRequest(HttpMethod.GET, URI_QY_USER_COUPONS);

        // header
        request.addHeader("Authorization", "JWT " + req.getToken());
        return httpRequest("查询用户优惠券列表", request);
    }

    /**
     * 失效优惠券
     *
     * @Param req 查询请求
     * @Param req.token 橡树联登token
     * @Param req.couponIds 失效优惠券ids
     */
    public Response<String> invalidCoupon(InvalidCouponReq req) {
        log.info("调用橡树[失效优惠券]接口-封装参数，方法入参：param:{}", JSON.toJSONString(req));
        ApiRequest request = new ApiRequest(HttpMethod.POST_FORM, "/api/activity/invalid_coupon");

        // header
        request.addHeader("Authorization", "JWT " + req.getToken());
        // body
        request.addParam("coupon_ids", JSON.toJSONString(req.getCouponIds()), ParamPosition.BODY,
                true);
        return httpRequest("失效优惠券", request);
    }

    /**
     * 三方接口调用
     */
    private Response<String> httpRequest(String method, ApiRequest request) {
        log.info("调用橡树[{}]三方http接口入参：body:{} query:{}", method,
                JSON.toJSONString(request.getFormParams()), JSON.toJSONString(request.getQuerys()));
        long costTimeStart = System.currentTimeMillis();
        ApiResponse response = sendSyncRequest(request);
        long costTimeEnd = System.currentTimeMillis();
        long totalTimeCost = costTimeEnd - costTimeStart;
        if (response.getCode() != 200) {
            // 调用失败
            String errorMsg =
                    response.getMessage() + response.getHeaders().get("x-ca-error-message");
            log.info("调用橡树[{}]三方http接口出参:{} 总耗时:{}", method,
                    response.getCode() + errorMsg, totalTimeCost);
            return Response.fail(response.getCode(), errorMsg);
        }
        // 调用成功
        String body = new String(response.getBody(), StandardCharsets.UTF_8);
        log.info("调用橡树[{}]三方http接口出参:{} 总耗时:{}", method, JSON.toJSONString(body),
                totalTimeCost);
        return Response.ok(body);
    }
}
