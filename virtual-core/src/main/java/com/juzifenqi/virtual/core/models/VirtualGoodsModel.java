package com.juzifenqi.virtual.core.models;

import com.alibaba.fastjson.JSONObject;
import com.juzifenqi.virtual.bean.bo.VirtualGoodsBo;
import com.juzifenqi.virtual.bean.vo.VirtualGoodsVo;
import com.juzifenqi.virtual.dao.VirtualGoodsDao;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 针对后台管理的操作
 *
 * <AUTHOR>
 * @verson 1.0
 * @date 2022/04/28 11:17
 */
@Component
@Slf4j
public class VirtualGoodsModel {

    @Autowired
    private VirtualGoodsDao virtualGoodsDao;


    public int getVirtualGoodsCount(VirtualGoodsBo virtualGoodsBo) {
        return virtualGoodsDao.pageListCount(virtualGoodsBo);
    }

    public List<VirtualGoodsVo> getVirtualGoodsList(VirtualGoodsBo virtualGoodsBo) {
        log.info("权益分页查询，virtualGoodsBo:{}", virtualGoodsBo);
        List<VirtualGoodsVo> result = virtualGoodsDao
                .pageList(virtualGoodsBo, virtualGoodsBo.getStartPage(),
                        virtualGoodsBo.getPageSize());
        log.info("权益分页返回结果:{}", JSONObject.toJSONString(result));
        return result;
    }

}
