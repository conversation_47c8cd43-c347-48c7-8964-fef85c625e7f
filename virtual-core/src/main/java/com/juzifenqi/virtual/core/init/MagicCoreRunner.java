package com.juzifenqi.virtual.core.init;

import com.groot.utils.exception.LogUtil;
import com.juzifenqi.virtual.dao.VirtualOrdersDao;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class MagicCoreRunner implements ApplicationRunner {

    @Resource
    private VirtualOrdersDao virtualOrdersDao;

    @Override
    public void run(ApplicationArguments args) {
        try {
            virtualOrdersDao.getVirtualOrdersByOrderSn("999999999");
            log.info("=======连接数据库测试结束=======");
        } catch (Exception e) {
            LogUtil.printLog("系统异常 ", e);
        }
    }
}
