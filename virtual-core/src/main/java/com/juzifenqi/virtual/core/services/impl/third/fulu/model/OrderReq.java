package com.juzifenqi.virtual.core.services.impl.third.fulu.model;

import java.io.Serializable;
import lombok.Data;

@Data
public class OrderReq implements Serializable {

    /**
     * 商品编号
     */
    private String productId;
    /**
     * 购买数量
     */
    private Integer buyNum;
    /**
     * 外部单号
     */
    private String customerOrderNo;
    /**
     * 下单类型 1 直充 2 卡密
     */
    private int rechargeType;
    /**
     * 充值账号 (直充必传)
     */
    private String chargeAccount;
    /**
     * 是否需要解析卡密
     */
    private Boolean decodeCards = false;
}
