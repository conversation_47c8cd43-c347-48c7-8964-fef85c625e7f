package com.juzifenqi.virtual.core.mq.consumer;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.bean.ConsumerBean;
import com.juzifenqi.mq.consumer.normal.NormalConsumerClient;
import com.juzifenqi.virtual.bean.pojo.VirtualOrders;
import com.juzifenqi.virtual.bean.pojo.VirtualOrdersWorker;
import com.juzifenqi.virtual.component.enums.FlowPayStateEnum;
import com.juzifenqi.virtual.component.enums.OrderStatusEnum;
import com.juzifenqi.virtual.component.enums.OrderSubmitStatusEnum;
import com.juzifenqi.virtual.component.enums.PayStateEnum;
import com.juzifenqi.virtual.component.enums.RechargeStatusEnum;
import com.juzifenqi.virtual.core.conf.VirtualMqConfig;
import com.juzifenqi.virtual.core.models.VirtualOrderModel;
import com.juzifenqi.virtual.dao.VirtualOrdersDao;
import com.juzifenqi.virtual.dao.VirtualOrdersWorkerDao;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import javax.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.StringUtils;

/**
 * 订单MQ消费-虚拟商品支付成功
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/26 1:45 下午
 */
@Configuration
@Slf4j
public class MqListenerForVirtualPaySuc extends NormalConsumerClient {

    @Autowired
    private VirtualMqConfig        mqConfig;
    @Autowired
    private VirtualOrdersWorkerDao virtualOrdersWorkerDao;
    @Autowired
    private VirtualOrdersDao       virtualOrdersDao;
    @Autowired
    private VirtualOrderModel      virtualOrderModel;

    @PostConstruct
    public ConsumerBean initOrderSucListener() {
        return initConsumer(mqConfig.getVirtualPayNoticeGid(), mqConfig.getVirtualPayNoticeTopic());
    }

    @Override
    protected boolean dealMessage(Message message, ConsumeContext consumeContext) {
        dealMqMsg(message);
        return true;
    }

    private boolean dealMqMsg(Message message) {
        try {
            String body = new String(message.getBody(), StandardCharsets.UTF_8);
            log.info("收到订单支付成功消息---处理开始 入参 {}", body);
            if (StringUtils.isEmpty(body)) {
                return true;
            }
            JSONObject jsonObject = JSONObject.parseObject(body);
            String orderSn = jsonObject.getString("orderSn");
            VirtualOrders order = virtualOrdersDao.getVirtualOrdersByOrderSn(orderSn);
            if (order == null) {
                log.info("订单号 {} 不是虚拟商品订单,不作处理", orderSn);
                return true;
            }
            if (OrderStatusEnum.STATUS_MALL_NO_PAY.getCode() != order.getOrderStatus()) {
                log.info("订单号 {} 消息已经消费过,不作处理", orderSn);
                return true;
            }
            // 主表订单状态改为待充值下单，支付状态为支付成功，充值状态为充值中
            VirtualOrders orders = new VirtualOrders();
            orders.setOrderSn(orderSn);
            orders.setPayStatus(PayStateEnum.PAY_SUCCESS.getCode());
            orders.setRechargeStatus(RechargeStatusEnum.RECHARGE_ING.getCode());
            orders.setOrderStatus(OrderStatusEnum.STATUS_ORDER_NO_PUSH.getCode());
            orders.setRemark(OrderStatusEnum.STATUS_ORDER_NO_PUSH.getDesc());
            virtualOrdersDao.updateOrderByOrderSn(orders);

            // 更新请求流水状态
            virtualOrderModel.updateOrderPayFlow(jsonObject.getString("applySerialNo"),
                    jsonObject.getString("serialNumber"), FlowPayStateEnum.PAY_SUCCESS);
            // 插入任务表
            VirtualOrdersWorker virtualOrdersWorker = new VirtualOrdersWorker();
            virtualOrdersWorker.setOrderSn(order.getOrderSn());
            virtualOrdersWorker.setOrderSubmitStatus(OrderSubmitStatusEnum.ORDER_NO_PUSH.getCode());
            virtualOrdersWorker.setGetRetryCount(0);
            virtualOrdersWorker.setRemark(OrderSubmitStatusEnum.ORDER_NO_PUSH.getDesc());
            virtualOrdersWorker.setCreateTime(new Date());
            virtualOrdersWorkerDao.saveVirtualOrdersWorker(virtualOrdersWorker);//插入任务记录
            log.info("虚拟商品订单号 {} 支付成功消息---处理完毕", orderSn);
            return true;
        } catch (Exception e) {
            log.error("虚拟商品支付成功消息处理发生异常", e);
            return false;
        }
    }
}
