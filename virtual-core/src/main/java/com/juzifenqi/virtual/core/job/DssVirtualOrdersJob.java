package com.juzifenqi.virtual.core.job;

import com.juzifenqi.virtual.bean.pojo.VirtualOrders;
import com.juzifenqi.virtual.core.constant.RedisConstantPrefix;
import com.juzifenqi.virtual.core.utils.RedisUtils;
import com.juzifenqi.virtual.dao.VirtualOrdersDao;
import com.juzishuke.dss.sdk.service.DssOfflineSdkService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/4/27  14:07
 * @description
 */
@Component
@Slf4j
public class DssVirtualOrdersJob {

    @Resource
    private VirtualOrdersDao virtualOrdersDao;

    @Autowired
    private DssOfflineSdkService dssOfflineSdkService;

    @Autowired
    private RedisUtils redisUtils;

    @XxlJob("dssVirtualOrdersJob")
    public ReturnT<String> execute(String param) {
        Long index = 0L;
        Integer batchSize = 1;
        if (StringUtils.isNotBlank(param)) {
            String[] split = param.split(",");
            index = Long.parseLong(split[0]);
            batchSize = Integer.parseInt(split[1]);
        }
        try {
            log.info("[结算订单重提] 充值账号密文任务开始");
            if (redisUtils.hasKey(RedisConstantPrefix.VIRTUAL_ORDERS_BEGIN_ID)) {
                index = Long.parseLong(redisUtils.get(RedisConstantPrefix.VIRTUAL_ORDERS_BEGIN_ID));
            }
            List<VirtualOrders> list = virtualOrdersDao.getWithEmptyUuidVirtualOrders(index, batchSize);
            log.info("[结算订单重提] data cleaning search db, size: {}, index: {}", list.size(), index);
            if (list.isEmpty()) {
                return ReturnT.SUCCESS;
            }
            List<String> rechargeAccountList = list.stream().map(VirtualOrders::getRechargeAccount).distinct().collect(Collectors.toList());

            Map<String, String> mwMap = dssOfflineSdkService.encryptPhoneBatch(new ArrayList<>(rechargeAccountList));
            log.info("[结算订单重提] data cleaning encryptPhoneBatch size: {}, index: {}", mwMap.size(), index);

            List<VirtualOrders> updateList = list.stream().map(item -> {
                VirtualOrders virtualOrders = new VirtualOrders();
                virtualOrders.setId(item.getId());
                virtualOrders.setRechargeAccountUuid(mwMap.get(item.getRechargeAccount()));
                return virtualOrders;
            }).collect(Collectors.toList());
            virtualOrdersDao.updateBatchById(updateList);

            int nextIndex = Math.toIntExact(list.stream().sorted(Comparator.comparing(VirtualOrders::getId).reversed()).map(VirtualOrders::getId).findFirst().get());
            redisUtils.setEx(RedisConstantPrefix.VIRTUAL_ORDERS_BEGIN_ID, String.valueOf(nextIndex), 10, TimeUnit.MINUTES);
            log.info("[结算订单重提] 充值账号密文任务结束");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("初始化结算订单充值账号密文任务执行发生异常:{}", e.getMessage());
        }
        return ReturnT.FAIL;
    }

    @XxlJob("dssVirtualOrdersInspectJob")
    public ReturnT<String> dssVirtualOrdersInspect(String param) {
        Long index = 0L;
        Integer batchSize = 1;
        if (StringUtils.isNotBlank(param)) {
            String[] split = param.split(",");
            index = Long.parseLong(split[0]);
            batchSize = Integer.parseInt(split[1]);
        }
        try {
            log.info("结算订单,充值账号密文巡检任务开始");
            if (redisUtils.hasKey(RedisConstantPrefix.VIRTUAL_ORDERS_INSPECT_BEGIN_ID)) {
                index = Long.parseLong(redisUtils.get(RedisConstantPrefix.VIRTUAL_ORDERS_INSPECT_BEGIN_ID));
            }
            List<VirtualOrders> virtualOrdersList = virtualOrdersDao.getWithUuidVirtualOrders(index, batchSize);
            log.info("结算订单, data inspect search db, size: {}, index: {}", virtualOrdersList.size(), index);
            if (virtualOrdersList.isEmpty()) {
                return ReturnT.SUCCESS;
            }
            List<String> rechargeAccountList = virtualOrdersList.stream().map(VirtualOrders::getRechargeAccount).distinct().collect(Collectors.toList());

            Map<String, String> mwMap = dssOfflineSdkService.encryptPhoneBatch(new ArrayList<>(rechargeAccountList));
            log.info("结算订单, data inspect encryptPhoneBatch size: {}, index: {}", mwMap.size(), index);

            List<VirtualOrders> updateList = virtualOrdersList.stream().filter(virtualOrder -> {
                String rechargeAccount = virtualOrder.getRechargeAccount();
                String rechargeAccountUuid = virtualOrder.getRechargeAccountUuid();
                String m = mwMap.get(rechargeAccount);
                if (!rechargeAccountUuid.equals(m)) {
                    log.info("结算订单,新密文校验错误,data inspect id:{}, rechargeAccountUuid: {}, m: {}",
                            virtualOrder.getId(), rechargeAccountUuid, m);
                    return true;
                }
                return false;
            }).map(item -> {
                VirtualOrders virtualOrders = new VirtualOrders();
                virtualOrders.setId(item.getId());
                virtualOrders.setRechargeAccountUuid(mwMap.get(item.getRechargeAccount()));
                return virtualOrders;
            }).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(updateList)) {
                virtualOrdersDao.updateBatchById(updateList);
            }

            int nextIndex = Math.toIntExact(virtualOrdersList.stream().sorted(Comparator.comparing(VirtualOrders::getId).reversed()).map(VirtualOrders::getId).findFirst().get());
            redisUtils.setEx(RedisConstantPrefix.VIRTUAL_ORDERS_INSPECT_BEGIN_ID, String.valueOf(nextIndex), 10, TimeUnit.MINUTES);
            log.info("结算订单,充值账号密文巡检任务结束");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("初始化结算订单充值账号密文巡检任务执行发生异常:{}", e.getMessage());
        }
        return ReturnT.FAIL;
    }

}
