package com.juzifenqi.virtual.core.services.impl.third.zixuan;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.groot.utils.http.OKHttp3SimpleUtils;
import com.juzifenqi.virtual.bean.pojo.VirtualOrders;
import com.juzifenqi.virtual.bean.pojo.VirtualOrdersCallback;
import com.juzifenqi.virtual.bean.pojo.VirtualOrdersDoubt;
import com.juzifenqi.virtual.bean.pojo.VirtualOrdersWorker;
import com.juzifenqi.virtual.bean.pojo.third.zixuan.request.ZxRechargeStatusNotice;
import com.juzifenqi.virtual.bean.vo.RechargeRespVo;
import com.juzifenqi.virtual.bean.vo.VirtualOrdersWorkerVo;
import com.juzifenqi.virtual.component.enums.DateEnum;
import com.juzifenqi.virtual.component.enums.OrderRechargeStatusEnum;
import com.juzifenqi.virtual.component.enums.OrderStatusEnum;
import com.juzifenqi.virtual.component.enums.OrderSubmitStatusEnum;
import com.juzifenqi.virtual.component.enums.RechargeStatusEnum;
import com.juzifenqi.virtual.component.models.VirtualOrdersTrailModel;
import com.juzifenqi.virtual.component.util.DateUtils;
import com.juzifenqi.virtual.component.util.RobotUtil;
import com.juzifenqi.virtual.core.services.impl.third.AbstractStrategyHandler;
import com.juzifenqi.virtual.core.services.impl.third.common.CommonBusHandler;
import com.juzifenqi.virtual.core.services.impl.third.common.MqResultCodeEnum;
import com.juzifenqi.virtual.dao.VirtualOrdersCallbackDao;
import com.juzifenqi.virtual.dao.VirtualOrdersDao;
import com.juzifenqi.virtual.dao.VirtualOrdersDoubtDao;
import com.juzifenqi.virtual.dao.VirtualOrdersWorkerDao;
import java.math.BigDecimal;
import java.security.MessageDigest;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.AbstractEnvironment;
import org.springframework.stereotype.Component;


/**
 * 子轩三方交互工具类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/20 9:38 下午
 */
@Component
@Slf4j
public class ZixuanUtil extends AbstractStrategyHandler {

    @Autowired
    AbstractEnvironment environment;
    @Autowired
    private VirtualOrdersWorkerDao   virtualOrdersWorkerDao;
    @Autowired
    private VirtualOrdersDao         virtualOrdersDao;
    @Autowired
    private VirtualOrdersDoubtDao    virtualOrdersDoubtDao;
    @Autowired
    private VirtualOrdersCallbackDao virtualOrdersCallbackDao;
    @Autowired
    private ZixuanConfig             config;
    @Autowired
    private RobotUtil                robotUtil;
    @Autowired
    private VirtualOrdersTrailModel  ordersTrailModel;
    @Autowired
    private CommonBusHandler         commonBusHandler;


    private static final String ZX_SUCCESS_RESPCODE       = "00";//结果码-成功
    private static final String ZX_UNKNOWN_RESPCODE       = "-99";//结果码-存疑
    private static final String RECHARGE_SUCCESS_RESPCODE = "2";//查证状态码-2-充值成功
    private static final String RECHARGE_FAIL_RESPCODE    = "3";//查证状态码-3-充值失败
    private static final String RECHARGE_MIDDLE_RESPCODE  = "1";//查证状态码-1-处理中
    private static final String RECHARGE_UNKNOWN_RESPCODE = "4";//查证状态码-4-未查询到订单


    public static final Integer MAX_RETRY_QUERY_TIMES = 10;//最大查证次数

    public static final String DING_WARNING_BALANCE = "20000";//钉钉告警阈值


    public static final String CALLBACK_RSP_SUCCESS = "ok";//子轩要求的回调通知成功响应
    public static final String CALLBACK_RSP_FAIL    = "fail";//自定义的失败响应,非ok即可


    /**
     * 把数组所有元素排序，并按照“参数=参数值”的模式用“&”字符拼接成字符串
     *
     * @param params 需要排序并参与字符拼接的参数
     * @return 拼接后字符串
     */
    public static String createLinkString(Map params) {

        List<String> keys = new ArrayList<>(params.keySet());
        Collections.sort(keys);

        String prestr = "";

        for (int i = 0; i < keys.size(); i++) {
            String key = keys.get(i);
            String value = (String) params.get(key);

            if (i == keys.size() - 1) {//拼接时，不包括最后一个&字符
                prestr = prestr + key + "=" + value;
            } else {
                prestr = prestr + key + "=" + value + "&";
            }
        }

        return prestr;
    }

    /**
     * MD5指纹算法
     */
    public static String md5(String str) {
        if (str == null) {
            return null;
        }

        try {
            MessageDigest messageDigest = MessageDigest.getInstance("MD5");
            messageDigest.update(str.getBytes());
            return bytesToHexString(messageDigest.digest());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }

    /**
     * 将二进制转换成16进制
     */
    public static String bytesToHexString(byte[] src) {
        StringBuilder stringBuilder = new StringBuilder("");
        if (src == null || src.length <= 0) {
            return null;
        }
        for (int i = 0; i < src.length; i++) {
            int v = src[i] & 0xFF;
            String hv = Integer.toHexString(v);
            if (hv.length() < 2) {
                stringBuilder.append(0);
            }
            stringBuilder.append(hv);
        }
        return stringBuilder.toString();
    }


    /**
     * 充值下单
     */
    @Override
    public RechargeRespVo orderRecharge(VirtualOrdersWorkerVo paramVo) {
        log.info("子轩-调用三方冲充值：param:{}", JSON.toJSONString(paramVo));
        long start = System.currentTimeMillis();
        VirtualOrders order = paramVo.getVirtualOrder();
        String timestamp = DateUtils.dateToStr(new Date(), DateEnum.DATE_BANK_SEQ_MILL);
        HashMap<String, Object> params = new HashMap<>();
        params.put("appId", config.getAppId());
        params.put("appSecret", config.getAppSecret());
        params.put("outOrderId", order.getOrderSn());
        //刚上线有可能用户已经通过手机号下单-兼容
        params.put("uuid", StringUtils.isEmpty(order.getMobile()) ? order.getRechargeAccount()
                : order.getMobile());
        String env = environment.getProperty("spring.profiles.active");

        if ("prd".equals(env)) {
            params.put("itemId", order.getSupplierItemId());
            //params.put("itemPrice", order.getSupplierMoney().setScale(3).toPlainString());
            // 暂定不传了
        } else {
            params.put("itemId", "100001");//直充测试商品100001
            //params.put("itemPrice", "1.000");//直充测试商品价格1元
        }
        params.put("amount", "1");
        params.put("callbackUrl", config.getCallbackUrl());
        params.put("timestamp", timestamp);
        String signStr = createLinkString(params);
        log.info("订单:{},排序拼接参数signStr:{}", order.getOrderSn(), signStr);
        String sign = md5(signStr);
        log.info("订单:{},md5签名值:{}", order.getOrderSn(), sign);
        params.put("sign", sign);
        params.remove("appSecret");//appSecret只在签名时使用，请不要作为参数进行网络传输。
        log.info("子轩下单开始,订单:{}", order.getOrderSn());

        Boolean flag = false;//下单成功统一处理
        String supplierOrderSn = null;//子轩订单号
        try {
            String methodUrl = config.getUrl() + "/api/order/submit";
            log.info("======调取子轩接口：直充下单接口===开始," + "url={}, param={}", methodUrl,
                    JSONObject.toJSONString(params));
            JSONObject resultJson = OKHttp3SimpleUtils.postByForm(methodUrl, params);
            log.info("======调取子轩接口：直充下单接口===返回结果,result={}",
                    resultJson.toJSONString());
            JSONObject jsonObject = resultJson.getJSONObject("responseVal");

            if (ZX_SUCCESS_RESPCODE.equals(jsonObject.getString("code"))
                    || ZX_UNKNOWN_RESPCODE.equals(jsonObject.getString("code"))) {//下单成功
                flag = true;
                supplierOrderSn = jsonObject.getString("orderId");//记录下子轩订单号
            } else {//下单失败
                String msg = jsonObject.getString("msg");

                VirtualOrdersWorker worker = new VirtualOrdersWorker();
                worker.setId(paramVo.getId());
                worker.setOrderSubmitStatus(OrderSubmitStatusEnum.ORDER_FAIL.getCode());
                worker.setOrderSubmitTime(new Date());
                worker.setRemark("子轩下单失败:" + msg);
                virtualOrdersWorkerDao.updateVirtualOrdersWorkerById(worker);

                //更新主表订单状态
                VirtualOrders virtualOrders = new VirtualOrders();
                virtualOrders.setId(order.getId());
                virtualOrders.setOrderStatus(OrderStatusEnum.STATUS_ORDER_FAIL.getCode());
                virtualOrders.setRemark(OrderStatusEnum.STATUS_ORDER_FAIL.getDesc());
                // 20231110 zjf 充值状态赋值
                virtualOrders.setRechargeStatus(RechargeStatusEnum.RECHARGE_FAIL.getCode());
                virtualOrdersDao.updateVirtualOrdersById(virtualOrders);

                // 是否0元订单
                boolean zeroOrder = order.getOrderMoney().compareTo(BigDecimal.ZERO) == 0;
                String remark = zeroOrder ? "下单失败,0元订单不退款" : "下单失败";
                //记录订单流转状态
                ordersTrailModel.saveOrdersTrail(order.getOrderSn(),
                        OrderStatusEnum.STATUS_ORDER_NO_PUSH.getCode(),
                        OrderStatusEnum.STATUS_ORDER_FAIL.getCode(), remark);

                log.info("子轩下单失败,订单:{},详情:{}", order.getOrderSn(), msg);
                // 0元订单不调用订单中心接口
                if (!zeroOrder) {
                    commonBusHandler.cancelOrder(order, "子轩下单失败");
                }
            }
        } catch (Exception e) {//接口请求异常,一般是超时 //SocketTimeoutException
            log.info("子轩下单发生异常,订单:{},详情:{}", order.getOrderSn(), e);
            flag = true;
        }

        if (flag) {
            //更新子表订单状态
            VirtualOrdersWorker worker = new VirtualOrdersWorker();
            worker.setId(paramVo.getId());
            worker.setOrderSubmitStatus(OrderSubmitStatusEnum.ORDER_SUCCESS.getCode());
            worker.setOrderSubmitTime(new Date());
            worker.setOrderRechargeStatus(OrderRechargeStatusEnum.RECHARGE_NO_VERIFY.getCode());
            worker.setRemark(StringUtils.isNotBlank(supplierOrderSn) ? "子轩下单成功"
                    : "子轩接口请求超时,视作下单成功");
            virtualOrdersWorkerDao.updateVirtualOrdersWorkerById(worker);

            //更新主表订单状态
            VirtualOrders virtualOrders = new VirtualOrders();
            virtualOrders.setId(order.getId());
            virtualOrders.setOrderStatus(OrderStatusEnum.STATUS_ORDER_SUCCESS.getCode());
            virtualOrders.setRemark(OrderStatusEnum.STATUS_ORDER_SUCCESS.getDesc());
            virtualOrders.setSupplierOrderSn(supplierOrderSn);
            virtualOrdersDao.updateVirtualOrdersById(virtualOrders);

            //记录订单流转状态
            ordersTrailModel.saveOrdersTrail(order.getOrderSn(),
                    OrderStatusEnum.STATUS_ORDER_NO_PUSH.getCode(),
                    OrderStatusEnum.STATUS_ORDER_SUCCESS.getCode(), "下单成功");

            log.info("子轩下单成功,订单:{}", order.getOrderSn());

        }
        long end = System.currentTimeMillis();
        log.info("=====>job1处理一笔订单耗时: {} ms", end - start);
        return null;
    }

    /**
     * 充值结果查证
     */
    @Override
    public RechargeRespVo getRechargeStatus(VirtualOrdersWorkerVo paramVo) {
        log.info("子轩-充值结果查证：param:{}", JSON.toJSONString(paramVo));
        long start = System.currentTimeMillis();
        VirtualOrders order = paramVo.getVirtualOrder();
        String timestamp = DateUtils.dateToStr(new Date(), DateEnum.DATE_BANK_SEQ_MILL);
        HashMap<String, Object> params = new HashMap<>();
        params.put("appId", config.getAppId());
        params.put("appSecret", config.getAppSecret());
        if (StringUtils.isNotBlank(order.getSupplierOrderSn())) {
            params.put("orderId", order.getSupplierOrderSn());//可能为空
        }
        params.put("outOrderId", order.getOrderSn());
        params.put("timestamp", timestamp);
        String signStr = createLinkString(params);
        log.info("订单:{},排序拼接参数signStr:{}", order.getOrderSn(), signStr);
        String sign = md5(signStr);
        log.info("订单:{},md5签名值:{}", order.getOrderSn(), sign);
        params.put("sign", sign);
        params.remove("appSecret");

        log.info("子轩充值状态查证开始,订单:{}", order.getOrderSn());

        try {
            String methodUrl = config.getUrl() + "/api/order/query";
            log.info("======调取子轩接口：订单查询接口===开始," + "url={}, param={}", methodUrl,
                    JSONObject.toJSONString(params));
            JSONObject resultJson = OKHttp3SimpleUtils.postByForm(methodUrl, params);
            log.info("======调取子轩接口：订单查询接口===返回结果,result={}",
                    resultJson.toJSONString());
            JSONObject jsonObject = resultJson.getJSONObject("responseVal");

            if (ZX_SUCCESS_RESPCODE.equals(jsonObject.getString("code"))) {
                if (RECHARGE_SUCCESS_RESPCODE.equals(jsonObject.getString("orderStatus"))) {//充值成功
                    //更新子表订单状态
                    VirtualOrdersWorker worker = new VirtualOrdersWorker();
                    worker.setId(paramVo.getId());
                    worker.setOrderRechargeStatus(
                            OrderRechargeStatusEnum.RECHARGE_SUCCESS.getCode());
                    worker.setOrderRechargeTime(new Date());
                    worker.setRemark("子轩主动查证充值成功");
                    virtualOrdersWorkerDao.updateVirtualOrdersWorkerById(worker);

                    //更新主表订单状态
                    VirtualOrders virtualOrders = new VirtualOrders();
                    virtualOrders.setId(order.getId());
                    virtualOrders.setOrderStatus(OrderStatusEnum.STATUS_RECHARGE_SUCCESS.getCode());
                    virtualOrders.setRemark(OrderStatusEnum.STATUS_RECHARGE_SUCCESS.getDesc());
                    // 20231110 zjf 充值状态赋值
                    virtualOrders.setRechargeStatus(RechargeStatusEnum.RECHARGE_SUCCESS.getCode());
                    virtualOrdersDao.updateVirtualOrdersById(virtualOrders);

                    //记录订单流转状态
                    ordersTrailModel.saveOrdersTrail(order.getOrderSn(),
                            OrderStatusEnum.STATUS_ORDER_SUCCESS.getCode(),
                            OrderStatusEnum.STATUS_RECHARGE_SUCCESS.getCode(), "主动查证充值成功");

                    log.info("子轩主动查证充值成功,订单:{}", order.getOrderSn());
                    // 20230508 zjf 推送充值成功mq
                    commonBusHandler.sendRechargeResultMq(order, MqResultCodeEnum.SUCCESS);
                }
                boolean isFail = RECHARGE_FAIL_RESPCODE.equals(jsonObject.getString("orderStatus"));
                boolean notExist = RECHARGE_UNKNOWN_RESPCODE.equals(jsonObject.getString("orderStatus"))
                        && paramVo.getOrderSubmitTime() != null
                        && paramVo.getOrderSubmitTime().before(DateUtils.minusMinutes(new Date(), 6));
                if (isFail || notExist) {//充值失败或订单不存在
                    String orderDesc = jsonObject.getString("orderDesc");
                    //更新子表订单状态
                    VirtualOrdersWorker worker = new VirtualOrdersWorker();
                    worker.setId(paramVo.getId());
                    worker.setOrderRechargeStatus(OrderRechargeStatusEnum.RECHARGE_FAIL.getCode());
                    worker.setOrderRechargeTime(new Date());
                    worker.setRemark("子轩主动查证充值失败:" + orderDesc);
                    virtualOrdersWorkerDao.updateVirtualOrdersWorkerById(worker);

                    //更新主表订单状态
                    VirtualOrders virtualOrders = new VirtualOrders();
                    virtualOrders.setId(order.getId());
                    virtualOrders.setOrderStatus(OrderStatusEnum.STATUS_RECHARGE_FAIL.getCode());
                    virtualOrders.setRemark(OrderStatusEnum.STATUS_RECHARGE_FAIL.getDesc());
                    // 20231110 zjf 充值状态赋值
                    virtualOrders.setRechargeStatus(RechargeStatusEnum.RECHARGE_FAIL.getCode());
                    virtualOrdersDao.updateVirtualOrdersById(virtualOrders);

                    // 是否0元订单
                    boolean zeroOrder = order.getOrderMoney().compareTo(BigDecimal.ZERO) == 0;
                    String remark =
                            zeroOrder ? "主动查证充值失败,0元订单不退款" : "主动查证充值失败";
                    //记录订单流转状态
                    ordersTrailModel.saveOrdersTrail(order.getOrderSn(),
                            OrderStatusEnum.STATUS_ORDER_SUCCESS.getCode(),
                            OrderStatusEnum.STATUS_RECHARGE_FAIL.getCode(), remark);

                    log.info("子轩主动查证充值失败,订单:{}", order.getOrderSn());

                    // 0元订单不调用订单中心接口
                    if (!zeroOrder) {
                        commonBusHandler.cancelOrder(order, "子轩主动查证充值失败");
                    }
                    // 20230508 zjf 推送充值失败mq
                    commonBusHandler.sendRechargeResultMq(order, MqResultCodeEnum.FAIL);
                }
            } else { //没有明确结果的,等待下一次job继续处理,直到达到得到明确结果||达到阈值标记存疑
                if (paramVo.getGetRetryCount() + 1 == MAX_RETRY_QUERY_TIMES) {//存疑处理
                    String orderDesc = jsonObject.getString("orderDesc");
                    //更新子表订单状态
                    VirtualOrdersWorker worker = new VirtualOrdersWorker();
                    worker.setId(paramVo.getId());
                    worker.setOrderRechargeStatus(OrderRechargeStatusEnum.RECHARGE_FAIL.getCode());
                    worker.setOrderRechargeTime(new Date());
                    worker.setGetRetryCount(MAX_RETRY_QUERY_TIMES);
                    worker.setRemark("子轩充值结果存疑" + orderDesc);
                    virtualOrdersWorkerDao.updateVirtualOrdersWorkerById(worker);

                    //更新主表订单状态
                    VirtualOrders virtualOrders = new VirtualOrders();
                    virtualOrders.setId(order.getId());
                    virtualOrders.setOrderStatus(OrderStatusEnum.STATUS_RECHARGE_FAIL.getCode());
                    virtualOrders.setRemark(OrderStatusEnum.STATUS_RECHARGE_FAIL.getDesc());
                    // 20231110 zjf 充值状态赋值
                    virtualOrders.setRechargeStatus(RechargeStatusEnum.RECHARGE_FAIL.getCode());
                    virtualOrdersDao.updateVirtualOrdersById(virtualOrders);

                    // 是否0元订单
                    boolean zeroOrder = order.getOrderMoney().compareTo(BigDecimal.ZERO) == 0;
                    String remark = zeroOrder ? "充值结果存疑,0元订单不退款" : "充值结果存疑";
                    //记录订单流转状态
                    ordersTrailModel.saveOrdersTrail(order.getOrderSn(),
                            OrderStatusEnum.STATUS_ORDER_SUCCESS.getCode(),
                            OrderStatusEnum.STATUS_RECHARGE_UNKNOWN.getCode(), remark);

                    //保存存疑订单
                    VirtualOrdersDoubt virtualOrdersDoubt = new VirtualOrdersDoubt();
                    virtualOrdersDoubt.setUserId(order.getUserId());
                    virtualOrdersDoubt.setChannelId(order.getChannelId());
                    virtualOrdersDoubt.setOrderSn(order.getOrderSn());
                    virtualOrdersDoubt.setReason(orderDesc);
                    virtualOrdersDoubt.setCreateTime(new Date());
                    virtualOrdersDoubtDao.saveVirtualOrdersDoubt(virtualOrdersDoubt);

                    // 20230508 zjf 推送充值失败mq
                    commonBusHandler.sendRechargeResultMq(order, MqResultCodeEnum.FAIL);
                    // 20230817 zjf 存疑订单自动退款
                    // 0元订单不调用订单中心接口
                    if (!zeroOrder) {
                        commonBusHandler.cancelOrder(order, "子轩主动查证充值存疑");
                    }
                    log.info("子轩充值结果存疑,订单:{},详情:{}", order.getOrderSn(), orderDesc);
                    //钉钉告警提示存疑订单
                    String msg =
                            "子轩充值结果存疑,订单:" + order.getOrderSn() + ",详情:" + orderDesc;
                    robotUtil.pushMsg(msg);
                } else {
                    VirtualOrdersWorker worker = new VirtualOrdersWorker();
                    worker.setId(paramVo.getId());
                    worker.setGetRetryCount(paramVo.getGetRetryCount() + 1);
                    worker.setRemark("子轩充值还未有明确结果,等待下次调度重试");
                    virtualOrdersWorkerDao.updateVirtualOrdersWorkerById(worker);
                    log.info("子轩充值还未有明确结果,订单:{},第{}次查证重试", order.getOrderSn(),
                            worker.getGetRetryCount());
                    return null;
                }
            }
        } catch (Exception e) {
            log.info("子轩充值状态查证发生异常,订单:{},详情:{}", order.getOrderSn(),
                    e.getMessage());//用户订单退款失败钉钉告警,不回滚状态
        }
        long end = System.currentTimeMillis();
        log.info("=====>job2处理一笔订单耗时: {} ms", end - start);
        return null;
    }

    /**
     * 查询桔子在子轩余额
     */
    public void getZxBalance() {

        String timestamp = DateUtils.dateToStr(new Date(), DateEnum.DATE_BANK_SEQ_MILL);
        HashMap<String, Object> params = new HashMap<>();
        params.put("appId", config.getAppId());
        params.put("appSecret", config.getAppSecret());
        params.put("timestamp", timestamp);
        String signStr = createLinkString(params);
        String sign = md5(signStr);
        params.put("sign", sign);
        params.remove("appSecret");

        log.info("查询桔子在子轩余额任务开始:{}",
                DateUtils.dateToStr(new Date(), DateEnum.DATE_FORMAT));

        try {
            String methodUrl = config.getUrl() + "/api/account/balance";
            log.info("======调取子轩接口：余额查询接口===开始," + "url={}, param={}", methodUrl,
                    JSONObject.toJSONString(params));
            JSONObject resultJson = OKHttp3SimpleUtils.postByForm(methodUrl, params);
            log.info("======调取子轩接口：余额查询接口===返回结果,result={}",
                    resultJson.toJSONString());
            JSONObject jsonObject = resultJson.getJSONObject("responseVal");

            if (ZX_SUCCESS_RESPCODE.equals(jsonObject.getString("code")) && StringUtils.isNotBlank(
                    jsonObject.getString("balance"))) {
                String balance = jsonObject.getString("balance");
                BigDecimal restBalance = new BigDecimal(balance);
                BigDecimal dingBalance = new BigDecimal(DING_WARNING_BALANCE);
                if (restBalance.compareTo(dingBalance) < 0) {
                    String msg = "桔子在子轩当前余额小于" + DING_WARNING_BALANCE + ",日期:"
                            + DateUtils.dateToStr(new Date(), DateEnum.DATE_FORMAT)
                            + ",请产品运营人员及时处理！";
                    robotUtil.pushBusMsg(msg);
                }
            } else {
                log.info("桔子余额查询失败,日期:{},详情:{}",
                        DateUtils.dateToStr(new Date(), DateEnum.DATE_FORMAT), resultJson);
                String msg =
                        "桔子余额查询失败" + DING_WARNING_BALANCE + ",日期:" + DateUtils.dateToStr(
                                new Date(), DateEnum.DATE_FORMAT);
                robotUtil.pushBusMsg(msg);
            }
        } catch (Exception e) {
            log.info("查询桔子在子轩余额发生异常,日期:{},详情:{}",
                    DateUtils.dateToStr(new Date(), DateEnum.DATE_FORMAT), e.getMessage());
            String msg = "查询桔子在子轩余额发生异常" + DING_WARNING_BALANCE + ",日期:"
                    + DateUtils.dateToStr(new Date(), DateEnum.DATE_FORMAT);
            robotUtil.pushBusMsg(msg);
        }
    }

    /**
     * 回调处理
     */
    public String rechargeStatusNotice(ZxRechargeStatusNotice statusNotice) {
        log.info("子轩回调通知订单结果开始:{},日期:{}", JSONObject.toJSONString(statusNotice),
                DateUtils.dateToStr(new Date(), DateEnum.DATE_FORMAT));
        if (!config.getAppId().equals(statusNotice.getAppId())) {
            log.info("子轩回调appid不匹配！");
            return CALLBACK_RSP_FAIL;
        }
        if (StringUtils.isBlank(statusNotice.getOutOrderId()) || StringUtils.isBlank(
                statusNotice.getOrderId()) || StringUtils.isBlank(statusNotice.getOrderStatus())) {
            log.info("子轩回调通知订单结果失败-请求参数不合法,必填字段");
            return CALLBACK_RSP_FAIL;
        }
        VirtualOrdersWorker virtualOrdersWorker = virtualOrdersWorkerDao.getVirtualOrdersWorkerByOrderSn(
                statusNotice.getOutOrderId());
        if (virtualOrdersWorker == null) {
            log.info("子轩回调通知订单结果失败-订单不存在,传参桔子订单号:{}",
                    statusNotice.getOutOrderId());
            return CALLBACK_RSP_FAIL;
        }
        //主动查证已经有结果了
        if (virtualOrdersWorker.getOrderRechargeStatus() != 0) {
            log.info("子轩主动查证已经有结果了,不处理回调,订单号:{}", statusNotice.getOutOrderId());
            orderCallback(statusNotice, CALLBACK_RSP_SUCCESS, "主动查证已经有结果");
            return CALLBACK_RSP_SUCCESS;
        }
        String dealStatus = dealStatusNotice(statusNotice, virtualOrdersWorker.getId());
        orderCallback(statusNotice, dealStatus, null);
        return dealStatus;
    }

    private String dealStatusNotice(ZxRechargeStatusNotice statusNotice, Integer workerId) {
        {
            boolean success = RECHARGE_SUCCESS_RESPCODE.equals(statusNotice.getOrderStatus());
            VirtualOrders order = virtualOrdersDao.getVirtualOrdersByOrderSn(
                    statusNotice.getOutOrderId());
            //更新任务表订单状态
            VirtualOrdersWorker worker = new VirtualOrdersWorker();
            worker.setId(workerId);
            worker.setOrderRechargeStatus(
                    success ? OrderRechargeStatusEnum.RECHARGE_SUCCESS.getCode()
                            : OrderRechargeStatusEnum.RECHARGE_FAIL.getCode());
            worker.setOrderRechargeTime(new Date());
            worker.setRemark(success ? "子轩回调通知充值成功"
                    : "子轩回调通知充值失败-" + statusNotice.getOrderDesc());
            virtualOrdersWorkerDao.updateVirtualOrdersWorkerById(worker);
            //更新主表订单状态
            VirtualOrders virtualOrders = new VirtualOrders();
            virtualOrders.setId(order.getId());
            virtualOrders.setOrderStatus(success ? OrderStatusEnum.STATUS_RECHARGE_SUCCESS.getCode()
                    : OrderStatusEnum.STATUS_RECHARGE_FAIL.getCode());
            virtualOrders.setRemark(success ? OrderStatusEnum.STATUS_RECHARGE_SUCCESS.getDesc()
                    : OrderStatusEnum.STATUS_RECHARGE_FAIL.getDesc());
            // 20231110 zjf 充值状态赋值
            virtualOrders.setRechargeStatus(success ? RechargeStatusEnum.RECHARGE_SUCCESS.getCode()
                    : RechargeStatusEnum.RECHARGE_FAIL.getCode());
            virtualOrdersDao.updateVirtualOrdersById(virtualOrders);
            //记录订单流转状态
            ordersTrailModel.saveOrdersTrail(order.getOrderSn(),
                    OrderStatusEnum.STATUS_ORDER_SUCCESS.getCode(),
                    success ? OrderStatusEnum.STATUS_RECHARGE_SUCCESS.getCode()
                            : OrderStatusEnum.STATUS_RECHARGE_FAIL.getCode(),
                    success ? "子轩回调通知充值成功" : "子轩回调通知充值失败");
            log.info("处理子轩回调通知 {} ,订单:{}",
                    success ? "子轩回调通知充值成功" : "子轩回调通知充值失败", order.getOrderSn());
            if (!success) {//充值失败用户订单退款
                // 是否0元订单
                boolean zeroOrder = order.getOrderMoney().compareTo(BigDecimal.ZERO) == 0;
                // 充值失败用户订单退款 0元订单不调用订单中心接口
                if (!zeroOrder) {
                    commonBusHandler.cancelOrder(order, "子轩主动查证充值失败");
                }
            }
            // 20230508 zjf 发送子轩充值结果mq
            commonBusHandler.sendRechargeResultMq(order,
                    success ? MqResultCodeEnum.SUCCESS : MqResultCodeEnum.FAIL);
            return CALLBACK_RSP_SUCCESS;
        }
    }

    /**
     * 记录子轩回调状态
     *
     * <AUTHOR>
     * @version 1.0
     * @date 2022/1/22 2:59 下午
     */
    private void orderCallback(ZxRechargeStatusNotice statusNotice, String response,
            String remark) {
        //记录回调结果
        VirtualOrdersCallback virtualOrdersCallback = new VirtualOrdersCallback();
        virtualOrdersCallback.setOrderSn(statusNotice.getOutOrderId());
        virtualOrdersCallback.setSupplierOrderSn(statusNotice.getOrderId());
        virtualOrdersCallback.setCallbackOrderStatus(statusNotice.getOrderStatus());
        virtualOrdersCallback.setCallbackOrderDesc(statusNotice.getOrderDesc());
        virtualOrdersCallback.setResponse(response);
        virtualOrdersCallback.setRemark(remark);
        virtualOrdersCallbackDao.saveVirtualOrdersCallback(virtualOrdersCallback);
    }

}
