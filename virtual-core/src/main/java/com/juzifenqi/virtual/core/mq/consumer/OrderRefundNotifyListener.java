package com.juzifenqi.virtual.core.mq.consumer;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.bean.ConsumerBean;
import com.groot.utils.exception.LogUtil;
import com.juzifenqi.mq.consumer.normal.NormalConsumerClient;
import com.juzifenqi.virtual.bean.dto.OrderRefundNotifyDto;
import com.juzifenqi.virtual.bean.pojo.VirtualOrderRefundInfo;
import com.juzifenqi.virtual.component.enums.RefundInfoStateEnum;
import com.juzifenqi.virtual.component.enums.RefundStateEnum;
import com.juzifenqi.virtual.component.exception.VirtualException;
import com.juzifenqi.virtual.component.feishu.FeiShuModel;
import com.juzifenqi.virtual.core.common.Constant;
import com.juzifenqi.virtual.core.conf.VirtualMqConfig;
import com.juzifenqi.virtual.dao.VirtualOrderRefundInfoDao;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.Objects;
import javax.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

/**
 * 订单中心退款通知,以下两种退款方式 1.原路退款包括原路退款、原卡代付退款(支付自己处理) 2.换卡代付退款
 *
 * <AUTHOR>
 * @date 2024/9/2 15:49
 */
@Slf4j
@Configuration
public class OrderRefundNotifyListener extends NormalConsumerClient {

    @Autowired
    private VirtualMqConfig mqConfig;
    @Autowired
    private FeiShuModel     feiShuModel;
    @Autowired
    private VirtualOrderRefundInfoDao orderRefundInfoDao;

    @PostConstruct
    public ConsumerBean initOrderRefundNotifyListener() {
        return initConsumer(mqConfig.getGidOrderRefund(), mqConfig.getTopicOrderRefund(),
                Constant.PAY_SOURCE_VIRTUAL);
    }

    @Override
    protected boolean dealMessage(Message message, ConsumeContext consumeContext) {
        try {
            String body = new String(message.getBody(), StandardCharsets.UTF_8);
            log.info("订单中心退款通知内容：{}", body);
            OrderRefundNotifyDto entity = JSONObject.parseObject(body, OrderRefundNotifyDto.class);
            // 校验参数
            orderRefundNotifyValidator(entity);
            // 修改权益订单退款信息
            VirtualOrderRefundInfo updateRefundInfo = buildVirtualOrderRefundInfo(entity);
            orderRefundInfoDao.updateById(updateRefundInfo);
            // 退款失败报警
            if (StringUtils.equals(RefundStateEnum.F.getCode(), entity.getStatus())) {
                feiShuModel.sendSysTextMsg(
                        "虚拟权益充值失败/存疑自动退款失败,权益订单号：" + entity.getOrderId());
            }
            return true;
        } catch (Exception e) {
            LogUtil.printLog("订单中心退款通知异常", e);
            // 因业务异常导致取消失败,无需重试
            return e instanceof VirtualException;
        }
    }

    /**
     * 组装修改退款数据
     */
    private VirtualOrderRefundInfo buildVirtualOrderRefundInfo(OrderRefundNotifyDto entity) {
        // 根据退款业务流水号查询
        String thirdPayNum = entity.getThirdPayNum();
        VirtualOrderRefundInfo refundInfo = orderRefundInfoDao.getByRefundSerialNo(thirdPayNum);
        if (refundInfo == null) {
            throw new VirtualException("处理订单中心退款通知,虚拟权益订单退款信息不存在");
        }
        if (!Objects.equals(refundInfo.getRefundState(), RefundInfoStateEnum.DOING.getCode())) {
            throw new VirtualException("处理订单中心退款通知,虚拟权益订单退款信息非处理中,不处理");
        }
        // 更新虚拟权益订单退款信息
        String status = entity.getStatus();
        Integer refundState =
                RefundStateEnum.S.getCode().equals(status) ? RefundInfoStateEnum.SUCCESS.getCode()
                        : RefundInfoStateEnum.FAIL.getCode();
        VirtualOrderRefundInfo updateRefundInfo = new VirtualOrderRefundInfo();
        updateRefundInfo.setId(refundInfo.getId());
        updateRefundInfo.setRefundState(refundState);
        updateRefundInfo.setPaySerialNo(entity.getSerialNumber());
        updateRefundInfo.setPayCallbackTime(new Date());
        return updateRefundInfo;
    }

    /**
     * 订单中心退款通知校验
     */
    private void orderRefundNotifyValidator(OrderRefundNotifyDto entity) {
        if (entity == null) {
            throw new VirtualException("订单中心退款通知参数异常");
        }
        String thirdPayNum = entity.getThirdPayNum();
        String status = entity.getStatus();
        String plusOrderSn = entity.getOrderId();
        if (StringUtils.isAnyBlank(thirdPayNum, status, plusOrderSn)) {
            throw new VirtualException("订单中心退款通知缺少参数");
        }
        // 退款状态判断
        if (!RefundStateEnum.S.getCode().equals(status) && !RefundStateEnum.F.getCode()
                .equals(status)) {
            throw new VirtualException("处理订单中心退款通知,退款状态未知");
        }
    }
}
