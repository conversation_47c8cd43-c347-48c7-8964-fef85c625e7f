package com.juzifenqi.virtual.core.mq.producer;

import com.alibaba.fastjson.JSONObject;
import com.groot.utils.exception.LogUtil;
import com.juzi.smsgroup.vo.SmsMsgV2Dto;
import com.juzifenqi.mq.producer.normal.AbstractNormalMqClient;
import com.juzifenqi.virtual.core.conf.VirtualMqConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * mq发送
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/5/8 18:59
 */
@Component
@Slf4j
public class MqProducer extends AbstractNormalMqClient {

    @Autowired
    private VirtualMqConfig mqConfig;

    /**
     * 发送充值结果
     */
    public void sendRechargeResult(String message) {
        try {
            log.info("发送充值结果 message:{}", message);
            sendMessage(message, mqConfig.getVirtualResultTopic(), "*");
        } catch (Exception e) {
            LogUtil.printLog(e, "发送子轩充值结果MQ异常");
        }
    }

    /**
     * 发送短信
     */
    public void sendPhoneMsg(SmsMsgV2Dto dto) {
        try {
            String message = JSONObject.toJSONString(dto);
            log.info("发送短信 message:{}", message);
            sendMessage(message, mqConfig.getTopicSendMsg(), "*");
        } catch (Exception e) {
            LogUtil.printLog(e, "发送短信MQ异常");
        }
    }
}
