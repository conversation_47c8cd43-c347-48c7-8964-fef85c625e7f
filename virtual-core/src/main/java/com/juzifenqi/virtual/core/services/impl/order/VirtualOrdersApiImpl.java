package com.juzifenqi.virtual.core.services.impl.order;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.juzifenqi.member.entity.member.MemberInfo;
import com.juzifenqi.virtual.api.order.VirtualOrdersApi;
import com.juzifenqi.virtual.bean.bo.VirtualOrdersBo;
import com.juzifenqi.virtual.bean.pojo.VirtualGoods;
import com.juzifenqi.virtual.bean.pojo.VirtualOrders;
import com.juzifenqi.virtual.bean.pojo.VirtualOrdersCard;
import com.juzifenqi.virtual.bean.pojo.VirtualOrdersTrail;
import com.juzifenqi.virtual.bean.pojo.VirtualOrdersWorker;
import com.juzifenqi.virtual.bean.system.VirtualResult;
import com.juzifenqi.virtual.bean.vo.VirtualOrderPayInfoVo;
import com.juzifenqi.virtual.component.enums.OrderStatusEnum;
import com.juzifenqi.virtual.component.enums.OrderSubmitStatusEnum;
import com.juzifenqi.virtual.component.enums.PayStateEnum;
import com.juzifenqi.virtual.component.enums.RechargeStatusEnum;
import com.juzifenqi.virtual.component.enums.RechargeTypeEnum;
import com.juzifenqi.virtual.component.enums.SupplierEnum;
import com.juzifenqi.virtual.component.models.VirtualOrdersModel;
import com.juzifenqi.virtual.component.util.DES3;
import com.juzifenqi.virtual.core.models.VirtualOrderModel;
import com.juzifenqi.virtual.core.services.impl.third.HandlerContext;
import com.juzifenqi.virtual.dao.VirtualGoodsDao;
import com.juzifenqi.virtual.dao.VirtualOrdersCardDao;
import com.juzifenqi.virtual.dao.VirtualOrdersDao;
import com.juzifenqi.virtual.dao.VirtualOrdersTrailDao;
import com.juzifenqi.virtual.dao.VirtualOrdersWorkerDao;
import com.juzifenqi.virtual.manager.MemberCenterModel;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import javax.annotation.Resource;

import com.juzishuke.dss.sdk.service.DssSdkService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

/**
 * 权益结算订单管理
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/21 8:35 AM
 */
@Service
@Slf4j
public class VirtualOrdersApiImpl implements VirtualOrdersApi {

    @Resource
    private VirtualOrdersModel     virtualOrdersModel;
    @Autowired
    private VirtualOrdersDao       virtualOrdersDao;
    @Autowired
    private VirtualGoodsDao        virtualGoodsDao;
    @Autowired
    private VirtualOrdersWorkerDao virtualOrdersWorkerDao;
    @Autowired
    private VirtualOrdersCardDao   virtualOrdersCardDao;
    @Autowired
    private MemberCenterModel      memberCenterModel;
    @Autowired
    private VirtualOrdersTrailDao  trailDao;
    @Autowired
    private HandlerContext         context;
    @Autowired
    private VirtualOrderModel      virtualOrderModel;
    @Autowired
    private DssSdkService          dssSdkService;

    @Override
    public VirtualResult<Integer> countUesdOrdersByOrderSn(String plusOrderSn) {
        log.info("取消会员时判断用户是否已使用权益 plusOrderSn={}", plusOrderSn);
        // 默认生活权益
        Integer count = virtualOrdersModel.countUesdOrdersByOrderSn(plusOrderSn, 5);
        log.info("取消会员时判断用户是否已使用权益 count={}", count);
        VirtualResult<Integer> result = new VirtualResult<>();
        result.success("查询成功", count);
        return result;
    }

    @Override
    public VirtualResult<Integer> countUesdOrdersByOrderSn(String plusOrderSn, Integer modelId) {
        log.info("取消会员时判断用户是否已使用权益 plusOrderSn={},modelId={}", plusOrderSn, modelId);
        Integer count = virtualOrdersModel.countUesdOrdersByOrderSn(plusOrderSn, modelId);
        log.info("取消会员时判断用户是否已使用权益 count={}", count);
        VirtualResult<Integer> result = new VirtualResult<>();
        result.success("查询成功", count);
        return result;
    }

    @Override
    public VirtualResult<VirtualOrders> getVirtualOrdersByOrderSn(String orderSn) {
        log.info("查询权益结算订单orderSn={}", orderSn);
        VirtualOrders virtualOrder = virtualOrdersModel.getVirtualOrdersByOrderSn(orderSn);
        log.info("查询权益结算订单order={}", virtualOrder);
        VirtualResult<VirtualOrders> result = new VirtualResult<>();
        result.success("查询成功", virtualOrder);
        return result;
    }

    @Override
    public VirtualResult<List<VirtualOrders>> countOrderByPlus(String plusOrderSn,
            Integer modelId) {
        log.info("统计期限内通过该会员单号下的订单,plusOrderSn={},modelId={}", plusOrderSn, modelId);
        List<VirtualOrders> orders = virtualOrdersModel.countOrderByPlus(plusOrderSn, modelId);
        log.info("统计期限内通过该会员单号下的订单返回orders={}", JSONObject.toJSONString(orders));
        VirtualResult<List<VirtualOrders>> result = new VirtualResult<>();
        result.success("查询成功", orders);
        return result;
    }

    @Override
    public VirtualResult<List<VirtualOrders>> countOrderByPlus(String plusOrderSn) {
        log.info("统计期限内通过该会员单号下的订单,plusOrderSn={}", plusOrderSn);
        // 默认查生活权益。兼容老接口
        List<VirtualOrders> orders = virtualOrdersModel.countOrderByPlus(plusOrderSn, 5);
        log.info("统计期限内通过该会员单号下的订单返回orders={}", JSONObject.toJSONString(orders));
        VirtualResult<List<VirtualOrders>> result = new VirtualResult<>();
        result.success("查询成功", orders);
        return result;
    }

    @Override
    public VirtualResult<Integer> countProcessingOrder(String plusOrderSn, Integer modelId) {
        log.info("统计当前会员充值中订单数量,plusOrderSn={},modelId={}", plusOrderSn, modelId);
        Integer count = virtualOrdersModel.countProcessingOrder(plusOrderSn, modelId);
        log.info("统计当前会员充值中订单数量返回count={}", count);
        VirtualResult<Integer> result = new VirtualResult<>();
        result.success("查询成功", count);
        return result;
    }

    @Override
    public VirtualResult<Integer> countProcessingOrder(String plusOrderSn) {
        log.info("统计当前会员充值中订单数量,plusOrderSn={}", plusOrderSn);
        // 默认查生活权益。兼容老接口
        Integer count = virtualOrdersModel.countProcessingOrder(plusOrderSn, 5);
        log.info("统计当前会员充值中订单数量返回count={}", count);
        VirtualResult<Integer> result = new VirtualResult<>();
        result.success("查询成功", count);
        return result;
    }

    @Override
    public VirtualResult<List<VirtualOrders>> selectProcessingOrder(String plusOrderSn,
            Integer modelId) {
        log.info("统计当前会员充值中订单列表,plusOrderSn={},modelId={}", plusOrderSn, modelId);
        List<VirtualOrders> list = virtualOrdersModel.selectProcessingOrder(plusOrderSn, modelId);
        log.info("统计当前会员充值中订单列表返回count={}", JSONObject.toJSONString(list));
        VirtualResult<List<VirtualOrders>> result = new VirtualResult<>();
        result.success("查询成功", list);
        return result;
    }

    @Override
    public VirtualResult<List<VirtualOrders>> selectProcessingOrder(String plusOrderSn) {
        log.info("统计当前会员充值中订单列表,plusOrderSn={}", plusOrderSn);
        // 默认查生活权益。兼容老接口
        List<VirtualOrders> list = virtualOrdersModel.selectProcessingOrder(plusOrderSn, 5);
        log.info("统计当前会员充值中订单列表返回count={}", JSONObject.toJSONString(list));
        VirtualResult<List<VirtualOrders>> result = new VirtualResult<>();
        result.success("查询成功", list);
        return result;
    }

    @Override
    public VirtualResult<List<VirtualOrders>> getNeedPayOrder(String plusOrderSn, String productSku,
            Integer modelId) {
        log.info("获取商城下单未支付状态的订单,plusOrderSn={}，sku:{},modelId:{}", plusOrderSn, productSku,
                modelId);
        List<VirtualOrders> orders = virtualOrdersModel.getNeedPayOrder(plusOrderSn, productSku,
                modelId);
        log.info("获取商城下单未支付状态的订单返回orders={}", orders);
        VirtualResult<List<VirtualOrders>> result = new VirtualResult<>();
        result.success("查询成功", orders);
        return result;
    }

    @Override
    public VirtualResult<List<VirtualOrders>> getNeedPayOrder(String plusOrderSn,
            String productSku) {
        log.info("获取商城下单未支付状态的订单,plusOrderSn={}，sku:{}", plusOrderSn, productSku);
        // 默认查生活权益。兼容老接口
        List<VirtualOrders> orders = virtualOrdersModel.getNeedPayOrder(plusOrderSn, productSku, 5);
        log.info("获取商城下单未支付状态的订单返回orders={}", orders);
        VirtualResult<List<VirtualOrders>> result = new VirtualResult<>();
        result.success("查询成功", orders);
        return result;
    }


    @Override
    public VirtualResult<Boolean> ordersSubmit(VirtualOrders virtualOrders) {
        log.info("========会员下单调用virtual传参:{}", JSONObject.toJSONString(virtualOrders));
        VirtualResult<Boolean> result = new VirtualResult();
        if (virtualOrders.getUserId() == null || virtualOrders.getChannelId() == null
                || StringUtils.isBlank(virtualOrders.getOrderSn()) || StringUtils.isBlank(
                virtualOrders.getPlusOrderSn()) || virtualOrders.getProductId() == null
                || virtualOrders.getProfitTypeId() == null || StringUtils.isBlank(
                virtualOrders.getProgramName())) {
            result.error(VirtualResult.ERROR_CODE, "必填参数不能为空");
            return result;
        }
        VirtualGoods virtualGoodsBySku = virtualGoodsDao.getVirtualGoodsBySku(
                virtualOrders.getProductSku());
        if (virtualGoodsBySku == null || virtualGoodsBySku.getOnsaleState() == 0) {
            result.error(VirtualResult.ERROR_CODE, "未查询到sku对应的权益商品或权益商品已经下架");
            return result;
        }
        if (virtualGoodsBySku.getRechargeType() == RechargeTypeEnum.DIRECT.getCode()
                && StringUtils.isBlank(virtualOrders.getRechargeAccount())) {
            result.error(VirtualResult.ERROR_CODE, "直充方式请填写充值账号");
            return result;
        }
        // ******** zjf 兼容上线时前端还没有上线导致modelId为空的情况
        if (virtualOrders.getModelId() == null) {
            log.info("虚拟权益下单接口权益id为空，默认生活权益：{}", virtualOrders.getOrderSn());
            virtualOrders.setModelId(5);
        }
        //补充数据
        virtualOrders.setProductName(virtualGoodsBySku.getProductName());
        virtualOrders.setSupplierName(virtualGoodsBySku.getSupplierName());
        virtualOrders.setSupplierItemId(virtualGoodsBySku.getSupplierItemId());
        virtualOrders.setSupplierId(virtualGoodsBySku.getSupplierId());
        virtualOrders.setSupplierMoney(virtualGoodsBySku.getSupplierItemPrice());
        virtualOrders.setOrderStatus(OrderStatusEnum.STATUS_MALL_NO_PAY.getCode());
        virtualOrders.setRemark(OrderStatusEnum.STATUS_MALL_NO_PAY.getDesc());
        virtualOrders.setRechargeCode(virtualGoodsBySku.getRechargeCode());
        virtualOrders.setRechargeName(virtualGoodsBySku.getRechargeName());
        // 20231109 zjf 增加充值方式赋值
        virtualOrders.setRechargeType(virtualGoodsBySku.getRechargeType());
        virtualOrders.setRechargeTypeName(virtualGoodsBySku.getRechargeTypeName());
        // 20231109 zjf 增加支付状态，充值状态目前为空，等支付成功再赋值
        virtualOrders.setPayStatus(PayStateEnum.WAIT_PAY.getCode());
        // ******** zjf 增加酒店跳转url和类型
        virtualOrders.setSupplierAccessUrl(virtualGoodsBySku.getSupplierAccessUrl());
        virtualOrders.setSupplierAccessType(virtualGoodsBySku.getSupplierAccessType());
        try {
            if (virtualOrders.getRechargeCode() != null && virtualOrders.getRechargeCode() == 1) {
                virtualOrders.setRechargeAccountUuid(dssSdkService.encryptPhone(virtualOrders.getRechargeAccount()));
            } else {
                virtualOrders.setRechargeAccountUuid(virtualOrders.getRechargeAccount());
            }
        } catch (Exception e) {
            log.warn("虚拟权益下单接口,加密手机失败：{}", virtualOrders.getRechargeAccount());
        }
        Integer ret = virtualOrdersDao.saveVirtualOrders(virtualOrders);
        if (ret > 0) {
            result.setResult(true);
            result.success("新增成功", true);
            return result;
        } else {
            result.error(VirtualResult.ERROR_CODE, "新增失败");
            return result;
        }
    }

    @Override
    public VirtualResult<List<VirtualOrders>> querySettlementOrderList(VirtualOrdersBo bo) {
        log.info("查询结算订单列表入参:{}", JSONObject.toJSONString(bo));
        VirtualResult<List<VirtualOrders>> result = new VirtualResult<>();
        // ******** zjf 手机号查询，渠道必传
        if (org.apache.commons.lang3.StringUtils.isNotBlank(bo.getMobile())) {
            MemberInfo memberInfo = memberCenterModel.getMemberByMobile(bo.getMobile(),
                    String.valueOf(bo.getChannelId()));
            if (memberInfo != null) {
                bo.setUserId(memberInfo.getId());
            }
        }
        List<VirtualOrders> ordersList = virtualOrdersDao.getVirtualOrdersListByPage(bo,
                bo.getStartPage(), bo.getPageSize());
        int count = virtualOrdersDao.pageListCount(bo);
        result.success(count, ordersList);
        log.info("结算订单列表返回={}", JSON.toJSONString(result));
        return result;
    }


    @Override
    public VirtualResult<List<Map<String, Object>>> getOrderStatusDic() {
        List<Map<String, Object>> data = OrderStatusEnum.getKeyValue();
        VirtualResult<List<Map<String, Object>>> result = new VirtualResult<>();
        result.success("查询成功", data);
        return result;
    }

    @Override
    public VirtualResult<List<VirtualOrders>> getSettlementOrderList(
            VirtualOrdersBo virtualOrdersBo) {
        log.info("获取商城下单已完成状态的订单={}", JSONObject.toJSON(virtualOrdersBo));
        List<VirtualOrders> orders = virtualOrdersDao.getVirtualOrdersList(virtualOrdersBo);
        log.info("获取商城下单已完成状态的订单返回orders={}", orders);
        VirtualResult<List<VirtualOrders>> result = new VirtualResult<>();
        result.success("查询成功", orders);
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public VirtualResult submitOrder(VirtualOrders orders) {
        log.info("========会员虚拟直接下单无需支付:{}", JSONObject.toJSONString(orders));
        VirtualResult result = new VirtualResult();
        if (orders.getUserId() == null || orders.getChannelId() == null || StringUtils.isBlank(
                orders.getOrderSn()) || StringUtils.isBlank(orders.getPlusOrderSn())
                || orders.getProductId() == null || StringUtils.isBlank(orders.getProgramName())
                || orders.getOrderMoney() == null) {
            result.error(VirtualResult.ERROR_CODE, "必填参数不能为空");
            return result;
        }
        VirtualGoods virtualGoodsBySku = virtualGoodsDao.getVirtualGoodsBySku(
                orders.getProductSku());
        if (virtualGoodsBySku == null || virtualGoodsBySku.getOnsaleState() == 0) {
            result.error(VirtualResult.ERROR_CODE, "未查询到sku对应的权益商品或权益商品已经下架");
            return result;
        }
        if (virtualGoodsBySku.getRechargeType() == RechargeTypeEnum.DIRECT.getCode()
                && StringUtils.isBlank(orders.getRechargeAccount())) {
            result.error(VirtualResult.ERROR_CODE, "直充方式请填写充值账号");
            return result;
        }
        // ******** zjf 兼容上线时前端还没有上线导致modelId为空的情况
        if (orders.getModelId() == null) {
            log.info("虚拟权益下单无需支付接口权益id为空，默认生活权益：{}", orders.getOrderSn());
            orders.setModelId(5);
        }
        //补充数据
        orders.setProductName(virtualGoodsBySku.getProductName());
        orders.setSupplierName(virtualGoodsBySku.getSupplierName());
        orders.setSupplierItemId(virtualGoodsBySku.getSupplierItemId());
        orders.setSupplierId(virtualGoodsBySku.getSupplierId());
        orders.setSupplierMoney(virtualGoodsBySku.getSupplierItemPrice());
        orders.setOrderStatus(OrderStatusEnum.STATUS_ORDER_NO_PUSH.getCode());
        orders.setRemark(OrderStatusEnum.STATUS_ORDER_NO_PUSH.getDesc());
        orders.setRechargeCode(virtualGoodsBySku.getRechargeCode());
        orders.setRechargeName(virtualGoodsBySku.getRechargeName());
        // 20231109 zjf 增加充值方式赋值
        orders.setRechargeType(virtualGoodsBySku.getRechargeType());
        orders.setRechargeTypeName(virtualGoodsBySku.getRechargeTypeName());
        // 20231109 zjf 增加支付状态、充值状态记录
        orders.setPayStatus(PayStateEnum.PAY_SUCCESS.getCode());
        orders.setRechargeStatus(RechargeStatusEnum.RECHARGE_ING.getCode());
        // ******** zjf 增加酒店跳转url和类型
        orders.setSupplierAccessUrl(virtualGoodsBySku.getSupplierAccessUrl());
        orders.setSupplierAccessType(virtualGoodsBySku.getSupplierAccessType());
        try {
            if (orders.getRechargeCode() != null && orders.getRechargeCode() == 1) {
                orders.setRechargeAccountUuid(dssSdkService.encryptPhone(orders.getRechargeAccount()));
            } else {
                orders.setRechargeAccountUuid(orders.getRechargeAccount());
            }
        } catch (Exception e) {
            log.warn("虚拟权益下单无需支付接口,加密手机失败：{}", orders.getRechargeAccount());
        }
        Integer ret = virtualOrdersDao.saveVirtualOrders(orders);
        if (ret > 0) {
            //插入任务记录
            VirtualOrdersWorker virtualOrdersWorker = new VirtualOrdersWorker();
            virtualOrdersWorker.setOrderSn(orders.getOrderSn());
            virtualOrdersWorker.setOrderSubmitStatus(OrderSubmitStatusEnum.ORDER_NO_PUSH.getCode());
            virtualOrdersWorker.setGetRetryCount(0);
            virtualOrdersWorker.setRemark(OrderSubmitStatusEnum.ORDER_NO_PUSH.getDesc());
            virtualOrdersWorker.setCreateTime(new Date());
            virtualOrdersWorkerDao.saveVirtualOrdersWorker(virtualOrdersWorker);
            result.success("新增成功", true);
        } else {
            result.error(VirtualResult.ERROR_CODE, "新增失败");
        }
        return result;
    }

    @Override
    public VirtualResult<List<VirtualOrders>> getListByUser(VirtualOrdersBo bo) {
        VirtualResult<List<VirtualOrders>> result = new VirtualResult<>();
        List<VirtualOrders> ordersList = virtualOrdersModel.getOrdersListByUser(bo);
        result.success("查询成功", ordersList);
        return result;
    }

    @Override
    public VirtualResult<VirtualOrdersCard> getUserCard(VirtualOrdersBo bo) {
        VirtualResult<VirtualOrdersCard> result = new VirtualResult<>();
        if (bo == null || org.apache.commons.lang3.StringUtils.isBlank(bo.getOrderSn())) {
            result.error(VirtualResult.ERROR_CODE, "订单号不能为空");
            return result;
        }
        VirtualOrdersCard virtualOrdersCard = virtualOrdersCardDao.selectByOrderSn(bo.getOrderSn());
        if (virtualOrdersCard == null) {
            result.error(VirtualResult.ERROR_CODE, "无效的订单号，请刷新重试");
            return result;
        }
        VirtualOrders orders = virtualOrdersDao.getByOrderSn(bo.getOrderSn());
        if (orders == null) {
            result.error(VirtualResult.ERROR_CODE, "无效的订单号，无法查看卡密");
            return result;
        }
        if (!Objects.equals(bo.getUserId(), orders.getUserId())) {
            result.error(VirtualResult.ERROR_CODE, "您不能查看别人的卡密哦");
            return result;
        }
        String cardNo = virtualOrdersCard.getCardNo();
        if (org.apache.commons.lang3.StringUtils.isNotBlank(cardNo)) {
            virtualOrdersCard.setCardNo(DES3.decrypt(cardNo));
        }
        String cardPwd = virtualOrdersCard.getCardPwd();
        if (org.apache.commons.lang3.StringUtils.isNotBlank(cardPwd)) {
            virtualOrdersCard.setCardPwd(DES3.decrypt(cardPwd));
        }
        String cardLink = virtualOrdersCard.getCardLink();
        if (org.apache.commons.lang3.StringUtils.isNotBlank(cardLink)) {
            virtualOrdersCard.setCardLink(DES3.decrypt(cardLink));
        }
        result.success("查询成功", virtualOrdersCard);
        return result;
    }

    @Override
    public VirtualResult<List<VirtualOrdersTrail>> getTrailList(String orderSn) {
        VirtualResult<List<VirtualOrdersTrail>> result = new VirtualResult<>();
        List<VirtualOrdersTrail> virtualOrdersTrails = trailDao.selectByOrderSn(orderSn);
        result.success("查询成功", virtualOrdersTrails);
        return result;
    }

    @Override
    public VirtualResult cancelOrdersByPlus(String plusOrderSn) {
        log.info("通过开通会员单号取消充值成功的虚拟权益订单开始 plusOrderSn={}", plusOrderSn);
        // 查询所有充值成功的虚拟权益订单
        // 目前只有橡树 酒店券直充订单的虚拟权益订单才会存在取消
        List<Integer> supplierIds = Arrays.asList(SupplierEnum.SUPPLIER_XS.getCode());
        List<Integer> rechargeTypes = Arrays.asList(RechargeTypeEnum.HOTEL.getCode());
        List<VirtualOrders> virtualOrders = virtualOrdersModel.getRechargeSuccessOrders(plusOrderSn,
                supplierIds, rechargeTypes);
        log.info("获取充值成功的虚拟权益订单 plusOrderSn={} orders={}", plusOrderSn,
                JSON.toJSONString(virtualOrders));
        if (!CollectionUtils.isEmpty(virtualOrders)) {
            virtualOrders.forEach(order -> {
                log.info("取消单个虚拟权益订单开始 orderSn={} supplier={} rechargeType={}", order.getOrderSn(),
                        order.getSupplierName(), order.getRechargeTypeName());
                context.cancelVirtualOrder(order);
                log.info("取消单个虚拟权益订单结束 orderSn={} supplier={} rechargeType={}", order.getOrderSn(),
                        order.getSupplierName(), order.getRechargeTypeName());
            });
        }
        log.info("通过开通会员单号取消充值成功的虚拟权益订单结束 plusOrderSn={}", plusOrderSn);
        VirtualResult result = new VirtualResult();
        result.success("取消成功", true);
        return result;
    }

    @Override
    public VirtualResult<List<VirtualOrders>> getAllOrderList(VirtualOrdersBo virtualOrdersBo) {
        log.info("获取商城下单全部状态的订单={}", JSONObject.toJSON(virtualOrdersBo));
        List<VirtualOrders> orders = virtualOrdersDao.getAllVirtualOrdersList(virtualOrdersBo);
        log.info("获取商城下单全部状态的订单返回orders={}", orders);
        VirtualResult<List<VirtualOrders>> result = new VirtualResult<>();
        result.success("查询成功", orders);
        return result;
    }

    /**
     * 获取支付请求信息
     */
    @Override
    public VirtualResult<VirtualOrderPayInfoVo> getOrderPayInfo(String orderSn) {
        VirtualResult<VirtualOrderPayInfoVo> result = new VirtualResult<>();
        result.success("请求成功", virtualOrderModel.getOrderPayInfo(orderSn));
        return result;
    }
}
