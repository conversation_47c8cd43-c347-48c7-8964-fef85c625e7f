package com.juzifenqi.virtual.core.services.impl.third.xiangshu;

import com.alibaba.cloudapi.sdk.model.ApiResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.groot.utils.exception.LogUtil;
import com.juzifenqi.member.entity.member.MemberInfo;
import com.juzifenqi.plus.api.IMemberPlusQueryApi;
import com.juzifenqi.plus.dto.resp.PlusAbyssResult;
import com.juzifenqi.plus.dto.resp.member.MemberPlusInfoDetailResp;
import com.juzifenqi.virtual.bean.pojo.VirtualOrderSupplierCoupon;
import com.juzifenqi.virtual.bean.pojo.VirtualOrders;
import com.juzifenqi.virtual.bean.pojo.VirtualOrdersCallback;
import com.juzifenqi.virtual.bean.pojo.VirtualOrdersDoubt;
import com.juzifenqi.virtual.bean.pojo.VirtualOrdersWorker;
import com.juzifenqi.virtual.bean.pojo.VirtualSupplierHotelOrder;
import com.juzifenqi.virtual.bean.pojo.VirtualThirdNotify;
import com.juzifenqi.virtual.bean.pojo.third.xiangshu.request.XsHotelOrderCouponInfo;
import com.juzifenqi.virtual.bean.pojo.third.xiangshu.request.XsHotelOrderNotice;
import com.juzifenqi.virtual.bean.pojo.third.xiangshu.request.XsHotelOrderPayInfo;
import com.juzifenqi.virtual.bean.pojo.third.xiangshu.request.XsRechargeStatusNotice;
import com.juzifenqi.virtual.bean.system.Response;
import com.juzifenqi.virtual.bean.vo.HotelOrderDetailVo;
import com.juzifenqi.virtual.bean.vo.HotelUrlVo;
import com.juzifenqi.virtual.bean.vo.RechargeParamVo;
import com.juzifenqi.virtual.bean.vo.RechargeRespVo;
import com.juzifenqi.virtual.bean.vo.VirtualOrderRechargeSuccVo;
import com.juzifenqi.virtual.bean.vo.VirtualOrdersWorkerVo;
import com.juzifenqi.virtual.bean.vo.XsOrderRespVo;
import com.juzifenqi.virtual.bean.vo.XsVerifyParamVo;
import com.juzifenqi.virtual.bean.vo.XsVerifyRespVo;
import com.juzifenqi.virtual.component.enums.DateEnum;
import com.juzifenqi.virtual.component.enums.HotelOrderStatusEnum;
import com.juzifenqi.virtual.component.enums.OakHotelTypeEnum;
import com.juzifenqi.virtual.component.enums.OrderRechargeStatusEnum;
import com.juzifenqi.virtual.component.enums.OrderStatusEnum;
import com.juzifenqi.virtual.component.enums.OrderSubmitStatusEnum;
import com.juzifenqi.virtual.component.enums.PayStateEnum;
import com.juzifenqi.virtual.component.enums.RechargeStatusEnum;
import com.juzifenqi.virtual.component.enums.RechargeTypeEnum;
import com.juzifenqi.virtual.component.enums.SupplierCouponStatusEnum;
import com.juzifenqi.virtual.component.enums.SupplierEnum;
import com.juzifenqi.virtual.component.enums.ThirdNotifyTypeEnum;
import com.juzifenqi.virtual.component.exception.VirtualException;
import com.juzifenqi.virtual.component.models.VirtualOrdersTrailModel;
import com.juzifenqi.virtual.component.util.DES3;
import com.juzifenqi.virtual.component.util.DateUtils;
import com.juzifenqi.virtual.component.util.RobotUtil;
import com.juzifenqi.virtual.core.services.impl.third.AbstractStrategyHandler;
import com.juzifenqi.virtual.core.services.impl.third.common.CommonBusHandler;
import com.juzifenqi.virtual.core.services.impl.third.common.MqResultCodeEnum;
import com.juzifenqi.virtual.core.services.impl.third.common.enums.ErrorMsgEnum;
import com.juzifenqi.virtual.core.services.impl.third.xiangshu.model.BasicCouponReq;
import com.juzifenqi.virtual.core.services.impl.third.xiangshu.model.DistributeCouponDataReq;
import com.juzifenqi.virtual.core.services.impl.third.xiangshu.model.DistributeCouponReq;
import com.juzifenqi.virtual.core.services.impl.third.xiangshu.model.InvalidCouponReq;
import com.juzifenqi.virtual.core.services.impl.third.xiangshu.model.OakLoginDataResp;
import com.juzifenqi.virtual.core.services.impl.third.xiangshu.model.OakVipLoginResp;
import com.juzifenqi.virtual.core.services.impl.third.xiangshu.model.XiangshuDistributeCouponDataResp;
import com.juzifenqi.virtual.core.services.impl.third.xiangshu.model.XiangshuDistributeCouponResp;
import com.juzifenqi.virtual.core.services.impl.third.xiangshu.model.XiangshuInvalidCouponDataResp;
import com.juzifenqi.virtual.core.services.impl.third.xiangshu.model.XiangshuInvalidCouponResp;
import com.juzifenqi.virtual.core.utils.RedisUtils;
import com.juzifenqi.virtual.dao.VirtualOrderSupplierCouponDao;
import com.juzifenqi.virtual.dao.VirtualOrdersCallbackDao;
import com.juzifenqi.virtual.dao.VirtualOrdersDao;
import com.juzifenqi.virtual.dao.VirtualOrdersDoubtDao;
import com.juzifenqi.virtual.dao.VirtualOrdersWorkerDao;
import com.juzifenqi.virtual.dao.VirtualSupplierHotelOrderDao;
import com.juzifenqi.virtual.dao.VirtualThirdNotifyDao;
import com.juzifenqi.virtual.manager.MemberCenterModel;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import javax.annotation.PostConstruct;
import javax.annotation.Resource;

import com.juzishuke.dss.sdk.service.DssSdkService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;


/**
 * 橡树三方业务处理类
 *
 * <AUTHOR>
 * @date 2023-07-14 10:33:23
 */
@Component
@Slf4j
public class XiangshuBusHandler extends AbstractStrategyHandler {

    @Autowired
    private VirtualOrdersWorkerDao       virtualOrdersWorkerDao;
    @Autowired
    private VirtualOrdersDao             virtualOrdersDao;
    @Autowired
    private VirtualOrdersCallbackDao     virtualOrdersCallbackDao;
    @Autowired
    private VirtualOrdersTrailModel      ordersTrailModel;
    @Autowired
    private XiangshuConfig               xiangshuConfig;
    private XiangshuUtil                 xsUtil;
    @Autowired
    private RedisUtils                   redisUtils;
    @Autowired
    private VirtualSupplierHotelOrderDao hotelOrderDao;

    @Autowired
    private CommonBusHandler              commonBusHandler;
    @Autowired
    private VirtualOrderSupplierCouponDao virtualOrderSupplierCouponDao;
    @Autowired
    private VirtualThirdNotifyDao         virtualThirdNotifyDao;
    @Resource
    private VirtualOrdersDoubtDao         virtualOrdersDoubtDao;
    @Autowired
    private MemberCenterModel             memberCenterModel;
    @Autowired
    private RobotUtil                     robotUtil;
    @Autowired
    private VirtualOrderSupplierCouponDao couponDao;
    @Resource
    private IMemberPlusQueryApi memberPlusQueryApi;
    @Autowired
    private DssSdkService dssSdkService;

    /**
     * 最大查证次数
     */
    public static final Integer MAX_RETRY_QUERY_TIMES_HOTEL = 3;

    private final String channel = SupplierEnum.SUPPLIER_XS.getName();

    // 橡树黑卡联登token redisKey
    private final String PLUS_OAKVIP_USER_TOKEN = "super_plus:oakvip_user_token_";

    // 未使用优惠券异常
    private final String couponErrorMsg = "酒店订单未使用优惠券";

    @PostConstruct
    public void intMethod() {
        log.info("初始化橡树黑卡配置类：host:{},key:{},secret:{}", xiangshuConfig.getXsHost(),
                xiangshuConfig.getXsHostKey(), xiangshuConfig.getXsHostSecret());
        //创建橡树调用器
        xsUtil = new XiangshuUtil(xiangshuConfig.getXsHost(), xiangshuConfig.getXsHostKey(),
                xiangshuConfig.getXsHostSecret());
    }


    /**
     * 橡树回调通知成功响应
     */
    public static final String CALLBACK_RSP_SUCCESS = "success";

    /**
     * 橡树回调通知失败响应
     */
    public static final String CALLBACK_RSP_FAIL = "fail";

    /**
     * 橡树-虚拟权益下单（调用三方充值）
     */
    @Override
    public RechargeRespVo orderRecharge(VirtualOrdersWorkerVo paramVo) {
        log.info("橡树-调用三方冲充值开始：param:{}", JSON.toJSONString(paramVo));
        VirtualOrders order = paramVo.getVirtualOrder();
        if (RechargeTypeEnum.HOTEL.getCode() == order.getRechargeType()) {
            // 酒店券直充
            return orderRechargeHotel(paramVo);
        } else {
            // 普通直充
            return orderRechargeDirect(paramVo);
        }
    }

    /**
     * 橡树-虚拟权益下单（调用三方充值）-直充
     */
    private RechargeRespVo orderRechargeDirect(VirtualOrdersWorkerVo paramVo) {
        VirtualOrders order = paramVo.getVirtualOrder();
        //调用橡树充值下单
        String supplierOrderSn = null;//橡树订单号
        try {
            RechargeParamVo rechargeParamVo = new RechargeParamVo();
            rechargeParamVo.setOrderSn(order.getOrderSn());
            rechargeParamVo.setProductCode(order.getSupplierItemId());
            rechargeParamVo.setRechargeAccount(order.getRechargeAccount());
            rechargeParamVo.setRechargeCode(order.getRechargeCode());
            rechargeParamVo.setCallbackUrl(xiangshuConfig.getCallbackUrl());
            rechargeParamVo.setProfitKey(xiangshuConfig.getProfitKey());
            rechargeParamVo.setProfitSecret(xiangshuConfig.getProfitSecret());
            ApiResponse qyOrder = xsUtil.createQyOrder(rechargeParamVo);
            if (qyOrder == null) {
                //下单失败
                commonBusHandler.handleSubmitOrderFail(paramVo.getId(), order, channel,
                        "调取橡树下单返回为空");
                return null;
            }
            String body = new String(qyOrder.getBody(), StandardCharsets.UTF_8);
            if (StringUtils.isEmpty(body)) {
                //下单失败
                commonBusHandler.handleSubmitOrderFail(paramVo.getId(), order, channel,
                        "调取橡树下单返回body为空");
                return null;
            }
            log.info("橡树黑卡下单返回内容：{},{}", order.getOrderSn(), body);
            XsOrderRespVo xsOrderRespVo = JSON.parseObject(body, XsOrderRespVo.class);
            if (!xsOrderRespVo.isOk() || xsOrderRespVo.getData() == null) {
                commonBusHandler.handleSubmitOrderFail(paramVo.getId(), order, channel,
                        xsOrderRespVo.getMsg());
                return null;
            }
            //下单成功记录下橡树订单号
            supplierOrderSn = xsOrderRespVo.getData().getOak_order_number();
            commonBusHandler.handleSubmitOrderSuc(paramVo.getId(), order, channel, supplierOrderSn);
        } catch (Exception e) {
            log.info("橡树下单发生异常,订单:{},详情 ", order.getOrderSn(), e);
            commonBusHandler.handleSubmitOrderSuc(paramVo.getId(), order, channel, supplierOrderSn);
        }
        return null;
    }

    /**
     * RechargeStatus : success：成功
     */
    private static final String RECHARGE_STATUS_SUC = "success";

    /**
     * RechargeStatus : failed：失败
     */
    private static final String RECHARGE_STATUS_FAIL = "failed";

    /**
     * 最大查证次数
     */
    public static final Integer MAX_RETRY_QUERY_TIMES = 10;

    /**
     * 充值结果查证
     */
    @Override
    public RechargeRespVo getRechargeStatus(VirtualOrdersWorkerVo paramVo) {
        log.info("橡树-调用三方充值查证开始：param:{}", JSON.toJSONString(paramVo));
        VirtualOrders order = paramVo.getVirtualOrder();
        if (RechargeTypeEnum.HOTEL.getCode() == order.getRechargeType()) {
            // 酒店券直充查证
            return getRechargeStatusHotel(paramVo);
        } else {
            // 普通直充查证
            return getRechargeStatusDirect(paramVo);
        }
    }

    /**
     * 充值结果查证-直充
     */
    private RechargeRespVo getRechargeStatusDirect(VirtualOrdersWorkerVo paramVo) {
        long start = System.currentTimeMillis();
        VirtualOrders order = paramVo.getVirtualOrder();
        String orderSn = order.getOrderSn();
        String title = channel + "主动充值查证";
        try {
            XsVerifyParamVo xsVerifyParamVo = new XsVerifyParamVo();
            xsVerifyParamVo.setOrderSn(orderSn);
            xsVerifyParamVo.setProfitKey(xiangshuConfig.getProfitKey());
            xsVerifyParamVo.setProfitSecret(xiangshuConfig.getProfitSecret());
            ApiResponse qyOrder = xsUtil.queryQyOrder(xsVerifyParamVo);
            log.info("调橡树查证接口：返回结果,result={}", JSON.toJSONString(qyOrder));
            if (qyOrder == null) {
                log.info("调橡树查证失败-返回为空,orderSn={}", orderSn);
                //查证状态-未知
                commonBusHandler.handleUnKnowVerify(title, "调取橡树查证返回为空", paramVo, order,
                        MAX_RETRY_QUERY_TIMES);
                return null;
            }
            String body = new String(qyOrder.getBody(), StandardCharsets.UTF_8);
            if (StringUtils.isEmpty(body)) {
                //查证状态-未知
                log.info("调橡树查证失败-返回为空,orderSn={}", orderSn);
                commonBusHandler.handleUnKnowVerify(title, "调取橡树查证返回body为空", paramVo,
                        order, MAX_RETRY_QUERY_TIMES);
                return null;
            }
            //{"ok":true,"code":200,"msg":"操作成功","data":{"out_order_number":"JDD123456","oak_order_number":"UN230714120008B9RRUTA2","status":"confirming","err_message":"确认中","coupon_info":{}}}
            XsVerifyRespVo verifyRespVo = JSON.parseObject(body, XsVerifyRespVo.class);
            if (!verifyRespVo.isOk() || verifyRespVo.getData() == null) {
                log.info("调橡树查证失败-调用失败或data为空,orderSn={}", orderSn);
                //查证状态-未知
                commonBusHandler.handleUnKnowVerify(title, "调用失败或data为空", paramVo, order,
                        MAX_RETRY_QUERY_TIMES);
                return null;
            }
            String status = verifyRespVo.getData().getStatus();
            if (StringUtils.isEmpty(status)) {
                log.info("调橡树查证失败-查证状态为空,orderSn={}", orderSn);
                //查证状态-未知
                commonBusHandler.handleUnKnowVerify(title, "查证状态为空", paramVo, order,
                        MAX_RETRY_QUERY_TIMES);
                return null;
            }
            //confirming：确认中 ，success：成功，failed：失败
            if (RECHARGE_STATUS_SUC.equals(status)) {
                log.info("调橡树查证成功-充值成功,orderSn={}", orderSn);
                commonBusHandler.handleSucVerify(paramVo.getId(), order, title, null, false);
                return null;
            }
            if (RECHARGE_STATUS_FAIL.equals(status)) {
                log.info("调橡树查证失败-充值失败,orderSn={}", orderSn);
                String message = verifyRespVo.getData().getErr_message();
                commonBusHandler.handleFailVerify(title, message, paramVo.getId(), order, false);
                return null;
            }
            log.info("调橡树查证状态为,status={}", status);
            commonBusHandler.handleUnKnowVerify(title, "查证状态为:" + status, paramVo, order,
                    MAX_RETRY_QUERY_TIMES);
        } catch (Exception e) {
            log.info("橡树查证发生异常,订单:{}", order.getOrderSn(), e);
        }
        long end = System.currentTimeMillis();
        log.info("=====>橡树-job处理一笔查证订单耗时: {} ms", end - start);
        return null;
    }

    /**
     * 橡树回调处理
     */
    public String rechargeStatusNotice(XsRechargeStatusNotice statusNotice) {
        log.info("橡树回调通知订单结果开始:{},日期:{}", JSON.toJSONString(statusNotice),
                DateUtils.dateToStr(new Date(), DateEnum.DATE_FORMAT));
        if (StringUtils.isBlank(statusNotice.getStatus()) || StringUtils.isBlank(
                statusNotice.getOut_order_number()) || StringUtils.isBlank(
                statusNotice.getOak_order_number())) {
            log.info("橡树回调通知订单结果失败-请求参数不合法,必填字段");
            return CALLBACK_RSP_FAIL;
        }
        String orderSn = statusNotice.getOut_order_number();
        VirtualOrdersWorker xsOrdersWorker = virtualOrdersWorkerDao.getVirtualOrdersWorkerByOrderSn(
                orderSn);
        if (xsOrdersWorker == null) {
            log.info("橡树回调通知订单结果失败-订单不存在,传参桔子订单号:{}", orderSn);
            return CALLBACK_RSP_FAIL;
        }
        //主动查证已经有结果了
        if (xsOrdersWorker.getOrderRechargeStatus() != 0) {
            log.info("橡树主动查证已经有结果了,不处理回调,订单号:{}", orderSn);
            orderCallback(statusNotice, CALLBACK_RSP_SUCCESS, "橡树主动查证已经有结果");
            return CALLBACK_RSP_SUCCESS;
        }
        //处理回调状态
        String dealStatus = dealStatusNotice(statusNotice, xsOrdersWorker.getId());
        orderCallback(statusNotice, dealStatus, "回调处理成功");
        return dealStatus;
    }

    /**
     * 记录橡树回调状态
     */
    private void orderCallback(XsRechargeStatusNotice statusNotice, String response,
            String remark) {
        //记录回调结果
        VirtualOrdersCallback virtualOrdersCallback = new VirtualOrdersCallback();
        virtualOrdersCallback.setOrderSn(statusNotice.getOut_order_number());
        virtualOrdersCallback.setSupplierOrderSn(statusNotice.getOak_order_number());
        virtualOrdersCallback.setCallbackOrderStatus(statusNotice.getStatus());
        virtualOrdersCallback.setCallbackOrderDesc(statusNotice.getErr_message());
        virtualOrdersCallback.setResponse(response);
        virtualOrdersCallback.setRemark(remark);
        virtualOrdersCallbackDao.saveVirtualOrdersCallback(virtualOrdersCallback);
    }

    /**
     * 处理回调信息
     */
    private String dealStatusNotice(XsRechargeStatusNotice statusNotice, Integer workerId) {
        {
            String orderSn = statusNotice.getOut_order_number();
            log.info("橡树回调处理开始,订单号:{}", orderSn);
            boolean success = RECHARGE_STATUS_SUC.equals(statusNotice.getStatus());
            VirtualOrders order = virtualOrdersDao.getVirtualOrdersByOrderSn(orderSn);
            //更新任务表订单状态
            VirtualOrdersWorker worker = new VirtualOrdersWorker();
            worker.setId(workerId);
            worker.setOrderRechargeStatus(
                    success ? OrderRechargeStatusEnum.RECHARGE_SUCCESS.getCode()
                            : OrderRechargeStatusEnum.RECHARGE_FAIL.getCode());
            worker.setOrderRechargeTime(new Date());
            worker.setRemark(success ? "橡树回调通知充值成功"
                    : "橡树轩回调通知充值失败-" + statusNotice.getErr_message());
            virtualOrdersWorkerDao.updateVirtualOrdersWorkerById(worker);
            //更新主表订单状态
            VirtualOrders virtualOrders = new VirtualOrders();
            virtualOrders.setId(order.getId());
            virtualOrders.setOrderStatus(success ? OrderStatusEnum.STATUS_RECHARGE_SUCCESS.getCode()
                    : OrderStatusEnum.STATUS_RECHARGE_FAIL.getCode());
            virtualOrders.setRemark(success ? OrderStatusEnum.STATUS_RECHARGE_SUCCESS.getDesc()
                    : OrderStatusEnum.STATUS_RECHARGE_FAIL.getDesc());
            // 20231110 zjf 充值状态赋值
            virtualOrders.setRechargeStatus(success ? RechargeStatusEnum.RECHARGE_SUCCESS.getCode()
                    : RechargeStatusEnum.RECHARGE_FAIL.getCode());
            virtualOrdersDao.updateVirtualOrdersById(virtualOrders);
            //记录订单流转状态
            ordersTrailModel.saveOrdersTrail(order.getOrderSn(),
                    OrderStatusEnum.STATUS_ORDER_SUCCESS.getCode(),
                    success ? OrderStatusEnum.STATUS_RECHARGE_SUCCESS.getCode()
                            : OrderStatusEnum.STATUS_RECHARGE_FAIL.getCode(),
                    success ? "回调通知充值成功" : "回调通知充值失败");
            log.info("处理橡树回调通知 {} ,订单:{}",
                    success ? "橡树回调通知充值成功" : "橡树回调通知充值失败", order.getOrderSn());
            if (!success) {
                // 是否0元订单
                boolean zeroOrder = order.getOrderMoney().compareTo(BigDecimal.ZERO) == 0;
                // 充值失败用户订单退款 0元订单不调用订单中心接口
                if (!zeroOrder) {
                    commonBusHandler.cancelOrder(order, "橡树回调充值失败");
                }
            }
            // 20230508 zjf 发送橡树充值结果mq
            commonBusHandler.sendRechargeResultMq(order,
                    success ? MqResultCodeEnum.SUCCESS : MqResultCodeEnum.FAIL);
            return CALLBACK_RSP_SUCCESS;
        }
    }

    /**
     * 橡树黑卡联登并返回token 1.先查缓存，存在直接返回token 2.不存在进行橡树联登 3.缓存记录token
     */
    private Response<String> oakVipLogin(Integer userId) {
        log.info("获取橡树黑卡联登token：{}", userId);
        try {
            String redisKey = PLUS_OAKVIP_USER_TOKEN + userId;
            String userToken = redisUtils.get(redisKey);
            log.info("获取黑卡联登token缓存：{}", userToken);
            if (StringUtils.isNotEmpty(userToken)) {
                // 缓存存在直接返回token
                return Response.ok(userToken);
            }
            // 联登
            String userIdStr = String.valueOf(userId);
            Response<String> loginResp = xsUtil.login(userIdStr, userIdStr);
            if (!loginResp.isOk()) {
                // 接口调用失败
                return loginResp;
            }
            if (StringUtils.isEmpty(loginResp.getData())) {
                log.info("橡树黑卡联登接口调用成功但body为空：user={}", userIdStr);
                return Response.fail(ErrorMsgEnum.RESP_ERROR.getCode(), "返回数据异常,body为空");
            }
            // 业务参数解析
            OakVipLoginResp loginVo = JSONObject.parseObject(loginResp.getData(),
                    OakVipLoginResp.class);
            if (Objects.isNull(loginVo)) {
                log.info("橡树黑卡联登接口调用成功但对象解析为空：user={}", userIdStr);
                return Response.fail(ErrorMsgEnum.RESP_ERROR.getCode(), "返回数据异常,解析失败");
            }
            // 业务错误-联登失败
            if (!loginVo.isOk()) {
                log.info("橡树黑卡联登失败：user={} msg={}", userIdStr,
                        loginVo.getCode() + loginVo.getMsg());
                return Response.fail(ErrorMsgEnum.BUS_ERROR.getCode(),
                        loginVo.getCode() + loginVo.getMsg());
            }
            OakLoginDataResp data = loginVo.getData();
            if (Objects.isNull(data)) {
                log.info("橡树黑卡联登成功但data为空：user={}", userIdStr);
                return Response.fail(ErrorMsgEnum.RESP_ERROR.getCode(), "返回数据异常,data为空");
            }
            if (StringUtils.isEmpty(data.getUser_token())) {
                log.info("橡树黑卡联登成功但user_token为空：user={}", userIdStr);
                return Response.fail(ErrorMsgEnum.RESP_ERROR.getCode(), "返回数据异常,token为空");
            }
            // 缓存橡树黑卡token，橡树是4小时有效，桔子设置3小时，防止提前过期
            redisUtils.setEx(redisKey, data.getUser_token(), 3, TimeUnit.HOURS);
            log.info("橡树黑卡联登成功：user={} key={} token={}", userIdStr, redisKey,
                    data.getUser_token());
            return Response.ok(data.getUser_token());
        } catch (Exception e) {
            LogUtil.printLog("橡树联登异常,详情:{}", e);
            return Response.fail(ErrorMsgEnum.SYS_ERROR.getCode(), "接口异常");
        }
    }

    /**
     * 获取去预定酒店跳转的url
     */
    @Override
    public HotelUrlVo getHotelUrl(VirtualOrdersWorkerVo paramVo) {
        VirtualOrders virtualOrder = paramVo.getVirtualOrder();
        log.info("获取橡树酒店预定跳转url：{}", virtualOrder.getOrderSn());
        if (StringUtils.isBlank(virtualOrder.getSupplierAccessUrl())
                || virtualOrder.getSupplierAccessType() == null) {
            log.info("获取橡树酒店预定跳转url参数不全：{}", virtualOrder.getOrderSn());
            throw new VirtualException("跳转地址参数不全");
        }
        // 联登
        String token = oakVipLogin(virtualOrder.getUserId()).getData();
        if (StringUtils.isBlank(token)) {
            throw new VirtualException("联登失败,请重试");
        }
        // 酒店类型
        String supplierCode = OakHotelTypeEnum.getSupplierCode(
                virtualOrder.getSupplierAccessType());
        String url = virtualOrder.getSupplierAccessUrl() + "?benefitType=" + supplierCode
                + "&namespace=jdd&user_token=" + token + "&source=" + virtualOrder.getOrderSn();
        log.info("获取橡树酒店预定跳转url结果：{}", url);
        return new HotelUrlVo(url);
    }

    /**
     * 获取查看酒店订单详情需要的参数
     */
    @Override
    public HotelOrderDetailVo getHotelOrderDetail(VirtualOrdersWorkerVo paramVo) {
        VirtualOrders virtualOrder = paramVo.getVirtualOrder();
        log.info("获取橡树酒店订单详情：{}", virtualOrder.getOrderSn());
        // 获取虚拟权益订单领取的酒店券id关联的有效酒店订单信息
        List<VirtualOrderSupplierCoupon> coupons = couponDao.selectByOrderSns(
                Collections.singletonList(virtualOrder.getOrderSn()));
        if (CollectionUtils.isEmpty(coupons)) {
            throw new VirtualException("未获取到有效的酒店券");
        }
        // 获取有效的酒店订单
        List<VirtualSupplierHotelOrder> hotelOrders = hotelOrderDao.selectHotelOrderByCouponIds(
                coupons.stream().map(VirtualOrderSupplierCoupon::getCouponId)
                        .collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(hotelOrders)) {
            throw new VirtualException("未获取到有效的酒店订单信息");
        }
        String token = oakVipLogin(virtualOrder.getUserId()).getData();
        if (StringUtils.isBlank(token)) {
            throw new VirtualException("联登失败,请重试");
        }
        return new HotelOrderDetailVo(token, hotelOrders.get(0).getHotelOrderSn());
    }

    /**
     * 橡树-虚拟权益下单（调用三方充值）-领取酒店券
     */
    private RechargeRespVo orderRechargeHotel(VirtualOrdersWorkerVo paramVo) {
        long start = System.currentTimeMillis();
        VirtualOrders order = paramVo.getVirtualOrder();
        String orderSn = order.getOrderSn();
        //调用橡树酒店券充值下单
        String title = "橡树领券";
        Date expiration = null;
        try {
            // 联登
            Response<String> loginResp = oakVipLogin(order.getUserId());
            if (!loginResp.isOk()) {
                //下单失败-联登失败
                commonBusHandler.handleFailVerify(title, "联登失败:" + loginResp.getMsg(),
                        paramVo.getId(), order, true);
                return null;
            }
            // 领券
            DistributeCouponReq req = new DistributeCouponReq();
            // txId 幂等id 用虚拟订单号
            req.setTxId(orderSn);
            // 橡树用户token
            req.setToken(loginResp.getData());
            // 领券信息
            DistributeCouponDataReq couponDataReq = new DistributeCouponDataReq();
            // 数量写死1 1次领一张券
            couponDataReq.setCount(1);
            // 批次号 合作方商品id
            couponDataReq.setBatchId(order.getSupplierItemId());
            // 查询当前会员的结束时间
            Response<Date> endTimeResp = queryMemberPlusEndTimeByPlusOrderSn(
                    order.getPlusOrderSn());
            if (!endTimeResp.isOk()) {
                // 下单失败-获取会员周期结束时间失败
                commonBusHandler.handleFailVerify(title,
                        "获取会员周期时间失败:" + endTimeResp.getMsg(), paramVo.getId(), order,
                        true);
                return null;
            }
            // 优惠券有效期=会员周期结束时间
            couponDataReq.setExpiration(
                    DateUtils.dateToStr(endTimeResp.getData(), DateEnum.DATE_FORMAT));
            req.setCouponDataReqList(Collections.singletonList(couponDataReq));
            expiration = endTimeResp.getData();
            Response<String> distributeResp = xsUtil.distributeCoupon(req);
            if (!distributeResp.isOk()) {
                // 接口调用失败
                log.info("{} 接口调用失败：orderSn={} msg={}", title, orderSn,
                        distributeResp.getMsg());
                commonBusHandler.handleFailVerify(title, "接口调用失败:" + distributeResp.getMsg(),
                        paramVo.getId(), order, true);
                return null;
            }
            if (StringUtils.isEmpty(distributeResp.getData())) {
                log.info("{} 返回对象异常,body为空：orderSn={}", title, orderSn);
                commonBusHandler.handleFailVerify(title, "返回对象异常,body为空", paramVo.getId(),
                        order, true);
                return null;
            }
            // 业务参数解析
            XiangshuDistributeCouponResp couponResp = JSONObject.parseObject(
                    distributeResp.getData(), XiangshuDistributeCouponResp.class);
            if (Objects.isNull(couponResp)) {
                log.info("{} 返回对象异常,解析失败：orderSn={}", title, orderSn);
                commonBusHandler.handleFailVerify(title, "返回对象异常,解析失败", paramVo.getId(),
                        order, true);
                return null;
            }
            // 业务错误-领券失败
            if (!couponResp.isOk()) {
                log.info("{} 接口失败：orderSn={} msg={}", title, orderSn,
                        couponResp.getCode() + couponResp.getMsg());
                commonBusHandler.handleFailVerify(title,
                        "接口失败:" + couponResp.getCode() + couponResp.getMsg(), paramVo.getId(),
                        order, true);
                return null;
            }
            List<XiangshuDistributeCouponDataResp> dataList = couponResp.getData();
            if (CollectionUtils.isEmpty(dataList)) {
                log.info("{} 返回对象异常,data为空：orderSn={}", title, orderSn);
                commonBusHandler.handleFailVerify(title, "返回对象异常,data为空", paramVo.getId(),
                        order, true);
                return null;
            }
            // 目前每笔订单只会领一张券
            XiangshuDistributeCouponDataResp data = dataList.get(0);
            if (!"success".equals(data.getStatus())) {
                log.info("{} 领券失败：orderSn={} msg={}", title, orderSn,
                        data.getStatus() + data.getReason());
                commonBusHandler.handleFailVerify(title, "领券失败:" + data.getReason(),
                        paramVo.getId(), order, true);
                return null;
            }
            if (CollectionUtils.isEmpty(data.getCoupon_ids())) {
                log.info("{} 返回对象异常,coupon_ids为空：orderSn={}", title, orderSn);
                commonBusHandler.handleFailVerify(title, "返回对象异常,coupon_ids为空",
                        paramVo.getId(), order, true);
                return null;
            }
            // 领券成功
            VirtualOrderSupplierCoupon coupon = new VirtualOrderSupplierCoupon();
            coupon.setOrderSn(order.getOrderSn());
            coupon.setSupplierId(order.getSupplierId());
            coupon.setSupplierName(order.getSupplierName());
            coupon.setBatchId(data.getBatch_id());
            coupon.setCouponId(data.getCoupon_ids().get(0));
            coupon.setUseState(SupplierCouponStatusEnum.RECEIVED.getCode());
            coupon.setExpirationTime(expiration);
            commonBusHandler.handleSucVerify(paramVo.getId(), order, title,
                    new VirtualOrderRechargeSuccVo(coupon), true);
        } catch (Exception e) {
            // 接口请求异常,一般是超时 //SocketTimeoutException
            LogUtil.printLog("{} 发生异常,订单:{},详情:{}", title, order.getOrderSn(), e);
            if (!Objects.isNull(expiration)) {
                // 非空表示已经进行领取操作并产生的异常，需要进行未知查证流程
                handleOrderRechargeCouponUnKnow(paramVo.getId(), order, expiration);
            } else {
                // 调用领券前的异常都当失败处理
                commonBusHandler.handleFailVerify(title, "系统异常", paramVo.getId(), order, true);
            }
        }
        long end = System.currentTimeMillis();
        log.info("{} 处理一笔订单耗时: {} ms", title, end - start);
        return null;
    }

    /**
     * 通过会员订单号查询会员周期结束时间
     */
    private Response<Date> queryMemberPlusEndTimeByPlusOrderSn(String plusOrderSn) {
        log.info("通过会员订单号查询会员周期信息：param:{}", plusOrderSn);
        try {
            PlusAbyssResult<MemberPlusInfoDetailResp> resp = memberPlusQueryApi.getMemberPlusInfoDetail(
                    plusOrderSn);
            if (!resp.getSuccess()) {
                log.info("通过会员订单号查询会员周期信息失败：param:{} msg:{}", plusOrderSn,
                        resp.getMessage());
                return Response.fail(ErrorMsgEnum.RESP_ERROR.getCode(), resp.getMessage());
            }
            log.info("通过会员订单号查询会员周期信息: endTime:{}", resp.getResult().getJxEndTime());
            return Response.ok(resp.getResult().getJxEndTime());
        } catch (Exception e) {
            LogUtil.printLog(e, "通过会员单号获取会员周期信息异常");
            return Response.fail(ErrorMsgEnum.SYS_ERROR.getCode(), "接口异常");
        }
    }

    /**
     * 充值领券未知处理
     *
     * @param workerId 任务ID
     * @param order 订单信息
     * @Param expiration 优惠券有效期
     */
    private void handleOrderRechargeCouponUnKnow(Integer workerId, VirtualOrders order,
            Date expiration) {
        String title = channel + "领券结果未知";
        log.info("{}：order={} expiration={}", title, order.getOrderSn(), expiration);
        try {
            // 更新子表订单状态
            VirtualOrdersWorker worker = new VirtualOrdersWorker();
            worker.setId(workerId);
            worker.setOrderSubmitStatus(OrderSubmitStatusEnum.ORDER_SUCCESS.getCode());
            worker.setOrderSubmitTime(new Date());
            worker.setOrderRechargeStatus(OrderRechargeStatusEnum.RECHARGE_NO_VERIFY.getCode());
            worker.setRemark(title);
            virtualOrdersWorkerDao.updateVirtualOrdersWorkerById(worker);

            // 更新主表订单状态
            VirtualOrders virtualOrders = new VirtualOrders();
            virtualOrders.setId(order.getId());
            virtualOrders.setOrderStatus(OrderStatusEnum.STATUS_ORDER_SUCCESS.getCode());
            virtualOrders.setRechargeStatus(RechargeStatusEnum.RECHARGE_ING.getCode());
            virtualOrders.setRemark(title);
            virtualOrdersDao.updateVirtualOrdersById(virtualOrders);

            // 记录订单流转状态
            ordersTrailModel.saveOrdersTrail(order.getOrderSn(),
                    OrderStatusEnum.STATUS_ORDER_NO_PUSH.getCode(),
                    OrderStatusEnum.STATUS_ORDER_SUCCESS.getCode(), "领券结果未知,等待查证");

            // 记录优惠券信息表
            VirtualOrderSupplierCoupon coupon = new VirtualOrderSupplierCoupon();
            coupon.setOrderSn(order.getOrderSn());
            coupon.setSupplierId(order.getSupplierId());
            coupon.setSupplierName(order.getSupplierName());
            coupon.setBatchId(order.getSupplierItemId());
            coupon.setUseState(SupplierCouponStatusEnum.NO_RECEIVE.getCode());
            coupon.setExpirationTime(expiration);
            virtualOrderSupplierCouponDao.saveVirtualOrderSupplierCoupon(coupon);
        } catch (Exception e) {
            LogUtil.printLog(e, title + " 处理异常");
        }
    }

    /**
     * 充值结果查证-酒店券直充
     */
    private RechargeRespVo getRechargeStatusHotel(VirtualOrdersWorkerVo paramVo) {
        long start = System.currentTimeMillis();
        VirtualOrders order = paramVo.getVirtualOrder();
        String orderSn = order.getOrderSn();
        String title = "橡树领券结果查证";
        try {
            // 联登
            Response<String> loginResp = oakVipLogin(order.getUserId());
            if (!loginResp.isOk()) {
                //下单失败-联登失败
                commonBusHandler.handleUnKnowVerify(title, "联登失败:" + loginResp.getMsg(),
                        paramVo, order, MAX_RETRY_QUERY_TIMES_HOTEL);
                return null;
            }
            // 查询领券结果
            BasicCouponReq req = new BasicCouponReq();
            req.setToken(loginResp.getData());
            req.setTxId(orderSn);
            Response<String> distributeResp = xsUtil.queryDistributeCouponResult(req);
            if (!distributeResp.isOk()) {
                // 接口调用失败
                log.info("{} 接口调用失败：orderSn={} msg={}", title, orderSn,
                        distributeResp.getMsg());
                commonBusHandler.handleUnKnowVerify(title,
                        "接口调用失败:" + distributeResp.getMsg(), paramVo, order,
                        MAX_RETRY_QUERY_TIMES_HOTEL);
                return null;
            }
            if (StringUtils.isEmpty(distributeResp.getData())) {
                log.info("{} 返回对象异常,body为空：orderSn={}", title, orderSn);
                commonBusHandler.handleUnKnowVerify(title, "返回对象异常,body为空", paramVo, order,
                        MAX_RETRY_QUERY_TIMES_HOTEL);
                return null;
            }
            // 业务参数解析
            XiangshuDistributeCouponResp couponResp = JSONObject.parseObject(
                    distributeResp.getData(), XiangshuDistributeCouponResp.class);
            if (Objects.isNull(couponResp)) {
                log.info("{} 返回对象异常,解析失败：orderSn={}", title, orderSn);
                commonBusHandler.handleUnKnowVerify(title, "返回对象异常,解析失败", paramVo, order,
                        MAX_RETRY_QUERY_TIMES_HOTEL);
                return null;
            }
            // 业务错误-领券失败 这里通过data里的status判断是否是明确的失败
            if (!couponResp.isOk()) {
                log.info("{} 接口失败：orderSn={} msg={}", title, orderSn,
                        couponResp.getCode() + couponResp.getMsg());
                commonBusHandler.handleUnKnowVerify(title,
                        "接口失败:" + couponResp.getCode() + couponResp.getMsg(), paramVo, order,
                        MAX_RETRY_QUERY_TIMES_HOTEL);
                return null;
            }
            List<XiangshuDistributeCouponDataResp> dataList = couponResp.getData();
            if (CollectionUtils.isEmpty(dataList)) {
                log.info("{} 返回对象异常,data为空：orderSn={}", title, orderSn);
                commonBusHandler.handleUnKnowVerify(title, "返回对象异常,data为空", paramVo, order,
                        MAX_RETRY_QUERY_TIMES_HOTEL);
                return null;
            }
            // 目前每笔订单只会领一张券
            XiangshuDistributeCouponDataResp data = dataList.get(0);
            if (!"success".equals(data.getStatus())) {
                log.info("{} 领券失败：orderSn={} msg={}", title, orderSn,
                        data.getStatus() + data.getReason());
                commonBusHandler.handleFailVerify(title, "领券失败:" + data.getReason(),
                        paramVo.getId(), order, false);
                return null;
            }
            if (CollectionUtils.isEmpty(data.getCoupon_ids())) {
                log.info("{} 返回对象异常,coupon_ids为空：orderSn={}", title, orderSn);
                commonBusHandler.handleUnKnowVerify(title, "返回对象异常,coupon_ids为空", paramVo,
                        order, MAX_RETRY_QUERY_TIMES_HOTEL);
                return null;
            }
            // 领券充值成功
            // 查询本单领券记录
            VirtualOrderSupplierCoupon supplierCoupon = virtualOrderSupplierCouponDao.selectByOrderSn(
                    orderSn);
            if (Objects.isNull(supplierCoupon)) {
                commonBusHandler.handleUnKnowVerify(title, "未查到订单的领券信息", paramVo, order,
                        MAX_RETRY_QUERY_TIMES_HOTEL);
                return null;
            }
            VirtualOrderSupplierCoupon coupon = new VirtualOrderSupplierCoupon();
            coupon.setId(supplierCoupon.getId());
            coupon.setCouponId(data.getCoupon_ids().get(0));
            coupon.setUseState(SupplierCouponStatusEnum.RECEIVED.getCode());
            commonBusHandler.handleSucVerify(paramVo.getId(), order, title,
                    new VirtualOrderRechargeSuccVo(coupon), false);
        } catch (Exception e) {
            // 接口请求异常,一般是超时 //SocketTimeoutException
            LogUtil.printLog("{} 发生异常,订单:{},详情:{}", title, order.getOrderSn(), e);
            commonBusHandler.handleUnKnowVerify(title, "处理异常", paramVo, order,
                    MAX_RETRY_QUERY_TIMES_HOTEL);
        }
        long end = System.currentTimeMillis();
        log.info("{} 处理一笔订单耗时: {} ms", title, end - start);
        return null;
    }

    /**
     * 橡树回调处理-酒店订单推送
     */
    public String supplierHotelOrderNotify(XsHotelOrderNotice notice) {
        String title = channel + "酒店订单推送";
        log.info("{},time:{},body:{}", title, DateUtils.dateToStr(new Date(), DateEnum.DATE_FORMAT),
                JSON.toJSONString(notice));
        try {
            // 必要参数校验 - 桔子订单号
            if (Objects.isNull(notice) || StringUtils.isEmpty(notice.getConfirmation())
                    || StringUtils.isEmpty(notice.getOrder_number())) {
                log.info("{} 处理失败-请求参数不合法,缺少必填字段", title);
                return CALLBACK_RSP_FAIL;
            }
            // 桔子订单号
            String orderSn = notice.getConfirmation();
            // 查询虚拟订单信息-是否桔子订单
            VirtualOrders order = virtualOrdersDao.getByOrderSn(orderSn);
            if (Objects.isNull(order)) {
                log.info("{} 非桔子订单 orderSn:{}", title, orderSn);
                return RECHARGE_STATUS_SUC;
            }
            // 保存回调记录表virtual_third_notify
            VirtualThirdNotify notify = new VirtualThirdNotify();
            notify.setOrderSn(orderSn);
            notify.setSupplierOrderSn(notice.getOrder_number());
            notify.setSupplierId(SupplierEnum.SUPPLIER_XS.getCode());
            notify.setSupplierName(SupplierEnum.SUPPLIER_XS.getName());
            notify.setNotifyType(ThirdNotifyTypeEnum.HOTEL_ORDER.getCode());
            notify.setNotifyDesc(ThirdNotifyTypeEnum.HOTEL_ORDER.getName());
            notify.setNotifyBody(JSON.toJSONString(notice));
            virtualThirdNotifyDao.saveVirtualThirdNotify(notify);
            // 酒店订单数据处理
            Response resp = dealHotelOrderInfo(order, notice);
            if (resp.isOk()) {
                log.info("{} 处理成功 orderSn:{}", title, orderSn);
                return RECHARGE_STATUS_SUC;
            } else {
                // 处理失败 - 记录存疑表virtual_orders_doubt
                log.info("{} 处理失败 orderSn:{} msg:{}", title, orderSn, resp.getMsg());
                VirtualOrdersDoubt virtualOrdersDoubt = new VirtualOrdersDoubt();
                virtualOrdersDoubt.setUserId(order.getUserId());
                virtualOrdersDoubt.setChannelId(order.getChannelId());
                virtualOrdersDoubt.setOrderSn(order.getOrderSn());
                virtualOrdersDoubt.setReason(title + " 处理失败:" + resp.getMsg());
                virtualOrdersDoubt.setCreateTime(new Date());
                virtualOrdersDoubtDao.saveVirtualOrdersDoubt(virtualOrdersDoubt);
                // 发送飞书技术报警
                robotUtil.pushMsg(title + " 处理失败:" + resp.getMsg());
                return couponErrorMsg.equals(resp.getMsg()) ? RECHARGE_STATUS_SUC
                        : CALLBACK_RSP_FAIL;
            }
        } catch (Exception e) {
            LogUtil.printLog(e, title + " 处理异常");
            return CALLBACK_RSP_FAIL;
        }
    }

    /**
     * 酒店订单数据处理
     */
    private Response dealHotelOrderInfo(VirtualOrders order, XsHotelOrderNotice notice) {
        try {
            String orderSn = order.getOrderSn();
            XsHotelOrderCouponInfo couponInfo = notice.getCoupon();
            XsHotelOrderPayInfo payInfo = notice.getPay_info();
            // 酒店订单状态校验
            if (StringUtils.isEmpty(notice.getStatus())) {
                return Response.fail(ErrorMsgEnum.RESP_ERROR.getCode(), "订单状态为空");
            }
            HotelOrderStatusEnum statusEnum = HotelOrderStatusEnum.getType(notice.getStatus());
            if (Objects.isNull(statusEnum)) {
                return Response.fail(ErrorMsgEnum.RESP_ERROR.getCode(),
                        "订单状态不匹配 " + notice.getStatus());
            }
            // 酒店信息
            if (Objects.isNull(payInfo)) {
                return Response.fail(ErrorMsgEnum.RESP_ERROR.getCode(), "酒店信息为空");
            }
            if (StringUtils.isEmpty(payInfo.getHotel_name())) {
                return Response.fail(ErrorMsgEnum.RESP_ERROR.getCode(), "酒店名称为空");
            }
            // 是否本单有使用优惠券
            // 如果没有使用酒店券，则认为订单无效，保存更新订单信息，记录存疑表，并发送飞书报警给业务
            boolean isValid = !Objects.isNull(couponInfo) && StringUtils.isNotEmpty(
                    couponInfo.getCoupon_id());
            if (isValid) {
                // 查询有效的酒店订单列表 - 优惠券id
                VirtualSupplierHotelOrder supplierHotelOrder = hotelOrderDao.selectValidHotelOrderByOrderSnAndCouponId(
                        orderSn, couponInfo.getCoupon_id(),
                        HotelOrderStatusEnum.notValidStatuses());
                if (!Objects.isNull(supplierHotelOrder)) {
                    if (!notice.getOrder_number().equals(supplierHotelOrder.getHotelOrderSn())) {
                        return Response.fail(ErrorMsgEnum.RESP_ERROR.getCode(),
                                "存在使用该酒店券的其他有效的酒店订单["
                                        + supplierHotelOrder.getHotelOrderSn() + "]");
                    }
                }
            }
            boolean repeated = false;
            // 保存酒店订单
            VirtualSupplierHotelOrder hotelOrder = builderHotelOrder(order, notice, statusEnum);
            // 查询是否存在该酒店订单 - 存在则更新,不存在则新增
            VirtualSupplierHotelOrder supplierHotelOrder = hotelOrderDao.selectHotelOrderBySn(
                    orderSn, notice.getOrder_number());
            if (!Objects.isNull(supplierHotelOrder)) {
                hotelOrder.setId(supplierHotelOrder.getId());
                // 需要判断是否是重复状态的消息，如果是短信不重复发送
                repeated = hotelOrder.getOrderStatus().equals(supplierHotelOrder.getOrderStatus());
                // 修改
                hotelOrderDao.update(hotelOrder);
            } else {
                // 新增
                hotelOrderDao.save(hotelOrder);
            }
            // 记录订单轨迹 - 酒店订单推送：酒店订单号 + 状态
            ordersTrailModel.saveOrdersTrail(order.getOrderSn(),
                    OrderStatusEnum.STATUS_RECHARGE_SUCCESS.getCode(),
                    OrderStatusEnum.STATUS_RECHARGE_SUCCESS.getCode(),
                    "酒店订单推送: " + notice.getOrder_number() + " " + statusEnum.getName());
            if (!isValid) {
                // 发送飞书预警
                String msg = "虚拟权益订单:" + orderSn + " 酒店订单:" + notice.getOrder_number()
                        + " 未使用优惠券";
                robotUtil.pushBusMsg(msg);
                return Response.fail(ErrorMsgEnum.RESP_ERROR.getCode(), couponErrorMsg);
            }
            // 状态节点发送短信 - 只有有效的订单才发送短信
            if (HotelOrderStatusEnum.needSendPhoneMsg(statusEnum.getCode()) && !repeated) {
                sendHotelMsg(order, notice, statusEnum);
            }
            return Response.ok();
        } catch (Exception e) {
            LogUtil.printLog(e, "酒店订单数据处理异常");
            return Response.fail(ErrorMsgEnum.SYS_ERROR.getCode(), "酒店订单数据处理异常");
        }
    }

    /**
     * 构建酒店订单数据
     */
    private VirtualSupplierHotelOrder builderHotelOrder(VirtualOrders order,
            XsHotelOrderNotice notice, HotelOrderStatusEnum statusEnum) {
        XsHotelOrderPayInfo payInfo = notice.getPay_info();
        XsHotelOrderCouponInfo couponInfo = notice.getCoupon();
        VirtualSupplierHotelOrder hotelOrder = new VirtualSupplierHotelOrder();
        hotelOrder.setOrderSn(order.getOrderSn());
        hotelOrder.setSupplierId(SupplierEnum.SUPPLIER_XS.getCode());
        hotelOrder.setSupplierName(SupplierEnum.SUPPLIER_XS.getName());
        hotelOrder.setHotelOrderSn(notice.getOrder_number());
        hotelOrder.setHotelOrderCreateTime(notice.getT_created());
        hotelOrder.setSupplierUserId(notice.getUser_id());
        hotelOrder.setSupplierUser(payInfo.getUser());
        // 手机号 加密
        hotelOrder.setSupplierUserPhone(DES3.encrypt(payInfo.getPhone()));
        try {
            hotelOrder.setSupplierUserPhoneUuid(dssSdkService.encryptPhone(payInfo.getPhone()));
        } catch (Exception e) {
            log.warn("橡树回调处理-酒店订单推送接口,构建酒店订单数据加密手机失败：{}", payInfo.getPhone());
        }
        hotelOrder.setCouponId(Objects.isNull(couponInfo) ? null : couponInfo.getCoupon_id());
        hotelOrder.setOrderName(notice.getName());
        hotelOrder.setOrderDesc(notice.getDesc());
        hotelOrder.setOrderAmount(notice.getAmount());
        hotelOrder.setOrderUnit(notice.getUnit());
        hotelOrder.setOrderSource(notice.getSource());
        hotelOrder.setOrderType(notice.getType());
        hotelOrder.setOutOrderId(notice.getOut_order_id());
        hotelOrder.setItemPrice(payInfo.getItem_price());
        hotelOrder.setBreakfastType(payInfo.getBreakfast_type());
        hotelOrder.setCheckInDate(payInfo.getCheckin_date());
        hotelOrder.setCheckOutDate(payInfo.getCheckout_date());
        hotelOrder.setStayDays(payInfo.getStay_days());
        hotelOrder.setTotalPrice(payInfo.getTotal_price());
        hotelOrder.setHotelName(payInfo.getHotel_name());
        hotelOrder.setRoomName(payInfo.getRoom_name());
        hotelOrder.setTotalOriginPrice(payInfo.getTotal_origin_price());
        hotelOrder.setTotalPriceDifference(payInfo.getTotal_price_difference());
        hotelOrder.setRoomCnt(payInfo.getRoom_cnt());
        hotelOrder.setTotalFee(payInfo.getTotal_fee());
        hotelOrder.setCanceledReason(payInfo.getCanceled_reason());
        hotelOrder.setCouponRealPrice(payInfo.getCoupon_real_price());
        hotelOrder.setOrderStatus(statusEnum.getCode());
        return hotelOrder;
    }

    /**
     * 发送酒店短信 - 待确认,已确认,已取消
     */
    private void sendHotelMsg(VirtualOrders order, XsHotelOrderNotice notice,
            HotelOrderStatusEnum statusEnum) {
        try {
            // 【讯奇】
            String signCode = "XUNQ";
            List<String> list = new ArrayList<>();
            // 查询用户信息
            MemberInfo memberInfo = memberCenterModel.getMemberByUserId(order.getUserId());
            if (Objects.isNull(memberInfo)) {
                log.info("异步发送短信时未查到用户信息：{}", order.getUserId());
                return;
            }
            // {$用户姓名}
            list.add(StringUtils.isEmpty(memberInfo.getRealName()) ? "用户"
                    : memberInfo.getRealName());
            switch (statusEnum) {
                case confirming:
                case success:
                    // {$酒店名称}
                    list.add(notice.getPay_info().getHotel_name());
                    break;
            }
            log.info("酒店订单-短信参数值 ={}", JSON.toJSONString(list));
            if (StringUtils.isNotEmpty(statusEnum.getTemplateCode())) {
                commonBusHandler.asyncSendMsg(list.toArray(new String[list.size()]),
                        memberInfo.getMobileDes(), signCode, statusEnum.getTemplateCode());
            }
        } catch (Exception e) {
            LogUtil.printLog(e, "发送酒店订单短信异常");
        }
    }

    /**
     * 橡树-取消虚拟权益下单
     */
    @Override
    public Boolean cancelVirtualOrder(VirtualOrders paramVo) {
        // 目前只支持酒店券直充的取消
        if (RechargeTypeEnum.HOTEL.getCode() != paramVo.getRechargeType()) {
            return null;
        }
        String title = channel + "取消虚拟权益订单";
        String orderSn = paramVo.getOrderSn();
        log.info("{}开始：param:{}", title, JSON.toJSONString(paramVo));
        Response<String> resp = cancelOrder(paramVo);
        String remark;
        if (!resp.isOk()) {
            // 取消失败 - 记录轨迹表
            log.info("{} 取消失败 orderSn:{} msg:{}", title, orderSn, resp.getMsg());
            remark = title + " 取消失败:" + resp.getMsg();
        } else {
            remark = resp.getData();
        }
        ordersTrailModel.saveOrdersTrail(orderSn, OrderStatusEnum.STATUS_RECHARGE_SUCCESS.getCode(),
                OrderStatusEnum.STATUS_RECHARGE_SUCCESS.getCode(), remark);
        log.info("{}结束：result:{} msg={}", title, resp.isOk(),
                resp.isOk() ? "取消成功" : resp.getMsg());
        return true;
    }

    /**
     * 橡树-取消虚拟权益下单
     */
    private Response<String> cancelOrder(VirtualOrders order) {
        String orderSn = order.getOrderSn();
        try {
            // 查询领券信息
            VirtualOrderSupplierCoupon coupon = virtualOrderSupplierCouponDao.selectByOrderSn(
                    orderSn);
            if (Objects.isNull(coupon)) {
                return Response.fail(ErrorMsgEnum.RESP_ERROR.getCode(), "未查询到领券信息");
            }
            if (!coupon.getUseState().equals(SupplierCouponStatusEnum.RECEIVED.getCode())) {
                return Response.fail(ErrorMsgEnum.RESP_ERROR.getCode(),
                        "优惠券状态异常:" + SupplierCouponStatusEnum.getDesc(coupon.getUseState()));
            }
            // 查询优惠券是否已经使用
            List<VirtualSupplierHotelOrder> orders = hotelOrderDao.selectHotelOrderByCouponIds(
                    Collections.singletonList(coupon.getCouponId()));
            // 如果不存在有效的酒店订单，则需要失效优惠券，并且退款虚拟权益订单
            if (CollectionUtils.isEmpty(orders)) {
                // 优惠券无关联的有效酒店订单，则认为券没有使用，需要失效优惠券
                Response resp = invalidCoupon(order, coupon);
                if (!resp.isOk()) {
                    // 券失效失败，发送飞书报警
                    robotUtil.pushMsg(orderSn + "酒店券失效失败:" + resp.getMsg());
                    return Response.fail(ErrorMsgEnum.RESP_ERROR.getCode(),
                            "优惠券失效失败:" + resp.getMsg());
                }
                // 更新券状态 - 已失效
                coupon.setUseState(SupplierCouponStatusEnum.INVALID.getCode());
                virtualOrderSupplierCouponDao.updateVirtualOrderSupplierCouponById(coupon);
                // 记录订单轨迹
                ordersTrailModel.saveOrdersTrail(orderSn,
                        OrderStatusEnum.STATUS_RECHARGE_SUCCESS.getCode(),
                        OrderStatusEnum.STATUS_RECHARGE_SUCCESS.getCode(),
                        "酒店券" + coupon.getCouponId() + "已失效");
                // 是否0元订单
                boolean zeroOrder = order.getOrderMoney().compareTo(BigDecimal.ZERO) == 0;
                // 取消订单退款 0元订单不调用订单中心接口
                if (!zeroOrder) {
                    commonBusHandler.cancelOrder(order, "取消虚拟权益订单");
                }
                // 更新任务表和订单表状态：充值失败
                VirtualOrdersWorker worker = new VirtualOrdersWorker();
                worker.setOrderSn(orderSn);
                worker.setOrderRechargeStatus(OrderRechargeStatusEnum.RECHARGE_FAIL.getCode());
                worker.setOrderRechargeTime(new Date());
                worker.setRemark("虚拟权益订单取消,变更状态为充值失败");
                virtualOrdersWorkerDao.updateVirtualOrdersByOrderSn(worker);

                // 更新订单状态
                VirtualOrders virtualOrders = new VirtualOrders();
                virtualOrders.setId(order.getId());
                virtualOrders.setOrderStatus(OrderStatusEnum.STATUS_RECHARGE_FAIL.getCode());
                virtualOrders.setRemark(OrderStatusEnum.STATUS_RECHARGE_FAIL.getDesc());
                virtualOrders.setRechargeStatus(RechargeStatusEnum.RECHARGE_FAIL.getCode());
                if (!zeroOrder) {
                    virtualOrders.setPayStatus(PayStateEnum.REFUND.getCode());
                }
                virtualOrdersDao.updateVirtualOrdersById(virtualOrders);
                // 通知会员系统充值失败
                commonBusHandler.sendRechargeResultMq(order, MqResultCodeEnum.FAIL);
                String remark =
                        zeroOrder ? "虚拟权益订单取消成功,0元订单不退款" : "虚拟权益订单取消成功";
                return Response.ok(remark);
            }
            return Response.ok("酒店券已使用,虚拟权益订单不需要取消");
        } catch (Exception e) {
            LogUtil.printLog(e, "处理异常");
            robotUtil.pushMsg("取消虚拟权益订单异常，订单号：" + orderSn);
            return Response.fail(ErrorMsgEnum.SYS_ERROR.getCode(), "处理异常");
        }
    }

    /**
     * 橡树-失效优惠券
     */
    private Response invalidCoupon(VirtualOrders order, VirtualOrderSupplierCoupon coupon) {
        String title = channel + "失效优惠券";
        String orderSn = order.getOrderSn();
        try {
            // 联登
            Response<String> loginResp = oakVipLogin(order.getUserId());
            if (!loginResp.isOk()) {
                return Response.fail(ErrorMsgEnum.RESP_ERROR.getCode(),
                        "联登失败" + loginResp.getMsg());
            }
            InvalidCouponReq req = new InvalidCouponReq();
            req.setToken(loginResp.getData());
            req.setCouponIds(Arrays.asList(coupon.getCouponId()));
            Response<String> resp = xsUtil.invalidCoupon(req);
            if (!resp.isOk()) {
                // 接口调用失败
                log.info("{} 接口调用失败：orderSn={} msg={}", title, orderSn, resp.getMsg());
                return Response.fail(ErrorMsgEnum.RESP_ERROR.getCode(),
                        "接口调用失败:" + resp.getMsg());
            }
            if (StringUtils.isEmpty(resp.getData())) {
                log.info("{} 返回对象异常,body为空：orderSn={}", title, orderSn);
                return Response.fail(ErrorMsgEnum.RESP_ERROR.getCode(), "返回对象异常,body为空");
            }
            // 业务参数解析
            XiangshuInvalidCouponResp invalidResp = JSONObject.parseObject(resp.getData(),
                    XiangshuInvalidCouponResp.class);
            if (Objects.isNull(invalidResp)) {
                log.info("{} 返回对象异常,解析失败：orderSn={}", title, orderSn);
                return Response.fail(ErrorMsgEnum.RESP_ERROR.getCode(), "返回对象异常,解析失败");
            }
            // 业务错误-领券失败
            if (!invalidResp.isOk()) {
                log.info("{} 接口失败：orderSn={} msg={}", title, orderSn,
                        invalidResp.getCode() + invalidResp.getMsg());
                return Response.fail(ErrorMsgEnum.RESP_ERROR.getCode(),
                        "接口失败:" + invalidResp.getCode() + invalidResp.getMsg());
            }
            List<XiangshuInvalidCouponDataResp> dataList = invalidResp.getData();
            if (CollectionUtils.isEmpty(dataList)) {
                log.info("{} 返回对象异常,data为空：orderSn={}", title, orderSn);
                return Response.fail(ErrorMsgEnum.RESP_ERROR.getCode(), "返回对象异常,data为空");
            }
            // 目前每笔订单只会领一张券
            XiangshuInvalidCouponDataResp data = dataList.get(0);
            if (!"success".equals(data.getStatus())) {
                log.info("{} 失效状态异常：orderSn={} status={}", title, orderSn, data.getStatus());
                return Response.fail(ErrorMsgEnum.RESP_ERROR.getCode(),
                        "失效状态异常:" + data.getStatus());
            }
        } catch (Exception e) {
            LogUtil.printLog(e, "{} 处理异常", title);
        }
        return Response.ok();
    }
}
