package com.juzifenqi.virtual.core.job;

import com.alibaba.fastjson.JSON;
import com.juzifenqi.virtual.bean.pojo.VirtualOrders;
import com.juzifenqi.virtual.bean.pojo.VirtualOrdersWorker;
import com.juzifenqi.virtual.bean.vo.VirtualOrdersWorkerVo;
import com.juzifenqi.virtual.core.services.impl.third.HandlerContext;
import com.juzifenqi.virtual.dao.VirtualOrdersDao;
import com.juzifenqi.virtual.dao.VirtualOrdersWorkerDao;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.util.ShardingUtil;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * 虚拟商品充值下单定时任务
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/20 9:37 下午
 */
@Component
@Slf4j
public class OrderPushRechargeJob {

    @Autowired
    private HandlerContext         handlerContext;
    @Autowired
    private VirtualOrdersWorkerDao virtualOrdersWorkerDao;
    @Autowired
    private VirtualOrdersDao       virtualOrdersDao;

    @XxlJob("orderPushRechargeJob")
    public ReturnT<String> execute(String param) {
        try {
            ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
            // 当前机器下标
            int index = shardingVO.getIndex();
            int limit = StringUtils.isNotBlank(param) ? Integer.parseInt(param) : 100;
            // 计算数据查询范围
            int start = index * limit;
            log.info("虚拟商品充值下单任务开始，处理数量：{}，当前机器下标：{}，开始查询下标：{}", limit,
                    index, start);
            List<VirtualOrdersWorker> virtualOrdersWorkers = virtualOrdersWorkerDao.getAllNeedPushOrders(
                    start, limit);
            log.info("当前待充值下单的订单有{}单", virtualOrdersWorkers.size());
            if (CollectionUtils.isEmpty(virtualOrdersWorkers)) {
                return ReturnT.SUCCESS;
            }
            for (VirtualOrdersWorker virtualOrdersWorker : virtualOrdersWorkers) {
                String orderSn = virtualOrdersWorker.getOrderSn();
                try {
                    log.info("批量处理-单条虚拟权益充值开始，orderSn：{}", orderSn);
                    VirtualOrders order = virtualOrdersDao.getVirtualOrdersByOrderSn(orderSn);
                    log.info("order = {}", JSON.toJSONString(order));
                    if (order == null) {
                        log.info("充值时未查询到有效订单，orderSn：{}", orderSn);
                        continue;
                    }
                    VirtualOrdersWorkerVo paramVo = new VirtualOrdersWorkerVo();
                    BeanUtils.copyProperties(virtualOrdersWorker, paramVo);
                    paramVo.setSupplierId(order.getSupplierId());
                    paramVo.setVirtualOrder(order);
                    handlerContext.orderRecharge(paramVo);
                    log.info("批量处理-单条虚拟权益充值完成，orderSn：{}", orderSn);
                } catch (Exception e) {
                    log.info("虚拟权益充值时出现未知异常，orderSn：{}", orderSn,e);
                }
            }
            log.info("虚拟商品充值下单任务结束");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("虚拟商品充值下单任务执行发生异常", e);
        }
        return ReturnT.FAIL;
    }
}
