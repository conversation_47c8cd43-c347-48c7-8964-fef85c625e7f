package com.juzifenqi.virtual.core.services.impl.third.fulu.enums;

/**
 * 福禄方法枚举
 *
 * <AUTHOR>
 * @date 2023-11-08 17:33:23
 */
public enum MethodEnum {

    STOCK_CHECK("fulu.goods.stock.check", "校验库存"),
    DIRECT_CREATE_ORDER("fulu.order.direct.add", "充值下单"),
    CARD_CREATE_ORDER("fulu.order.card.add", "卡密下单"),
    QUERY_ORDER("fulu.order.info.get", "查询订单"),
    QUERY_PRODUCT("fulu.goods.info.get", "查询商品信息"),
    ;

    private String code;
    private String name;

    MethodEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
