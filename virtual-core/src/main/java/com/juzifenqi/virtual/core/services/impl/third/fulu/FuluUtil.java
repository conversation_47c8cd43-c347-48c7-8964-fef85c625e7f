package com.juzifenqi.virtual.core.services.impl.third.fulu;

import com.alibaba.cloudapi.sdk.client.ApacheHttpClient;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.groot.utils.core.util.ObjectUtil;
import com.groot.utils.crypto.MD5Utils;
import com.groot.utils.http.OKHttp3SimpleUtils;
import com.groot.utils.http.OkHttpClientEnum;
import com.juzifenqi.virtual.bean.system.Response;
import com.juzifenqi.virtual.component.enums.DateEnum;
import com.juzifenqi.virtual.component.enums.RechargeTypeEnum;
import com.juzifenqi.virtual.component.util.DateUtils;
import com.juzifenqi.virtual.core.services.impl.third.common.enums.ErrorMsgEnum;
import com.juzifenqi.virtual.core.services.impl.third.fulu.enums.MethodEnum;
import com.juzifenqi.virtual.core.services.impl.third.fulu.model.OrderReq;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;


/**
 * 福禄 工具类
 *
 * <AUTHOR>
 * @date 2023-11-08 13:33:23
 */
@Slf4j
public class FuluUtil extends ApacheHttpClient {

    /**
     * 接口url
     */
    String url;
    /**
     * app_key
     */
    String appKey;
    /**
     * 应用密钥
     */
    String appSecret;

    public FuluUtil(String url, String appKey, String appSecret) {
        this.url = url;
        this.appKey = appKey;
        this.appSecret = appSecret;
    }

    /**
     * Md5加密
     */
    private String sign(Map<String, Object> map) {
        //1.构建待签名的json null也要参与加密
        String dataJson = JSON.toJSONString(map, SerializerFeature.WriteMapNullValue);
        log.info("=====>Md5加密内容dataJson=" + dataJson);
        //2.将Json字符串转化为字符数组charObjectArray，然后将charObjectArray进行Array.Sort()排序
        char[] charArray = dataJson.toCharArray();
        Arrays.sort(charArray);
        //3.签名
        String signStr = String.format("%s%s", new String(charArray), appSecret);
        log.info("=====>Md5加密结果signStr=" + signStr);
        return MD5Utils.md5(signStr).toLowerCase();
    }

    /**
     * Md5加密-临时对外
     */
    public String signOut(Map<String, Object> map) {
        return sign(map);
    }

    /**
     * httpPost
     */
    private Response<JSONObject> httpPost(MethodEnum method, String body) {
        log.info("=====>调取福禄接口:{}开始, url={}, param={}", method.getName(), url, body);
        long costTimeStart = System.currentTimeMillis();
        JSONObject resultJson = OKHttp3SimpleUtils.postByJson(url, body, OkHttpClientEnum.TWO_SECOND);
        long costTimeEnd = System.currentTimeMillis();
        long totalTimeCost = costTimeEnd - costTimeStart;
        if (resultJson == null) {
            return Response.fail(ErrorMsgEnum.RESP_ERROR.getCode(), String.format(ErrorMsgEnum.RESP_ERROR.getMessage(), "调用返回resultJson为空"));
        }
        log.info("=====>调取福禄接口:{} 返回结果,总耗时{},result={}", method.getName(), totalTimeCost,
                resultJson.toJSONString());
        JSONObject jsonObject = resultJson.getJSONObject("responseVal");
        if (jsonObject == null) {
            return Response.fail(ErrorMsgEnum.RESP_ERROR.getCode(), String.format(ErrorMsgEnum.RESP_ERROR.getMessage(), "调用返回报文为空"));
        }
        // 返回数据判断 0成功 其余失败
        if (jsonObject.getIntValue("code") != 0) {
            // 接口业务失败
            return Response.fail(ErrorMsgEnum.BUS_ERROR.getCode(),
                    String.format(ErrorMsgEnum.BUS_ERROR.getMessage(), jsonObject.getIntValue("code"), jsonObject.getString("message")));
        }
        // 返回验签
        if (StringUtils.isEmpty(jsonObject.getString("sign"))) {
            return Response.fail(ErrorMsgEnum.RESP_ERROR.getCode(), String.format(ErrorMsgEnum.RESP_ERROR.getMessage(), "对象或者签名为空"));
        }
        JSONObject resultObj = jsonObject.getJSONObject("result");
        if (resultObj == null) {
            return Response.fail(ErrorMsgEnum.RESP_ERROR.getCode(), String.format(ErrorMsgEnum.RESP_ERROR.getMessage(), "result为空"));
        }
        String sign = sign(resultObj);
        if (!jsonObject.getString("sign").equals(sign)) {
            return Response.fail(ErrorMsgEnum.RESP_ERROR.getCode(),
                    String.format(ErrorMsgEnum.RESP_ERROR.getMessage(), "签名校验失败"));
        }
        return Response.ok(resultObj);
    }

    /**
     * 请求map初始化
     */
    private Map<String, Object> initMap(MethodEnum method) {
        Map<String, Object> map = new HashMap<>(9);
        map.put("app_key", appKey);
        map.put("method", method.getCode());
        map.put("timestamp", DateUtils.dateToStr(new Date(), DateEnum.DATE_FORMAT));
        map.put("version", "2.0");
        map.put("format", "json");
        map.put("charset", "utf-8");
        map.put("sign_type", "md5");
        // 默认""
        map.put("app_auth_token", "");
        return map;
    }

    /**
     * 校验库存
     *
     * @param req 商品编码+购买数量
     */
    public Response<JSONObject> stockCheck(OrderReq req) {
        MethodEnum methodEnum = MethodEnum.STOCK_CHECK;
        // 1.校验入参
        if (req == null) {
            return Response.fail(ErrorMsgEnum.PARAMS_CANNOT_BE_NULL.getCode(),
                    String.format(ErrorMsgEnum.PARAMS_CANNOT_BE_NULL.getMessage(), "obj"));
        }
        if (StringUtils.isEmpty(req.getProductId())) {
            return Response.fail(ErrorMsgEnum.PARAMS_CANNOT_BE_NULL.getCode(),
                    String.format(ErrorMsgEnum.PARAMS_CANNOT_BE_NULL.getMessage(), "productId"));
        }
        if (req.getBuyNum() == null || req.getBuyNum() <= 0) {
            return Response.fail(ErrorMsgEnum.PARAMS_CANNOT_BE_NULL.getCode(),
                    String.format(ErrorMsgEnum.PARAMS_CANNOT_BE_NULL.getMessage(), "buyNum"));
        }
        // 2.组装业务数据
        Map<String, Object> map = initMap(methodEnum);
        Map<String, Object> subMap = new HashMap<>(2);
        // 福禄商品编码
        subMap.put("product_id", req.getProductId());
        // 购买数量 默认1
        subMap.put("buy_num", req.getBuyNum().toString());
        map.put("biz_content", JSON.toJSONString(subMap));
        // 加密 null值也要参与加密
        String sign = sign(map);
        map.put("sign", sign);
        // 3.接口调用
        return httpPost(methodEnum, JSON.toJSONString(map));
    }

    /**
     * 下单
     *
     * @param req 下单类型+商品+购买数量+外部单号+充值账号
     */
    public Response<JSONObject> createOrder(OrderReq req) {
        // 1.校验入参
        if (req == null) {
            return Response.fail(ErrorMsgEnum.PARAMS_CANNOT_BE_NULL.getCode(),
                    String.format(ErrorMsgEnum.PARAMS_CANNOT_BE_NULL.getMessage(), "obj"));
        }
        if (StringUtils.isEmpty(req.getProductId())) {
            return Response.fail(ErrorMsgEnum.PARAMS_CANNOT_BE_NULL.getCode(),
                    String.format(ErrorMsgEnum.PARAMS_CANNOT_BE_NULL.getMessage(), "productId"));
        }
        if (req.getBuyNum() == null || req.getBuyNum() <= 0) {
            return Response.fail(ErrorMsgEnum.PARAMS_CANNOT_BE_NULL.getCode(),
                    String.format(ErrorMsgEnum.PARAMS_CANNOT_BE_NULL.getMessage(), "buyNum"));
        }
        if (req.getRechargeType() <= 0) {
            return Response.fail(ErrorMsgEnum.PARAMS_CANNOT_BE_NULL.getCode(),
                    String.format(ErrorMsgEnum.PARAMS_CANNOT_BE_NULL.getMessage(), "orderType"));
        }
        if (StringUtils.isEmpty(req.getCustomerOrderNo())) {
            return Response.fail(ErrorMsgEnum.PARAMS_CANNOT_BE_NULL.getCode(),
                    String.format(ErrorMsgEnum.PARAMS_CANNOT_BE_NULL.getMessage(), "customerOrderNo"));
        }
        // 直充类型
        if (RechargeTypeEnum.DIRECT.getCode() == req.getRechargeType()) {
            if (StringUtils.isEmpty(req.getChargeAccount())) {
                return Response.fail(ErrorMsgEnum.PARAMS_CANNOT_BE_NULL.getCode(),
                        String.format(ErrorMsgEnum.PARAMS_CANNOT_BE_NULL.getMessage(),
                                "chargeAccount"));
            }
        }
        // 2.组装业务数据
        MethodEnum methodEnum = RechargeTypeEnum.DIRECT.getCode() == req.getRechargeType() ? MethodEnum.DIRECT_CREATE_ORDER : MethodEnum.CARD_CREATE_ORDER;
        Map<String, Object> map = initMap(methodEnum);
        Map<String, Object> subMap = new HashMap<>(4);
        // 福禄商品编码
        subMap.put("product_id", req.getProductId());
        // 购买数量 默认1
        subMap.put("buy_num", req.getBuyNum().toString());
        // 桔子订单号
        subMap.put("customer_order_no", req.getCustomerOrderNo());
        if (RechargeTypeEnum.DIRECT.getCode() == req.getRechargeType()) {
            // 充值账号
            subMap.put("charge_account", req.getChargeAccount());
        }
        map.put("biz_content", JSON.toJSONString(subMap));
        // 加密 null值也要参与加密
        String sign = sign(map);
        map.put("sign", sign);
        // 3.接口调用
        return httpPost(methodEnum, JSON.toJSONString(map));
    }

    /**
     * 订单查询
     *
     * @param req 外部单号
     */
    public Response<JSONObject> queryOrder(OrderReq req) {
        MethodEnum methodEnum = MethodEnum.QUERY_ORDER;
        // 1.校验入参
        if (req == null) {
            return Response.fail(ErrorMsgEnum.PARAMS_CANNOT_BE_NULL.getCode(),
                    String.format(ErrorMsgEnum.PARAMS_CANNOT_BE_NULL.getMessage(), "obj"));
        }
        if (StringUtils.isEmpty(req.getCustomerOrderNo())) {
            return Response.fail(ErrorMsgEnum.PARAMS_CANNOT_BE_NULL.getCode(),
                    String.format(ErrorMsgEnum.PARAMS_CANNOT_BE_NULL.getMessage(), "customerOrderNo"));
        }
        // 2.组装业务数据
        Map<String, Object> map = initMap(methodEnum);
        Map<String, Object> subMap = new HashMap<>(2);
        // 桔子订单号
        subMap.put("customer_order_no", req.getCustomerOrderNo());
        map.put("biz_content", JSON.toJSONString(subMap));
        // 加密 null值也要参与加密
        String sign = sign(map);
        map.put("sign", sign);
        // 3.接口调用
        return httpPost(methodEnum, JSON.toJSONString(map));
    }

    /**
     * 验签
     *
     * @param jsonObject 验签对象
     * @param signStr 签名
     */
    public Boolean verifySign(JSONObject jsonObject, String signStr) {
        if (ObjectUtil.isNull(jsonObject)) {
            return false;
        }
        // JsonObject转map
        Map<String, Object> map= jsonObject;
        // 剔除sign
        map.remove("sign");
        // 加密sign null值也要参与加密
        String sign_str = sign(map);
        log.info("=====>验签: signStr={}, sign_str={}", signStr, sign_str);
        // 进行加密比对
        return signStr.equals(sign_str);
    }

    /**
     * 获取福禄商品信息
     */
    public Response<JSONObject> queryProduct(String productId) {
        MethodEnum methodEnum = MethodEnum.QUERY_PRODUCT;
        // 组装业务数据
        Map<String, Object> map = initMap(methodEnum);
        Map<String, Object> subMap = new HashMap<>(2);
        // 商品id
        subMap.put("product_id", productId);
        // 商品详情输出格式 0.纯文本 1.json格式。不传默认以纯文本方式显示
        subMap.put("detail_format", 1);
        map.put("biz_content", JSON.toJSONString(subMap));
        // 加密 null值也要参与加密
        String sign = sign(map);
        map.put("sign", sign);
        // 3.接口调用
        return httpPost(methodEnum, JSON.toJSONString(map));
    }
}
