package com.juzifenqi.virtual.core.utils;

import com.juzifenqi.virtual.component.util.DateUtils;
import java.util.Date;
import java.util.Random;

/**
 * 流水号utils
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/8/31 11:30
 */
public class SerialNoUtils {

    /**
     * 生成请求流水号（每次唯一） 前缀：HK（划扣）、JSDF（结算代付） busNo：业务单号
     */
    public static String generateApplySerialNo(String prefix, String busNo) {
        // 前缀+会员单号/结算单号+当前时间戳(毫秒)
        return prefix + busNo + System.currentTimeMillis();
    }

}
