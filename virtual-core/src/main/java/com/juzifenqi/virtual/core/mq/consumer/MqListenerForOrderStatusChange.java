package com.juzifenqi.virtual.core.mq.consumer;


import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.bean.ConsumerBean;
import com.juzifenqi.mq.consumer.normal.NormalConsumerClient;
import com.juzifenqi.virtual.bean.dto.OrderInfoDto;
import com.juzifenqi.virtual.bean.pojo.VirtualOrders;
import com.juzifenqi.virtual.component.enums.OrderStatusEnum;
import com.juzifenqi.virtual.component.models.VirtualOrdersTrailModel;
import com.juzifenqi.virtual.core.conf.VirtualMqConfig;
import com.juzifenqi.virtual.core.models.VirtualOrderModel;
import com.juzifenqi.virtual.dao.VirtualOrdersDao;
import java.nio.charset.StandardCharsets;
import javax.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.StringUtils;

/**
 * 订单状态变更
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/9 15:41
 */
@Configuration
@Slf4j
public class MqListenerForOrderStatusChange extends NormalConsumerClient {

    @Autowired
    private VirtualMqConfig         mqConfig;
    @Autowired
    private VirtualOrdersDao        virtualOrdersDao;
    @Autowired
    private VirtualOrdersTrailModel ordersTrailModel;
    @Autowired
    private VirtualOrderModel       virtualOrderModel;

    @PostConstruct
    public ConsumerBean initOrderSucListener() {
        return initConsumer(mqConfig.getOrderStatusChangeGid(),
                mqConfig.getOrderStatusChangeTopic());
    }

    @Override
    protected boolean dealMessage(Message message, ConsumeContext consumeContext) {
        dealMqMsg(message);
        return true;
    }

    private boolean dealMqMsg(Message message) {
        try {
            String body = new String(message.getBody(), StandardCharsets.UTF_8);
            log.info("监听订单状态变更消息：{}", body);
            if (StringUtils.isEmpty(body)) {
                return true;
            }
            OrderInfoDto orderInfoDto = JSONObject.parseObject(body, OrderInfoDto.class);
            String orderSn = orderInfoDto.getOrderSn();
            // 订单取消节点
            if (!"QXDD".equals(orderInfoDto.getOrderNode())) {
                log.info("订单状态变更非取消订单节点：{}", orderSn);
                return true;
            }
            VirtualOrders order = virtualOrdersDao.getVirtualOrdersByOrderSn(orderSn);
            if (order == null) {
                log.info("订单状态变更非虚拟权益订单,不作处理：{}", orderSn);
                return true;
            }
            if (OrderStatusEnum.STATUS_MALL_NO_PAY.getCode() != order.getOrderStatus()) {
                log.info("订单状态变更虚拟订单非待支付状态,不作处理：{}", orderSn);
                return true;
            }
            // 系统闭单
            virtualOrdersDao.updateOrderStatusByOrderSn(orderSn,
                    OrderStatusEnum.SYSTEM_CLOSE.getCode(), OrderStatusEnum.SYSTEM_CLOSE.getDesc());
            // 记录轨迹
            ordersTrailModel.saveOrdersTrail(orderSn, OrderStatusEnum.STATUS_MALL_NO_PAY.getCode(),
                    OrderStatusEnum.SYSTEM_CLOSE.getCode(), OrderStatusEnum.SYSTEM_CLOSE.getDesc());

            // 更新请求流水状态
            virtualOrderModel.updateOrderPayFlowForPayTimeout(orderSn);
            log.info("监听订单状态变更消息处理完毕：{}", orderSn);
            return true;
        } catch (Exception e) {
            log.error("监听订单状态变更异常", e);
            return false;
        }
    }

}
