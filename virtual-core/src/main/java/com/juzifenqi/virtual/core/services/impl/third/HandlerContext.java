package com.juzifenqi.virtual.core.services.impl.third;

import com.juzifenqi.virtual.bean.pojo.VirtualOrders;
import com.juzifenqi.virtual.bean.vo.CardDecodeRespVo;
import com.juzifenqi.virtual.bean.vo.HotelOrderDetailVo;
import com.juzifenqi.virtual.bean.vo.HotelUrlVo;
import com.juzifenqi.virtual.bean.vo.RechargeRespVo;
import com.juzifenqi.virtual.bean.vo.VirtualOrdersCardVo;
import com.juzifenqi.virtual.bean.vo.VirtualOrdersWorkerVo;
import com.juzifenqi.virtual.component.enums.SupplierEnum;
import com.juzifenqi.virtual.component.exception.VirtualException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 三方权益上下文
 *
 * <AUTHOR>
 * @date 2023-07-14 10:33:23
 */
@Slf4j
@Component
public class HandlerContext {

    @Autowired
    private SpringUtils springUtils;

    private AbstractStrategyHandler getInstance(Integer code) {
        String beanName = SupplierEnum.getBeanNameByCode(code);
        if (beanName == null) {
            throw new VirtualException("类配置异常，请检查SupplierEnum枚举！code=" + code);
        }
        AbstractStrategyHandler handler = (AbstractStrategyHandler) springUtils.getBean(beanName);
        if (handler == null) {
            throw new VirtualException("获取策略失败，请检查SupplierEnum枚举！code=" + code);
        }
        return handler;
    }

    /**
     * 虚拟权益下单（调用三方充值）
     */
    public RechargeRespVo orderRecharge(VirtualOrdersWorkerVo paramVo) {
        AbstractStrategyHandler handler = getInstance(paramVo.getSupplierId());
        return handler.orderRecharge(paramVo);
    }

    /**
     * 充值结果查证
     */
    public RechargeRespVo getRechargeStatus(VirtualOrdersWorkerVo paramVo) {
        AbstractStrategyHandler handler = getInstance(paramVo.getSupplierId());
        return handler.getRechargeStatus(paramVo);
    }

    /**
     * 卡密解析
     */
    public CardDecodeRespVo decodeCard(VirtualOrdersCardVo paramVo) {
        AbstractStrategyHandler handler = getInstance(paramVo.getSupplierId());
        return handler.decodeCard(paramVo);
    }

    /**
     * 获取去预定酒店跳转url
     */
    public HotelUrlVo getHotelUrl(VirtualOrdersWorkerVo paramVo) {
        AbstractStrategyHandler handler = getInstance(paramVo.getSupplierId());
        return handler.getHotelUrl(paramVo);
    }

    /**
     * 获取查看酒店订单详情需要的参数
     */
    public HotelOrderDetailVo getHotelOrderDetail(VirtualOrdersWorkerVo paramVo) {
        AbstractStrategyHandler handler = getInstance(paramVo.getSupplierId());
        return handler.getHotelOrderDetail(paramVo);
    }

    /**
     * 取消虚拟权益订单
     */
    public Boolean cancelVirtualOrder(VirtualOrders paramVo) {
        AbstractStrategyHandler handler = getInstance(paramVo.getSupplierId());
        return handler.cancelVirtualOrder(paramVo);
    }
}
