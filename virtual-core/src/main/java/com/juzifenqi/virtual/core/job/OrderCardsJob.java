package com.juzifenqi.virtual.core.job;

import com.groot.utils.exception.LogUtil;
import com.juzifenqi.virtual.bean.pojo.VirtualOrders;
import com.juzifenqi.virtual.bean.pojo.VirtualOrdersCard;
import com.juzifenqi.virtual.bean.vo.VirtualOrdersCardVo;
import com.juzifenqi.virtual.component.enums.VirtualCardStateEnum;
import com.juzifenqi.virtual.core.services.impl.third.HandlerContext;
import com.juzifenqi.virtual.dao.VirtualOrdersCardDao;
import com.juzifenqi.virtual.dao.VirtualOrdersDao;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.util.ShardingUtil;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * 查询卡密定时任务
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/10 16:37 下午
 */
@Component
@Slf4j
public class OrderCardsJob {

    @Resource
    private VirtualOrdersCardDao virtualOrdersCardDao;
    @Resource
    private VirtualOrdersDao     virtualOrdersDao;
    @Autowired
    private HandlerContext       handlerContext;

    /**
     * 待查询卡密任务处理
     */
    @XxlJob("getFuluOrderCardsJob")
    public ReturnT<String> getFuluOrderCardsJob(String param) {
        try {
            int limit = StringUtils.isNotBlank(param) ? Integer.parseInt(param) : 100;
            // 分片查询待处理数据
            ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
            // 当前机器下标
            int index = shardingVO.getIndex();
            // 计算数据查询范围
            int start = index * limit;
            log.info("福禄查询卡密任务开始，处理数量：{}，当前机器下标：{}，开始查询下标：{}", limit,
                    index, start);
            // 查询待处理数据
            List<VirtualOrdersCard> virtualOrdersCards = virtualOrdersCardDao.getAllNeedDecodeCardsByTimeLimit(
                    VirtualCardStateEnum.DECODE_WAIT.getCode(), start, limit);
            log.info("福禄查询卡密当前待正常处理的任务数量：size:{}", virtualOrdersCards.size());
            if (CollectionUtils.isEmpty(virtualOrdersCards)) {
                return ReturnT.SUCCESS;
            }
            handleData(VirtualCardStateEnum.DECODE_WAIT.getCode(), virtualOrdersCards);
            log.info("福禄查询卡密任务结束");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            LogUtil.printLog(e, "福禄查询卡密正常任务异常");
        }
        return ReturnT.FAIL;
    }

    /**
     * 查询异常卡密任务处理
     */
    @XxlJob("getFuluErrorOrderCardsJob")
    public ReturnT<String> getFuluErrorOrderCardsJob(String param) {
        try {
            int limit = StringUtils.isNotBlank(param) ? Integer.parseInt(param) : 100;
            log.info("福禄查询卡密异常任务开始");
            // 查询待处理数据
            List<VirtualOrdersCard> virtualOrdersCards = virtualOrdersCardDao.getAllNeedDecodeCardsByTimeLimit(
                    VirtualCardStateEnum.DECODE_FAIL.getCode(), 0, limit);
            log.info("福禄查询卡密当前待异常处理的任务数量：size:{}", virtualOrdersCards.size());
            if (CollectionUtils.isEmpty(virtualOrdersCards)) {
                return ReturnT.SUCCESS;
            }
            handleData(VirtualCardStateEnum.DECODE_FAIL.getCode(), virtualOrdersCards);
            log.info("福禄查询卡密异常任务结束");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            LogUtil.printLog(e, "福禄查询卡密异常任务处理异常");
        }
        return ReturnT.FAIL;
    }


    /**
     * 处理数据
     */
    private void handleData(Integer status, List<VirtualOrdersCard> virtualOrdersCards) {
        // 批量更新任务状态
        virtualOrdersCardDao.batchUpdateCardsStateByIds(
                virtualOrdersCards.stream().map(VirtualOrdersCard::getId)
                        .collect(Collectors.toList()), VirtualCardStateEnum.DECODE_ING.getCode(),
                status);
        // 循环处理
        for (VirtualOrdersCard card : virtualOrdersCards) {
            String orderSn = card.getOrderSn();
            try {
                log.info("福禄查询卡密处理单条开始，orderSn:{}", orderSn);
                VirtualOrders order = virtualOrdersDao.getVirtualOrdersByOrderSn(orderSn);
                if (order == null) {
                    log.info("福禄查询卡密未查询到有效订单，orderSn:{}", orderSn);
                    continue;
                }
                VirtualOrdersCardVo paramVo = new VirtualOrdersCardVo();
                paramVo.setSupplierId(order.getSupplierId());
                paramVo.setVirtualOrdersCard(card);
                handlerContext.decodeCard(paramVo);
                log.info("福禄查询卡密单条完成，orderSn:{}", orderSn);
            } catch (Exception e) {
                LogUtil.printLog("福禄查询卡密单条出现未知异常，orderSn:{}", orderSn);
            }
        }
    }
}
