package com.juzifenqi.virtual.core.conf;

import com.xxl.job.core.executor.impl.XxlJobSpringExecutor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@Slf4j
public class XxlJobConfig {

    @Value("${xxl.job.admin.addresses}")
    public String jobAddress;
    @Value("${xxl.job.executor.appname}")
    public String jobAppname;
    @Value("${xxl.job.executor.port}")
    public int    jobPort;

    @Bean
    public XxlJobSpringExecutor xxlJobExecutor() {
        log.info(">>>>>>>>>>>>>>>>>>>>>>>xxljob-init-start>>>>>>>>>>>>>>>>>>>>>>>");
        log.info("参数:jobAddress：{} jobAppname：{} jobPort：{}", jobAddress, jobAppname, jobPort);
        XxlJobSpringExecutor xxlJobSpringExecutor = new XxlJobSpringExecutor();
        xxlJobSpringExecutor.setAdminAddresses(jobAddress);
        xxlJobSpringExecutor.setAppname(jobAppname);
        xxlJobSpringExecutor.setPort(jobPort);
        xxlJobSpringExecutor.setLogRetentionDays(7);
        log.info(">>>>>>>>>>>>>>>>>>>>>>>xxljob-init-end>>>>>>>>>>>>>>>>>>>>>>>");
        return xxlJobSpringExecutor;
    }
}

