package com.juzifenqi.virtual.core.aspect;

import com.alibaba.dubbo.rpc.RpcContext;
import com.alibaba.fastjson.JSONObject;
import com.juzifenqi.virtual.bean.system.VirtualResult;
import com.juzifenqi.virtual.component.enums.VirtualErrorEnum;
import com.juzifenqi.virtual.component.exception.VirtualException;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.MDC;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2017/10/20 10:36
 */
@Slf4j
@Aspect
@Component
@Order(-2)//保证该AOP在数据源AOP之前执行
public class CoreAspect {

    private final static String TRACE_KEY = "traceId";

    @Pointcut("execution(public * com.juzifenqi.virtual.api..*.*(..))")
    public void plusAspectGlobal() {
    }

    @Around("plusAspectGlobal()")
    public Object doAround(ProceedingJoinPoint joinPoint) {
        long startTime = System.currentTimeMillis();
        String requestUUID = UUID.randomUUID().toString().replace("-", "");
        RpcContext rpcContext = RpcContext.getContext();
        String remoteHost = rpcContext.getRemoteHost();
        String methodName = joinPoint.getSignature().getName();
        log.info("traceStart {} methodName {} remote ip is {}", requestUUID, methodName,
                remoteHost);
        MDC.put(TRACE_KEY, requestUUID);
        Object proceedResult;
        try {
            printParams(joinPoint, methodName);
            proceedResult = joinPoint.proceed();
        } catch (Throwable throwable) {
            proceedResult = handlerException(throwable);
        }
        MDC.remove(TRACE_KEY);
        long endTime = System.currentTimeMillis();
        log.info("traceEnd {} executed {} ms ---> return result is {}", requestUUID,
                endTime - startTime, JSONObject.toJSONString(proceedResult));
        return proceedResult;
    }

    private void printParams(JoinPoint joinPoint, String methodName) {
        try {
            Object[] objects = joinPoint.getArgs();
            if (objects != null && objects.length > 0) {
                StringBuilder dbParams = new StringBuilder();
                dbParams.append("[method]:").append(methodName).append("[params]:");
                dbParams.append(JSONObject.toJSONString(objects[0]));
                log.info("params ---> :{}", dbParams);
            }
        } catch (Exception e) {
            log.error("打印请求参数异常 ", e);
        }
    }

    private Object handlerException(Throwable exception) {
        log.info("进入异常处理机制...");
        log.info("errorMsg:{}", exception.getMessage());
        VirtualResult supportResult = new VirtualResult();
        supportResult.error();
        if (exception instanceof VirtualException) {
            log.info("出现业务异常 ------------------->", exception);
            String eCode = ((VirtualException) exception).getErrorCode();
            supportResult.error(eCode, exception.getMessage());
        } else {
            log.error("出现未知异常 ------------------->", exception);
            supportResult.error(VirtualErrorEnum.ERROR_100100.getCode(),
                    VirtualErrorEnum.ERROR_100100.getMessage());
        }
        log.info("异常处理机制返回信息:{}", JSONObject.toJSONString(supportResult));
        return supportResult;
    }
}
