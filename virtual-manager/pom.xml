<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <artifactId>virtual-center</artifactId>
        <groupId>com.juzifenqi</groupId>
        <version>ykd-1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>virtual-manager</artifactId>
    <version>ykd-1.0.0-SNAPSHOT</version>
    <name>trigger-manager</name>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.juzifenqi</groupId>
            <artifactId>virtual-component</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.groot.utils</groupId>
            <artifactId>groot-all</artifactId>
            <version>1.0.4.7-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.juzifenqi.sorder</groupId>
            <artifactId>super-order-service-client</artifactId>
            <version>1.5.7.2-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--商品服务-->
        <dependency>
            <groupId>com.juzifenqi.product</groupId>
            <artifactId>product-service-client</artifactId>
            <version>1.1.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>alita-dubbo-client</artifactId>
                    <groupId>com.juzifenqi</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>activity</groupId>
            <artifactId>activity-bean</artifactId>
            <version>1.0.8.5-SNAPSHOT</version>
        </dependency>

        <!--用户中心-->
        <dependency>
            <groupId>com.juzifenqi</groupId>
            <artifactId>member-platform-api</artifactId>
            <version>2.1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.juzifenqi</groupId>
            <artifactId>plus-abyss-api</artifactId>
            <version>ykd-1.0.7</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
