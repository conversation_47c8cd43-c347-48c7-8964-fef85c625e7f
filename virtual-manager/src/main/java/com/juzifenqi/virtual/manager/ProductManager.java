package com.juzifenqi.virtual.manager;

import com.alibaba.fastjson.JSON;
import com.groot.utils.exception.LogUtil;
import com.juzifenqi.product.dto.ProductServiceResult;
import com.juzifenqi.product.entity.Product;
import com.juzifenqi.product.entity.ProductGoods;
import com.juzifenqi.product.service.IProductGoodsService;
import com.juzifenqi.product.service.IProductService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 和商品交互
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/22 1:54 下午
 */
@Component
@Slf4j
public class ProductManager {

    @Autowired
    private IProductGoodsService iProductGoodsService;
    @Autowired
    private IProductService      iProductService;

    /**
     * <AUTHOR>
     * @version 1.0
     * @date 2022/1/22 1:56 下午
     */
    public ProductGoods getProductGoodsBySku(String sku) {
        try {
            log.info("请求商品服务获取goods信息开始：sku:{}", sku);
            ProductServiceResult<ProductGoods> productGoodsBySku =
                    iProductGoodsService.getProductGoodsBySku(
                    sku);
            log.info("请求商品服务获取goods信息返回：result:{}", JSON.toJSONString(productGoodsBySku));
            if (productGoodsBySku.getSuccess() && productGoodsBySku.getResult() != null) {
                return productGoodsBySku.getResult();
            }
        } catch (Exception e) {
            LogUtil.printLog("请求商品服务异常", e);
        }
        return null;
    }

    /**
     * <AUTHOR>
     * @version 1.0
     * @date 2022/1/22 1:56 下午
     */
    public Product getProductById(Integer productId) {
        try {
            log.info("请求商品服务获取商品信息开始：productId:{}", productId);
            ProductServiceResult<Product> productServiceProduct = iProductService.getById(
                    productId);
            log.info("请求商品服务获取商品信息返回:{}", JSON.toJSONString(productServiceProduct));
            if (productServiceProduct.getSuccess() && productServiceProduct.getResult() != null) {
                return productServiceProduct.getResult();
            }
        } catch (Exception e) {
            LogUtil.printLog("请求商品服务异常", e);
        }
        return null;
    }
}
