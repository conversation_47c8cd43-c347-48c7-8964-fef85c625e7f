package com.juzifenqi.virtual.manager;

import com.alibaba.fastjson.JSON;
import com.groot.utils.exception.LogUtil;
import com.juzifenqi.alita.result.BaseResult;
import com.juzifenqi.core.ServiceResult;
import com.juzifenqi.order.service.IOrderService;
import com.juzifenqi.order.vo.OrderCancelRefundResultVO;
import com.juzifenqi.order.vo.OrderCancelRefundVO;
import com.juzifenqi.service.OldBeanJzfqIOrdersOtherService;
import com.juzifenqi.virtual.component.feishu.FeiShuModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 订单交互
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/22 11:39 上午
 */
@Component
@Slf4j
public class OrderManager {

    @Autowired
    private OldBeanJzfqIOrdersOtherService oldBeanJzfqIOrdersOtherService;
    @Autowired
    private IOrderService iOrderService;
    @Autowired
    private FeiShuModel   feiShuModel;

    /**
     * 调订单服务更改订单状态,充值失败的进行退款
     *
     * <AUTHOR>
     * @version 1.0
     * @date 2022/1/22 11:49 上午
     */
    public Boolean cancelOrderForCusys(String orderSn, String reason, String operatorName) {
        try {
            log.info("请求订单服务更改订单状态参数orderSn:{},reason:{},operatorName:{}", orderSn,
                    reason, operatorName);
            ServiceResult<Boolean> result = oldBeanJzfqIOrdersOtherService.cancelOrderForCusys(
                    orderSn, reason, operatorName);
            log.info("请求订单服务更改订单状态返回,result:{}", JSON.toJSONString(result));
            return result.getResult();
        } catch (Exception e) {
            log.error("请求订单服务更改订单状态异常:", e);
            return false;
        }
    }

    /**
     * 调订单服务取消订单
     */
    public OrderCancelRefundResultVO closeOrderRefund(OrderCancelRefundVO vo) {
        try {
            log.info("调用订单中心取消订单入参为：{}", JSON.toJSONString(vo));
            BaseResult<OrderCancelRefundResultVO> result = iOrderService.closeOrderRefund(vo);
            log.info("调用订单中心取消订单返回为：{}", JSON.toJSONString(result));
            if (result.getCode() != 200 || result.getData() == null) {
                log.info("调用订单中心取消订单返回结果错误,请稍后再试!");
                // 退款调用失败发飞书
                feiShuModel.sendSysTextMsg(
                        "虚拟权益充值失败/存疑自动退款失败,权益订单号:" + vo.getOrderSn());
                return null;
            }
            return result.getData();
        } catch (Exception e) {
            LogUtil.printLog("调用订单中心取消订单异常", e);
        }
        return null;
    }

}
