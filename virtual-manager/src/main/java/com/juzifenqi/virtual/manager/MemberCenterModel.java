package com.juzifenqi.virtual.manager;

import com.alibaba.fastjson.JSONObject;
import com.juzifenqi.member.MemberResult;
import com.juzifenqi.member.entity.member.MemberInfo;
import com.juzifenqi.member.v2.api.MemberQueryApi;
import com.juzifenqi.virtual.component.exception.VirtualException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 用户中心
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/1/2 11:39 AM
 */
@Slf4j
@Component
public class MemberCenterModel {

    @Autowired
    private MemberQueryApi memberQueryApi;

    /**
     * 根据手机号渠道查询用户信息
     */
    public MemberInfo getMemberByMobile(String mobile, String channelId) {
        log.info("查询用户中心数据，mobile:{}", mobile);
        //2022/02/11 会员支持多渠道 gs
        MemberResult<MemberInfo> result = memberQueryApi.getMemberByMobile(mobile,
                Integer.valueOf(channelId));
        log.info("用户中心返回data:{}", JSONObject.toJSONString(result.getResult()));
        if (result.isSuccess() && result.getResult() != null) {
            return result.getResult();
        }
        throw new VirtualException("未查询到该用户的信息，请确认后重新尝试！");
    }

    /**
     * 根据userId查询用户信息
     */
    public MemberInfo getMemberByUserId(Integer userId) {
        log.info("查询用户中心数据，userId:{}", userId);
        MemberResult<MemberInfo> result = memberQueryApi.getMemberById(userId);
        log.info("用户中心返回data:{}", JSONObject.toJSONString(result));
        if (result.isSuccess() && result.getResult() != null) {
            return result.getResult();
        }
        throw new VirtualException("未查询到该用户的信息，请确认后重新尝试！");
    }
}
