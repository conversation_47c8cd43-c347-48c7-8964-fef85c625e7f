package com.juzifenqi.alita.result;

import java.io.Serializable;
import java.util.List;

/**
 * 兼容之前alita的对象
 */
public class PageResult<T> implements Serializable {
    private static final long    serialVersionUID = 1369984177274990188L;
    private static final int     defaultPageSize  = 15;
    private static final String  OK_MESSAGE       = "OK";
    private              int     totalPage        = 0;
    private              int     totalRows        = 0;
    private              int     pageNum          = 1;
    private              int     pageSize = 15;
    private              List<T> list     = null;
    private              int     code     = 200;
    private              String  message;

    public PageResult() {
        this.code = 200;
        this.message = "OK";
    }

    public int getTotalPage() {
        return (int)Math.ceil((double)(this.totalRows / this.pageSize)) + (this.totalRows % this.pageSize > 0 ? 1 : 0);
    }

    public void setTotalPage(int totalPage) {
        this.totalPage = totalPage;
    }

    public int getTotalRows() {
        return this.totalRows;
    }

    public void setTotalRows(int totalRows) {
        this.totalRows = totalRows;
    }

    public int getPageNum() {
        return this.pageNum;
    }

    public void setPageNum(int pageNum) {
        this.pageNum = pageNum;
    }

    public int getPageSize() {
        return this.pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
        if (pageSize <= 0) {
            this.pageSize = 15;
        }

    }

    public List getList() {
        return this.list;
    }

    public void setList(List list) {
        this.list = list;
    }

    public int getCode() {
        return this.code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMessage() {
        return this.message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}