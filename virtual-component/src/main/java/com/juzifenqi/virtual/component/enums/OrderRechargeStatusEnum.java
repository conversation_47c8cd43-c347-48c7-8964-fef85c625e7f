package com.juzifenqi.virtual.component.enums;

/**
 * 订单充值状态枚举，与三方交互
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/20 9:36 下午
 */
public enum OrderRechargeStatusEnum {

    RECHARGE_NO_VERIFY(0, "待查证充值状态"), RECHARGE_SUCCESS(1, "充值成功"), RECHARGE_FAIL(2,
            "充值失败"), RECHARGE_UNKNOWN(3, "充值结果存疑");

    private int    code;
    private String desc;

    OrderRechargeStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
