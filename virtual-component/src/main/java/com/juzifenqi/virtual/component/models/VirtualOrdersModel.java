package com.juzifenqi.virtual.component.models;

import com.alibaba.fastjson.JSON;
import com.groot.utils.exception.LogUtil;
import com.juzifenqi.virtual.bean.bo.VirtualOrdersBo;
import com.juzifenqi.virtual.bean.pojo.VirtualOrderSupplierCoupon;
import com.juzifenqi.virtual.bean.pojo.VirtualOrders;
import com.juzifenqi.virtual.bean.pojo.VirtualSupplierHotelOrder;
import com.juzifenqi.virtual.component.enums.OrderStatusEnum;
import com.juzifenqi.virtual.component.enums.PayStateEnum;
import com.juzifenqi.virtual.component.enums.RechargeStatusEnum;
import com.juzifenqi.virtual.component.enums.RechargeTypeEnum;
import com.juzifenqi.virtual.component.enums.SupplierEnum;
import com.juzifenqi.virtual.dao.VirtualOrderSupplierCouponDao;
import com.juzifenqi.virtual.dao.VirtualOrdersDao;
import com.juzifenqi.virtual.dao.VirtualSupplierHotelOrderDao;
import java.util.Arrays;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import org.springframework.util.CollectionUtils;

/**
 * 基础model
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/21 8:35 AM
 */
@Slf4j
@Component
public class VirtualOrdersModel {

    @Resource
    private VirtualOrdersDao              virtualOrdersDao;
    @Autowired
    private VirtualSupplierHotelOrderDao  hotelOrderDao;
    @Autowired
    private VirtualOrderSupplierCouponDao couponDao;

    /**
     * 根据会员订单号统计有效权益结算订单
     */
    public Integer countUesdOrdersByOrderSn(String orderSn, Integer modelId) {
        return virtualOrdersDao.countUesdOrdersByOrderSn(orderSn, modelId);
    }

    /**
     * 根据订单号查询结算订单
     */
    public VirtualOrders getVirtualOrdersByOrderSn(String orderSn) {
        return virtualOrdersDao.getVirtualOrdersByOrderSn(orderSn);
    }

    /**
     * 获取期限内，当前sku通过该会员单号下的订单
     *
     * @param plusOrderSn 开通会员单号
     */
    public List<VirtualOrders> countOrderByPlus(String plusOrderSn, Integer modelId) {
        List<VirtualOrders> virtualOrders = virtualOrdersDao.countOrderByPlus(plusOrderSn, modelId);
        setHotelOrderBtn(virtualOrders);
        return virtualOrders;
    }


    /**
     * 设置酒店订单按钮状态
     */
    private void setHotelOrderBtn(List<VirtualOrders> virtualOrders) {
        // 设置酒店订单状态
        List<String> hotelOrderSn = virtualOrders.stream()
                .filter(e -> e.getRechargeType() == RechargeTypeEnum.HOTEL.getCode())
                .map(VirtualOrders::getOrderSn).collect(Collectors.toList());
        log.info("设置酒店订单按钮状态，有效酒店单号：{}", JSON.toJSONString(hotelOrderSn));
        if (CollectionUtils.isEmpty(hotelOrderSn)) {
            log.info("设置酒店订单按钮状态无有效酒店订单号");
            return;
        }
        List<VirtualOrderSupplierCoupon> coupons = couponDao.selectByOrderSns(hotelOrderSn);
        log.info("设置酒店订单按钮状态，已领取的酒店券信息：{}", JSON.toJSONString(coupons));
        if (CollectionUtils.isEmpty(coupons)) {
            log.info("设置酒店订单按钮状态无领券信息");
            return;
        }
        List<VirtualSupplierHotelOrder> hotelOrders = hotelOrderDao.selectHotelOrderByCouponIds(
                coupons.stream().map(VirtualOrderSupplierCoupon::getCouponId)
                        .collect(Collectors.toList()));
        for (VirtualOrders virtualOrder : virtualOrders) {
            // 非酒店订单 或 非充值成功/存疑=无任何按钮（有默认值，无需设置）
            if (virtualOrder.getRechargeType() != RechargeTypeEnum.HOTEL.getCode() || (
                    virtualOrder.getOrderStatus()
                            != OrderStatusEnum.STATUS_RECHARGE_SUCCESS.getCode()
                            && virtualOrder.getOrderStatus()
                            != OrderStatusEnum.STATUS_RECHARGE_UNKNOWN.getCode())) {
                continue;
            }
            // 这里不能匹配hotel_order的虚拟权益单号，这个可能是错的。需要匹配coupon里的虚拟权益单号
            // 先获取当前虚拟权益订单对应的酒店券id
            VirtualOrderSupplierCoupon coupon = coupons.stream()
                    .filter(e -> StringUtils.equals(e.getOrderSn(), virtualOrder.getOrderSn()))
                    .findFirst().orElse(null);
            // 没有酒店券默认没有按钮
            if (coupon == null) {
                continue;
            }
            // 有有效酒店订单=查看酒店订单详情， 无有效酒店订单=去预定酒店
            boolean hasHotelOrder = hotelOrders.stream().anyMatch(
                    e -> StringUtils.isNotBlank(e.getCouponId()) && StringUtils.equals(
                            coupon.getCouponId(), e.getCouponId()));
            virtualOrder.setHotelOrderBtn(hasHotelOrder ? 2 : 1);
        }
    }

    /**
     * 是否有充值中订单（待充值下单，下单成功，待充值查证，充值成功，存疑订单）
     *
     * @param plusOrderSn 开通会员单号
     */
    public Integer countProcessingOrder(String plusOrderSn, Integer modelId) {
        return virtualOrdersDao.countProcessingOrder(plusOrderSn, modelId);
    }


    /**
     * 充值中订单列表（待充值下单，下单成功，待充值查证，充值成功，存疑订单）
     *
     * @param plusOrderSn 开通会员单号
     */
    public List<VirtualOrders> selectProcessingOrder(String plusOrderSn, Integer modelId) {
        return virtualOrdersDao.selectProcessingOrder(plusOrderSn, modelId);
    }

    /**
     * 获取商城下单未支付订单
     *
     * @param plusOrderSn 开通会员单号
     * @param productSku 商品sku
     */
    public List<VirtualOrders> getNeedPayOrder(String plusOrderSn, String productSku,
            Integer modelId) {
        return virtualOrdersDao.getNeedPayOrder(plusOrderSn, productSku, modelId);
    }


    /**
     * 获取用户虚拟订单列表
     */
    public List<VirtualOrders> getOrdersListByUser(VirtualOrdersBo bo) {
        List<VirtualOrders> ordersList = virtualOrdersDao.getOrdersListByUser(bo);
        setHotelOrderBtn(ordersList);
        return ordersList;
    }

    /**
     * 通过会员单号查询充值成功的虚拟权益订单列表
     *
     * @Param plusOrderSn 开通会员单号
     * @Param supplierIds 合作方ids
     * @Param rechargeTypes 充值类型
     */
    public List<VirtualOrders> getRechargeSuccessOrders(String plusOrderSn,
            List<Integer> supplierIds, List<Integer> rechargeTypes) {
        return virtualOrdersDao.getRechargeSuccessOrders(plusOrderSn,
                RechargeStatusEnum.RECHARGE_SUCCESS.getCode(), PayStateEnum.PAY_SUCCESS.getCode(),
                supplierIds, rechargeTypes);
    }
}
