package com.juzifenqi.virtual.component.enums;

import lombok.Getter;

/**
 * 会员订单退款状态
 *
 * <AUTHOR>
 * @date 2024/9/2 11:25
 */
@Getter
public enum RefundInfoStateEnum {

    DOING(1, "退款中"),

    SUCCESS(2, "退款成功"),

    FAIL(3, "退款失败");

    private final Integer code;
    private final String  name;

    RefundInfoStateEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

}
