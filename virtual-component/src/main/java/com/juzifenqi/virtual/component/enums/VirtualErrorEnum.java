package com.juzifenqi.virtual.component.enums;

/**
 * <AUTHOR>
 * @date 2020/2/4 16:25
 **/
public enum VirtualErrorEnum {

    ERROR_100100("100100", "服务内部错误,请稍后重试");

    private final String code;
    private final String message;

    VirtualErrorEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
