package com.juzifenqi.virtual.component.enums;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 主表订单状态
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/20 9:36 下午
 */
public enum OrderStatusEnum {

    /**
     * 权益订单状态
     */
    STATUS_MALL_NO_PAY(0, "商城下单未支付"), STATUS_ORDER_NO_PUSH(1, "待充值下单"), STATUS_ORDER_SUCCESS(2,
            "下单成功"), STATUS_ORDER_FAIL(3, "下单失败"), STATUS_RECHARGE_NO_VERIFY(4,
            "待充值查证"), STATUS_RECHARGE_SUCCESS(5, "充值成功"), STATUS_RECHARGE_FAIL(6,
            "充值失败"), STATUS_RECHARGE_UNKNOWN(7, "存疑订单"), SYSTEM_CLOSE(8, "30分钟未支付系统闭单");

    private int    code;
    private String desc;

    OrderStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static List<Map<String,Object>> getKeyValue() {
        List<Map<String,Object>> list = new ArrayList<>();
        for (OrderStatusEnum e : OrderStatusEnum.values()) {
            Map<String,Object> map = new HashMap<>();
            map.put("orderStatus",e.getCode());
            map.put("description",e.getDesc());
            list.add(map);
        }
        return list;
    }
}
