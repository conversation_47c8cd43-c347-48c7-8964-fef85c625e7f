package com.juzifenqi.virtual.component.dingtalk.bean;

import com.juzifenqi.virtual.component.dingtalk.type.MessageType;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 消息卡片类型Message
 *
 * <AUTHOR>
 * @version 1.0.0
 */
public class FeedCardMessage extends BaseMessage {

    private static final int MAX_BUTTON_COUNT = 10;
    private static final int MIN_BUTTON_COUNT = 1;

    /**
     * 消息明细条目
     */
    private List<FeedCardMessageItem> feedCardItems = new ArrayList<>();

    @Override
    protected void init() {
        this.msgtype = MessageType.feedCard;
    }

    @Override
    public Map<String, Object> toMessageMap() {
        if (feedCardItems == null || feedCardItems.size() < MIN_BUTTON_COUNT) {
            throw new IllegalArgumentException(
                    "the number of feedCardItems is not allow lower than " + MIN_BUTTON_COUNT);
        }
        if (feedCardItems.size() > MAX_BUTTON_COUNT) {
            throw new IllegalArgumentException(
                    "the number of buttons is not advise bigger than " + MAX_BUTTON_COUNT);
        }
        HashMap<String, Object> resultMap = new HashMap<>(8);
        resultMap.put("msgtype", this.msgtype);
        HashMap<String, Object> feedCardMap = new HashMap<>(8);
        feedCardMap.put("links", this.feedCardItems);
        resultMap.put("feedCard", feedCardMap);
        return resultMap;
    }
}
