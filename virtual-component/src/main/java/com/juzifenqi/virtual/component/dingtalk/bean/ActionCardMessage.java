package com.juzifenqi.virtual.component.dingtalk.bean;

import com.juzifenqi.virtual.component.dingtalk.type.ButtonOrientationType;
import com.juzifenqi.virtual.component.dingtalk.type.HideAvatarType;
import com.juzifenqi.virtual.component.dingtalk.type.MessageType;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.util.StringUtils;

/**
 * 跳转卡片类型
 *
 * <AUTHOR>
 * @verson 1.0
 * @date 2020/8/10 11:11 上午
 */
public class ActionCardMessage extends BaseMessage {

    private static final int MAX_BUTTON_COUNT = 5;
    private static final int MIN_BUTTON_COUNT = 0;

    /**
     * 标题
     */
    private String title;

    /**
     * 正文，支持MarkDown语法
     */
    private String text;

    /**
     * 是否隐藏头像 0 - 不隐藏头像 1 - 隐藏头像
     */
    private HideAvatarType hideAvatar = HideAvatarType.UNHIDE;

    /**
     * 按钮排列方式 0 - 垂直排列 1 - 水平排列
     */
    private ButtonOrientationType btnOrientation = ButtonOrientationType.HORIZONTAL;

    /**
     * 是否为按钮跳转布局，默认为false。 但只在Button个数为1时，分辨布局方式起作用。 true - 使用独立跳转ActionCard发消息 false -
     * 使用整体跳转ActionCard发消息
     */
    private boolean isButtonView;

    /**
     * 操作按钮成员变量 这个成员变量没有Set方法，主要是为了防止按钮过多造成的较差体验
     */
    private List<ActionCardButton> buttons = new ArrayList<>();

    @Override
    protected void init() {
        this.msgtype = MessageType.actionCard;
    }

    @Override
    public Map<String, Object> toMessageMap() {
        if (StringUtils.isEmpty(this.text) || StringUtils.isEmpty(this.title)) {
            throw new IllegalArgumentException("please check the necessary parameters!");
        }
        if (buttons == null) {
            throw new IllegalArgumentException(
                    "the number of buttons is not allow lower than " + MIN_BUTTON_COUNT);
        }
        if (buttons.size() > MAX_BUTTON_COUNT) {
            throw new IllegalArgumentException(
                    "the number of buttons is not advise bigger than " + MAX_BUTTON_COUNT);
        }
        HashMap<String, Object> resultMap = new HashMap<>(8);
        resultMap.put("msgtype", this.msgtype);
        HashMap<String, Object> actionCardMap = new HashMap<>(8);
        actionCardMap.put("title", this.title);
        actionCardMap.put("text", this.text);
        actionCardMap.put("hideAvatar", this.hideAvatar.getValue());
        actionCardMap.put("btnOrientation", this.btnOrientation.getValue());
        if (buttons.size() == 1 && !isButtonView) {
            actionCardMap.put("singleTitle", buttons.get(0).getTitle());
            actionCardMap.put("singleURL", buttons.get(0).getActionURL());
        } else if (buttons.size() > 1) {
            actionCardMap.put("btns", buttons);
        }
        resultMap.put("actionCard", actionCardMap);
        return resultMap;
    }

    /**
     * 增加操作按钮
     */
    public void addButton(ActionCardButton button) {
        if (button == null) {
            throw new IllegalArgumentException("not allow add empty button");
        }
        if (buttons == null || buttons.size() >= MAX_BUTTON_COUNT) {
            throw new IllegalArgumentException(
                    "the number of buttons is not advise bigger than " + MAX_BUTTON_COUNT);
        }
        buttons.add(button);
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public HideAvatarType getHideAvatar() {
        return hideAvatar;
    }

    public void setHideAvatar(HideAvatarType hideAvatar) {
        this.hideAvatar = hideAvatar;
    }

    public ButtonOrientationType getBtnOrientation() {
        return btnOrientation;
    }

    public void setBtnOrientation(ButtonOrientationType btnOrientation) {
        this.btnOrientation = btnOrientation;
    }

    public List<ActionCardButton> getButtons() {
        return buttons;
    }

    public boolean isButtonView() {
        return isButtonView;
    }

    public void setButtonView(boolean buttonView) {
        isButtonView = buttonView;
    }
}
