package com.juzifenqi.virtual.component.util;

import com.juzifenqi.virtual.component.feishu.FeiShuModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Description:
 * @Author: sun<PERSON><PERSON>
 * @Date: 2022/1/21 10:59
 */

/**
 * 钉钉告警工具类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/22 12:04 下午
 */
@Component
public class RobotUtil {

    @Autowired
    private FeiShuModel feiShuModel;

    /**
     * 发送系统报警消息
     */
    public void pushMsg(String msg) {
        feiShuModel.sendSysTextMsg(msg);
    }

    /**
     * 发送业务报警消息
     */
    public void pushBusMsg(String msg) {
        feiShuModel.sendBusTextMsg(msg);
    }
}