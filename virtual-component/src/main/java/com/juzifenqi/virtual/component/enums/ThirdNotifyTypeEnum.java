package com.juzifenqi.virtual.component.enums;

/**
 * 三方回调通知业务类型
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/13 10:22
 */
public enum ThirdNotifyTypeEnum {


    /**
     * 类型
     */
    PRODUCT_STATE_CHANGE(1, "商品状态变更"), PRODUCT_PRICE_CHANGE(2, "商品价格变更"),
    ORDER_REFUND(3, "订单售后退款"), HOTEL_ORDER(4, "酒店订单通知"),
    ;

    private int    code;
    private String name;

    ThirdNotifyTypeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
