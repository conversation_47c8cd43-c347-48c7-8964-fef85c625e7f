package com.juzifenqi.virtual.component.enums;

import lombok.Getter;

/**
 * 支付返回支付方式枚举
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/8/31 09:15
 */
@Getter
public enum PayProductCodeEnum {
    ZXZF("ZXZF", "在线支付", "银行卡"), HK("HK", "划扣", "银行卡"), ZFBZF("ZFBZF", "支付宝支付",
            "支付宝"), DF("DF", "代付", ""), TK("TK", "退款", ""),
    ;

    private final String code;
    private final String name;
    private final String desc;

    PayProductCodeEnum(String code, String name, String desc) {
        this.code = code;
        this.name = name;
        this.desc = desc;
    }

    public static PayProductCodeEnum getTypeByCode(String code) {
        for (PayProductCodeEnum productCode : PayProductCodeEnum.values()) {
            if (productCode.getCode().equals(code)) {
                return productCode;
            }
        }
        return null;
    }
}
