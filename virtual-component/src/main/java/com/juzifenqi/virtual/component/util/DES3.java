package com.juzifenqi.virtual.component.util;

import java.nio.charset.StandardCharsets;
import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;

/**
 * 加解密
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/10 14:34
 */
@Slf4j
public class DES3 {

    // 定义 加密算法,可用
    private static final String ALGORITHM = "DESede";
    // DES,DESede,Blowfish
    private static final byte[] KEYBYTE   = "16927bb25fd3d0171f838ag9".getBytes();

    /**
     * 加密 keybyte为加密密钥，长度为24字节 src为被加密的数据缓冲区（源）
     */
    public static String encrypt(String data) {
        if (StringUtils.isEmpty(data)) {
            return null;
        }
        try {
            byte[] src = data.getBytes(StandardCharsets.UTF_8);
            SecretKey deskey = new SecretKeySpec(KEYBYTE, ALGORITHM);
            Cipher c1 = Cipher.getInstance(ALGORITHM);
            c1.init(Cipher.ENCRYPT_MODE, deskey);
            return new String(Base64.encodeBase64(c1.doFinal(src)));
        } catch (Exception e3) {
            log.error("data：" + data, e3);
        }
        return null;
    }

    /**
     * 解密 keybyte为加密密钥，长度为24字节 src为被加密的数据缓冲区（源）
     */
    public static String decrypt(String data) {
        if (StringUtils.isEmpty(data)) {
            return null;
        }
        try {
            byte[] src = data.getBytes();
            // 生成密钥
            SecretKey deskey = new SecretKeySpec(KEYBYTE, ALGORITHM);
            // 解密
            Cipher c1 = Cipher.getInstance(ALGORITHM);
            c1.init(Cipher.DECRYPT_MODE, deskey);
            return new String(c1.doFinal(Base64.decodeBase64(src)), StandardCharsets.UTF_8);
        } catch (Exception e3) {
            log.error("data：" + data, e3);
        }
        return null;
    }

    // keybyte为加密密钥，长度为24字节
    // src为被加密的数据缓冲区（源）
    public static String encryptBatch(String data) {
        if (StringUtils.isEmpty(data)) {
            return "";
        }
        try {
            byte[] src = data.getBytes(StandardCharsets.UTF_8);
            SecretKey deskey = new SecretKeySpec(KEYBYTE, ALGORITHM);
            Cipher c1 = Cipher.getInstance(ALGORITHM);
            c1.init(Cipher.ENCRYPT_MODE, deskey);
            return new String(Base64.encodeBase64(c1.doFinal(src)));
        } catch (Exception e3) {
            log.error("data：" + data, e3);
            return "";
        }
    }

    // keybyte为加密密钥，长度为24字节
    // src为加密后的缓冲区
    public static String decryptBatch(String data) {
        if (StringUtils.isEmpty(data)) {
            return "";
        }
        try {
            byte[] src = data.getBytes();
            // 生成密钥
            SecretKey deskey = new SecretKeySpec(KEYBYTE, ALGORITHM);
            // 解密
            Cipher c1 = Cipher.getInstance(ALGORITHM);
            c1.init(Cipher.DECRYPT_MODE, deskey);
            return new String(c1.doFinal(Base64.decodeBase64(src)), "UTF-8");
        } catch (Exception e3) {
            log.error("dataDecrypt：" + data, e3);
            return "";
        }
    }


    /**
     * 将byte转为16进制
     */
    private static String byte2Hex(byte[] bytes) {
        StringBuffer stringBuffer = new StringBuffer();
        String temp = null;
        for (int i = 0; i < bytes.length; i++) {
            temp = Integer.toHexString(bytes[i] & 0xFF);
            if (temp.length() == 1) {
                //1得到一位的进行补0操作
                stringBuffer.append("0");
            }
            stringBuffer.append(temp);
        }
        return stringBuffer.toString();
    }

}
