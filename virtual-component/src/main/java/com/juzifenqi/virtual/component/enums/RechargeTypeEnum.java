package com.juzifenqi.virtual.component.enums;

/**
 * 充值方式枚举
 *
 * <AUTHOR>
 * @date 2023-11-09 17:33:23
 */
public enum RechargeTypeEnum {

    /**
     * 1.直充 2.卡密
     */
    DIRECT(1, "直充"), CARD(2, "卡密"), HOTEL(3, "酒店券直充")
    ;

    private int    code;
    private String name;

    RechargeTypeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static RechargeTypeEnum getType(int code) {
        for (RechargeTypeEnum item : RechargeTypeEnum.values()) {
            if (item.code == code) {
                return item;
            }
        }
        return null;
    }
}
