package com.juzifenqi.virtual.component.util;

import com.juzifenqi.virtual.component.enums.DateEnum;
import org.joda.time.DateTime;
import org.joda.time.Days;
import org.joda.time.LocalDate;
import org.joda.time.Months;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @Description: 日期计算工具类
 * @Author: sunchenhui
 * @Date: 2022/1/20 17:38
 */
public final class DateUtils {

    /**
     * 获取当前时间
     *
     * @return 返回java Date
     * <AUTHOR>
     */
    public static Date getNow() {
        return new DateTime().toDate();
    }

    public static Date truncate(Date date, int field) {
        return DateUtils.truncate(date, field);
    }

    /**
     * 获取当前时间
     *
     * @return 返回JODA DateTime
     * <AUTHOR>
     */
    public static DateTime getDateTimeNow() {
        return new DateTime();
    }

    public static DateTime dateToDateTime(Date date) {
        return new DateTime(date);
    }

    /**
     * 字符串时间 转换为时间
     *
     * @param date
     * @return
     * <AUTHOR>
     */
    public static Date toDate(String date, DateEnum format) {
        DateTimeFormatter fmt = DateTimeFormat.forPattern(format.getText());
        return fmt.parseDateTime(date.trim()).toDate();
    }

    /**
     * 毫秒转换为Date
     *
     * @param ms
     * @return
     * <AUTHOR>
     */
    public static Date msToDate(long ms) {
        return new DateTime(ms).toDate();
    }


    /**
     * 默认格式化时间
     *
     * @param date
     * @return
     */
    public static String dateToDefaultStr(Date date) {
        SimpleDateFormat format = new SimpleDateFormat(DateEnum.DATE_FORMAT.getText());
        return format.format(date);
    }

    /**
     * 格式化时间
     *
     * @param date     时间
     * @param template 格式
     * @return
     * <AUTHOR>
     */
    public static String dateToStr(Date date, DateEnum template) {
        SimpleDateFormat format = new SimpleDateFormat(template.getText());
        return format.format(date);
    }

    /**
     * 传入日期增加几天
     *
     * @param date
     * @param day
     * @return
     * <AUTHOR>
     */
    public static Date plusDay(Date date, int day) {
        return new DateTime(date).plusDays(day).toDate();
    }

    /**
     * 获取传入天数的0毫秒时间
     *
     * @param date
     * @param day
     * @return
     */
    public static Date plusDayZoreMills(Date date, int day) {
        return new DateTime(date).plusDays(day).withMillisOfDay(0).toDate();
    }

    /**
     * 传入日期增加几个月
     *
     * @param date
     * @param
     * @return
     * <AUTHOR>
     */
    public static Date plusMonth(Date date, int month) {
        return new DateTime(date).plusMonths(month).toDate();
    }

    /**
     * 传入日期减少几天
     *
     * @param date
     * @param day
     * @return
     * <AUTHOR>
     */
    public static Date minusDay(Date date, int day) {
        return new DateTime(date).minusDays(day).toDate();
    }


    /**
     * 传入日期减少月数
     *
     * @param date
     * @param month
     * @return
     */
    public static Date minusMonth(Date date, int month) {
        return new DateTime(date).minusMonths(month).toDate();
    }

    /**
     * 传入日期减少几分钟
     *
     * @param date
     * @param
     * @return
     */
    public static Date minusMinutes(Date date, int minutes) {
        return new DateTime(date).minusMinutes(minutes).toDate();
    }

    public static Date longToDate(long millis) {
        return new DateTime(millis).toDate();
    }

    /**
     * 返回传入两个date相差的天数
     * END 大于 start 忽略时分秒
     * 例：start = 2015-06-05 10:40:30
     * end = 2015-06-06 10:40:20
     * 返回值 1
     *
     * @param start
     * @param end
     * @return
     */
    public static int daysBetween(Date start, Date end) {
        int days = Days.daysBetween(new LocalDate(start), new LocalDate(end)).getDays();
        return days;
    }

    /**
     * 返回两个日期之间 月数
     *
     * @param start
     * @param end
     * @return
     * <AUTHOR>
     */
    public static int monthsBetween(Date start, Date end) {
        return monthsBetween(new DateTime(start), new DateTime(end));
    }

    /**
     * 返回两个日期之间 月数
     *
     * @param start
     * @param end
     * @return
     * <AUTHOR>
     */
    public static int monthsBetween(DateTime start, DateTime end) {
        int m = Months.monthsBetween(start, end).getMonths();
        return m;
    }

    /**
     * 返回当前日期距离N月之后之后的天数
     * <p>
     * 例如：计算1月5日，距离2个月之后的3月5日 返回相差天数
     *
     * @param date
     * @param addmonths
     * @return
     */
    public static int daysAfterMonths(Date date, int addmonths) {
        DateTime dateTime = new DateTime(date);
        DateTime dateTimeAfter = dateTime.plusMonths(addmonths);
        return daysBetween(date, dateTimeAfter.toDate());
    }

    /**
     * 返回传入日期+N月之后的日期
     *
     * @param date
     * @param addmonths
     * @return
     */
    public static Date dateAfterMonths(Date date, int addmonths) {
        DateTime dateTime = new DateTime(date);
        DateTime dateTimeAfter = dateTime.plusMonths(addmonths);
        return dateTimeAfter.toDate();
    }

    public static Date handleDateMix(Date date) {
        String str = DateUtils.dateToStr(date, DateEnum.DATE_SIMPLE) + " 00:00:00";
        return DateUtils.toDate(str, DateEnum.DATE_FORMAT);
    }

    public static Date handleDateMax(Date date) {
        String str = DateUtils.dateToStr(date, DateEnum.DATE_SIMPLE) + " 23:59:59";
        return DateUtils.toDate(str, DateEnum.DATE_FORMAT);
    }

    /**
     * 传入日期和当前日期比较（参数会转换成年月日格式）
     *
     * @param dt
     * @return 小于-1，等于0，大于1
     */
    public static int compareDateToNow(DateTime dt) {
        return dt.toLocalDate().compareTo(new DateTime().toLocalDate());
    }

    /**
     * 返回当前时间格式yyyyMMdd字符串
     *
     * @return
     */
    public static String getSimpleDate() {
        return dateToStr(getNow(), DateEnum.DATE_SIMPLE_MIN);
    }

    /**
     * 返回当前时间格式yyyyMMdd字符串
     *
     * @return
     */
    public static String getFullDate() {
        return dateToStr(getNow(), DateEnum.DATE_FORMAT);
    }

    /**
     * 返回当前时间格式HHmmss字符串
     *
     * @return
     */
    public static String getMinTime() {
        return dateToStr(getNow(), DateEnum.DATE_TIME_MIN);
    }

    /**
     * 返回当前时间，格式 yyyyMMddHHmmss字符串
     */
    public static String getBankSeqDate() {
        return dateToStr(getNow(), DateEnum.DATE_BANK_SEQ);
    }

    /**
     * @param date
     * @return 当前月第几天
     */
    public static int getDayOfMonth(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        return c.get(Calendar.DAY_OF_MONTH);
    }


    public static Date getDate(String str, DateEnum format) {
        SimpleDateFormat sdf = new SimpleDateFormat(format.getText());
        Date date = null;
        try {
            date = sdf.parse(str);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return date;
    }

    public static String getNowStr() {
        return dateToStr(new Date(), DateEnum.DATE_FORMAT);
    }


    public static boolean checkValidDate(String str, DateEnum dateEnum) {
        SimpleDateFormat format = new SimpleDateFormat(dateEnum.getText());
        format.setLenient(false);
        try {
            format.parse(str);
            return true;
        } catch (ParseException e) {
            //如果throw java.text.ParseException或者NullPointerException，就说明格式不对
            return false;
        }
    }

    /**
     * 判断目标日期是否包含在指定的时间段内
     *
     * @param low    include
     * @param high   exclude
     * @param target
     * @return
     */
    public static boolean containTimeInterval(Date low, Date high, Date target) {
        long targetTime = target.getTime();
        return targetTime >= low.getTime() && targetTime < high.getTime();
    }

    /**
     * 返回当前时间加上指定的年数，格式yyyy-MM-dd
     *
     * @param year : 正数 表示加多少年，负数表示减多少年
     * @return java.lang.String
     * @author: liangruihao
     * @date: 2021/4/8 15:43
     */
    public static String addYears(int year) {
        Calendar calendar = new GregorianCalendar();
        calendar.setTime(getNow());
        calendar.add(Calendar.YEAR, year); //把日期往后增加一年，整数往后推，负数往前移
        Date date = calendar.getTime();
        return dateToStr(date, DateEnum.DATE_SIMPLE);
    }

    /**
     * 指定日期加上指定年数
     *
     * @param date String yyyy-MM-dd
     * @param year int
     * @return 返回日期格式yyyy-MM-dd
     */
    public static String addYears(String date, int year) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        Date sourceDate = null;
        try {
            sourceDate = format.parse(date);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(sourceDate);
        calendar.add(Calendar.YEAR, year);
        Date targetDate = calendar.getTime();
        return dateToStr(targetDate, DateEnum.DATE_SIMPLE);
    }

    /**
     * 返回当前时间加上指定的月数，格式yyyy-MM-dd
     *
     * @param month :   正数 表示加多少月，负数表示减多少月
     * @return java.lang.String
     * @author: liangruihao
     * @date: 2021/4/9 14:41
     */
    public static String addMonths(int month) {
        Calendar calendar = new GregorianCalendar();
        calendar.setTime(getNow());
        calendar.add(Calendar.MONTH, month); //把日期往后增加一个月，整数往后推，负数往前移
        Date date = calendar.getTime();
        return dateToStr(date, DateEnum.DATE_SIMPLE);
    }

    public static String addMonths(Date date, int month) {
        Calendar calendar = new GregorianCalendar();
        calendar.setTime(date);
        calendar.add(Calendar.MONTH, month); //把日期往后增加一个月，整数往后推，负数往前移
        Date d = calendar.getTime();
        return dateToStr(d, DateEnum.DATE_SIMPLE);
    }

    /**
     * 获取两个日期之间的所有日期
     *
     * @param start
     * @param end
     * @return
     */
    private static List<Date> getDays(Date start, Date end) {
        List<Date> result = new ArrayList<Date>();
        Calendar tempStart = Calendar.getInstance();
        tempStart.setTime(start);
        tempStart.add(Calendar.DAY_OF_YEAR, 1);

        Calendar tempEnd = Calendar.getInstance();
        tempEnd.setTime(end);
        while (tempStart.before(tempEnd)) {
            result.add(tempStart.getTime());
            tempStart.add(Calendar.DAY_OF_YEAR, 1);
        }
        return result;
    }

    /**
     * 获取两个日期之间的所有日期
     *
     * @param startTime
     * @param endTime
     * @return
     */
    public static List<String> getDayList(String startTime, String endTime) {
        return getDays(startTime, endTime, "yyyyMMdd");
    }

    /**
     * 获取两个日期之间的所有日期
     *
     * @param startTime
     * @param endTime
     * @return
     */
    public static List<String> getDays(String startTime, String endTime, String fmt) {

        // 返回的日期集合
        List<String> days = new ArrayList<String>();

        DateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        DateFormat dateFormat1 = new SimpleDateFormat(fmt);
        try {
            Date start = dateFormat.parse(startTime);
            Date end = dateFormat.parse(endTime);

            Calendar tempStart = Calendar.getInstance();
            tempStart.setTime(start);

            Calendar tempEnd = Calendar.getInstance();
            tempEnd.setTime(end);
            // 日期加1(包含结束)
            tempEnd.add(Calendar.DATE, +1);
            while (tempStart.before(tempEnd)) {
                days.add(dateFormat1.format(tempStart.getTime()));
                tempStart.add(Calendar.DAY_OF_YEAR, 1);
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }

        return days;
    }
}
