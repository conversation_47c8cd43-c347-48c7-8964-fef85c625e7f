package com.juzifenqi.virtual.component.feishu;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.groot.utils.http.OKHttp3Utils;
import com.groot.utils.http.OkHttpClientEnum;
import com.juzifenqi.virtual.component.feishu.FeishuTextReq.Content;
import com.juzifenqi.virtual.component.util.IpUtil;
import java.nio.charset.StandardCharsets;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.net.util.Base64;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 飞书发送消息model
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/5 14:28
 */
@Component
@Slf4j
public class FeiShuModel {

    /**
     * 系统报警key
     */
    @Value("${feishu.secret}")
    private String secret;
    /**
     * 业务报警key
     */
    @Value("${feishu.busSecret}")
    private String busSecret;
    /**
     * 系统报警url
     */
    @Value("${feishu.url}")
    private String url;
    /**
     * 业务报警url
     */
    @Value("${feishu.busUrl}")
    private String busUrl;


    /**
     * 发送系统报警文本消息 <a
     * href="https://open.feishu.cn/document/client-docs/bot-v3/add-custom-bot">...</a>
     */
    public void sendSysTextMsg(String message) {
        try {
            FeishuTextReq req = new FeishuTextReq();
            long timeMillis = System.currentTimeMillis() / 1000;
            // 时间戳
            req.setTimestamp(timeMillis);
            // 签名
            req.setSign(GenSign(secret, timeMillis));
            // 消息内容
            String contentMsg =
                    "<at user_id=\"all\">所有人</at>virtual-center错误信息: " + message + "\n" + "服务器IP: "
                            + IpUtil.getServerIp() + "---" + IpUtil.getLinuxHostName() + "\n";
            Content content = new Content();
            content.setText(contentMsg);
            req.setContent(content);
            String requestBoyd = JSON.toJSONString(req);
            log.info("发送飞书消息入参：{}", requestBoyd);
            JSONObject jsonObject = OKHttp3Utils.postByJson(url, null, requestBoyd,
                    OkHttpClientEnum.ONE_SECOND, 0);
            log.info("发送飞书消息返回：{}", jsonObject == null ? null : jsonObject.toJSONString());
        } catch (Exception e) {
            log.info("发送飞书消息异常", e);
        }
    }


    /**
     * 发送业务群报警文本消息 <a
     * href="https://open.feishu.cn/document/client-docs/bot-v3/add-custom-bot">...</a>
     */
    public void sendBusTextMsg(String message) {
        try {
            FeishuTextReq req = new FeishuTextReq();
            long timeMillis = System.currentTimeMillis() / 1000;
            // 时间戳
            req.setTimestamp(timeMillis);
            // 签名
            req.setSign(GenSign(busSecret, timeMillis));
            // 消息内容
            String contentMsg = "<at user_id=\"all\">所有人</at>: " + message;
            Content content = new Content();
            content.setText(contentMsg);
            req.setContent(content);
            String requestBoyd = JSON.toJSONString(req);
            log.info("发送飞书消息入参：{}", requestBoyd);
            JSONObject jsonObject = OKHttp3Utils.postByJson(busUrl, null, requestBoyd,
                    OkHttpClientEnum.ONE_SECOND, 0);
            log.info("发送飞书消息返回：{}", jsonObject == null ? null : jsonObject.toJSONString());
        } catch (Exception e) {
            log.info("发送飞书消息异常", e);
        }
    }


    /**
     * 生成签名
     *
     * @param secret 秘钥
     * @param timestamp 时间戳：距当前时间不超过 1 小时（3600 秒）
     */
    private static String GenSign(String secret, long timestamp) throws Exception {
        // 把timestamp+"\n"+密钥当做签名字符串
        String stringToSign = timestamp + "\n" + secret;
        // 使用HmacSHA256算法计算签名
        Mac mac = Mac.getInstance("HmacSHA256");
        mac.init(new SecretKeySpec(stringToSign.getBytes(StandardCharsets.UTF_8), "HmacSHA256"));
        byte[] signData = mac.doFinal(new byte[]{});
        return new String(Base64.encodeBase64(signData));
    }

}
