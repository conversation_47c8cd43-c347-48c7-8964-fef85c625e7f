package com.juzifenqi.virtual.component.dingtalk.bean;

import com.juzifenqi.virtual.component.dingtalk.type.MessageType;
import java.util.HashMap;
import java.util.Map;
import org.springframework.util.StringUtils;

/**
 * 链接消息类型
 *
 * <AUTHOR>
 * @version 1.0.0
 */
public class LinkMessage extends BaseMessage {

    /**
     * 消息简介
     */
    private String text;

    /**
     * 消息标题
     */
    private String title;

    /**
     * 封面图片URL
     */
    private String picUrl;

    /**
     * 消息跳转URL
     */
    private String messageUrl;

    @Override
    protected void init() {
        this.msgtype = MessageType.link;
    }

    @Override
    public Map<String, Object> toMessageMap() {
        if (StringUtils.isEmpty(this.messageUrl) || StringUtils.isEmpty(this.title) || StringUtils
                .isEmpty(this.text) || !MessageType.link.equals(msgtype)) {
            throw new IllegalArgumentException("please check the necessary parameters!");
        }
        HashMap<String, Object> resultMap = new HashMap<>(8);
        resultMap.put("msgtype", this.msgtype);
        HashMap<String, String> linkItems = new HashMap<>(8);
        linkItems.put("title", this.title);
        linkItems.put("text", this.text);
        linkItems.put("picUrl", this.picUrl);
        linkItems.put("messageUrl", this.messageUrl);
        resultMap.put("link", linkItems);
        return resultMap;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getPicUrl() {
        return picUrl;
    }

    public void setPicUrl(String picUrl) {
        this.picUrl = picUrl;
    }

    public String getMessageUrl() {
        return messageUrl;
    }

    public void setMessageUrl(String messageUrl) {
        this.messageUrl = messageUrl;
    }
}