package com.juzifenqi.virtual.component.dingtalk;

import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @verson 1.0
 * @date 2020/8/11 1:25 下午
 */
@Configuration
public class DingTalkConfiguration {

    @Bean
    @ConditionalOnMissingBean
    public DingTalkClient dingTalkRobotClient() {
        return new DingTalkClient();
    }
}