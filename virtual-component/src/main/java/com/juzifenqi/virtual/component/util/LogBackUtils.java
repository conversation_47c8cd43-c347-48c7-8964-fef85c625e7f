package com.juzifenqi.virtual.component.util;

import ch.qos.logback.core.PropertyDefinerBase;
import java.net.InetAddress;
import java.net.UnknownHostException;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2020/09/15
 */
@Slf4j
public class LogBackUtils extends PropertyDefinerBase {

    @Override
    public String getPropertyValue() {
        String ip = null;
        try {
            ip = InetAddress.getLocalHost().getHostName();
        } catch (UnknownHostException e) {
            log.error("获取ip异常", e);
        }
        return ip;
    }
}
