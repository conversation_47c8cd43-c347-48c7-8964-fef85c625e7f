package com.juzifenqi.virtual.component.dingtalk.type;

/**
 * <AUTHOR>
 * @verson 1.0
 * @date 2020/8/10 11:13 上午
 */
public enum HideAvatarType {

    /**
     * 发消息的时候，隐藏机器人头像
     */
    HIDE("隐藏", "1"),

    /**
     * 发消息的时候，显示机器人头像
     */
    UNHIDE("不隐藏，正常显示", "0");
    private String comment;

    private String value;

    HideAvatarType(String comment, String value) {
        this.comment = comment;
        this.value = value;
    }

    public String getComment() {
        return comment;
    }

    public String getValue() {
        return value;
    }
}
