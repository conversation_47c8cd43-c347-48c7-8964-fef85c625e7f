package com.juzifenqi.virtual.component.util;

import ch.qos.logback.classic.pattern.MessageConverter;
import ch.qos.logback.classic.spi.ILoggingEvent;
import com.groot.utils.crypto.MD5Utils;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2021/07/20
 */
public class LogBackSensitiveConverterUtil extends MessageConverter {

    private static final String PHONE_REGEX = "(\\+861[3|4|5|7|8][0-9]\\d{8}[^\\d])|(^1[3|4|5|7|8][0-9]\\d{8})[^\\d]|([^\\d]1[3|4|5|7|8][0-9]\\d{8}[^\\d])";

    private static final String IDCARD_REGEX = "[^\\d]([1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx])[^\\d]|[^\\d](^[1-9]\\d{5}\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{3})[^\\d]";

    @Override
    public String convert(ILoggingEvent event) {
        // 获取原始日志
        String requestLogMsg = event.getFormattedMessage();
        try {
            if (StringUtils.isBlank(requestLogMsg)) {
                return requestLogMsg;
            }
            requestLogMsg = filterIdcard(requestLogMsg);
            requestLogMsg = filterMobile(requestLogMsg);
            return requestLogMsg;
        } catch (Exception e) {
            return requestLogMsg;
        }
    }

    /**
     * 身份证号脱敏
     */
    private static String filterIdcard(String num) {
        Pattern pattern = Pattern.compile(IDCARD_REGEX);
        Matcher matcher = pattern.matcher(num);
        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            matcher.appendReplacement(sb, baseSensitive(matcher.group()));
        }
        matcher.appendTail(sb);
        return sb.toString();
    }

    /**
     * 手机号码脱敏
     */
    private static String filterMobile(String num) {
        Pattern pattern = Pattern.compile(PHONE_REGEX);
        Matcher matcher = pattern.matcher(num);
        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            String s = baseSensitive(matcher.group());
            matcher.appendReplacement(sb, s);
        }
        matcher.appendTail(sb);
        return sb.toString();
    }

    /**
     * 基础脱敏处理
     *
     * @param str 待脱敏的字符串
     * @return 脱敏后的字符串
     */
    private static String baseSensitive(String str) {
        return MD5Utils.getMd5String(str.replace("\"", ""));
    }
}
