package com.juzifenqi.virtual.component.enums;

import lombok.Getter;

/**
 * 合作方优惠券状态
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/1 9:42
 */
@Getter
public enum SupplierCouponStatusEnum {
    NO_RECEIVE(0, "未领取"), RECEIVED(1, "已领取"), INVALID(2, "已失效");

    private int    code;
    private String desc;

    SupplierCouponStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDesc(int code) {
        for (SupplierCouponStatusEnum item : SupplierCouponStatusEnum.values()) {
            if (item.code == code) {
                return item.getDesc();
            }
        }
        return null;
    }
}
