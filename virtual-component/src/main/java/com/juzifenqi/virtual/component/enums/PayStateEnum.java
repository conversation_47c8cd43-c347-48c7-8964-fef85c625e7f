package com.juzifenqi.virtual.component.enums;

/**
 * 支付状态
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/9 16:38
 */
public enum PayStateEnum {

    WAIT_PAY(1, "待支付"), PAY_SUCCESS(2, "支付成功"), REFUND(3, "已退款");

    private int    code;
    private String desc;

    PayStateEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
