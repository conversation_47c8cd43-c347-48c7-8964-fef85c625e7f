package com.juzifenqi.virtual.component.exception;

import com.groot.utils.exception.GlobalException;
import com.juzifenqi.virtual.component.enums.VirtualErrorEnum;
import lombok.Data;

/**
 * 用户中心异常信息
 *
 * <AUTHOR>
 * @date 2020/2/4 15:50
 **/
@Data
public class VirtualException extends GlobalException {

    private String errorCode;

    public VirtualException(String message) {
        super(message);
    }

    public VirtualException(String errorCode, String message) {
        super(message);
        this.setErrorCode(errorCode);
    }

    public VirtualException(VirtualErrorEnum errorEnum) {
        super(errorEnum.getMessage());
        this.setErrorCode(errorEnum.getCode());
    }
}
