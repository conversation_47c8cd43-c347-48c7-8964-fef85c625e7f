package com.juzifenqi.virtual.component.models;

import com.juzifenqi.virtual.bean.pojo.VirtualOrdersTrail;
import com.juzifenqi.virtual.dao.VirtualOrdersTrailDao;
import java.util.Date;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/22 1:57 下午
 */
@Component
@Slf4j
public class VirtualOrdersTrailModel {

    @Autowired
    private VirtualOrdersTrailDao virtualOrdersTrailDao;

    /**
     * <AUTHOR>
     * @version 1.0
     * @date 2022/1/22 2:01 下午
     */
    public void saveOrdersTrail(String orderSn, Integer oldState, Integer nowState, String remark) {
        log.info("保存订单轨迹 {} remark {}", orderSn, remark);
        //记录订单流转状态
        VirtualOrdersTrail trail = new VirtualOrdersTrail();
        trail.setOrderSn(orderSn);
        trail.setOldState(oldState);
        trail.setNowState(nowState);
        trail.setCreateTime(new Date());
        trail.setRemark(remark);
        virtualOrdersTrailDao.saveVirtualOrdersTrail(trail);
    }
}
