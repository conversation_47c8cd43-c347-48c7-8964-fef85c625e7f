package com.juzifenqi.virtual.component.enums;

/**
 * 充值状态枚举，内部记录使用
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/9 16:42
 */
public enum RechargeStatusEnum {
    RECHARGE_ING(1, "充值中"), RECHARGE_SUCCESS(2, "充值成功"), RECHARGE_FAIL(3, "充值失败");

    private int    code;
    private String desc;

    RechargeStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
