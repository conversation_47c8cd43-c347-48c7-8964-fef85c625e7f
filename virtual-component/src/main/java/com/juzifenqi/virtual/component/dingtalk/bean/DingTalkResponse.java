package com.juzifenqi.virtual.component.dingtalk.bean;

/**
 * 钉钉返回的消息体，可以根据errcode和errmsg
 *
 * <AUTHOR>
 * @version 1.0.0
 */
public class DingTalkResponse {

    /**
     * 错误码
     */
    private Integer errcode;

    /**
     * 错误信息
     */
    private String errmsg;

    public Integer getErrcode() {
        return errcode;
    }

    public void setErrcode(Integer errcode) {
        this.errcode = errcode;
    }

    public String getErrmsg() {
        return errmsg;
    }

    public void setErrmsg(String errmsg) {
        this.errmsg = errmsg;
    }

    @Override
    public String toString() {
        return "DingTalkResponse{" + "errcode=" + errcode + ", errmsg='" + errmsg + '\'' + '}';
    }
}