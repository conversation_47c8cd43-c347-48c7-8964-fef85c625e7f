package com.juzifenqi.virtual.component.enums;

/**
 * 橡树酒店类型
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/1 11:07
 */
public enum OakHotelTypeEnum {

    /**
     * 酒店类型枚举
     */
    NORMAL(1, "ehotel", "经济型酒店"), SMALL_LUXURY(2, "hotel", "轻奢型酒店");

    private final int    code;
    private final String supplierCode;
    private final String desc;

    OakHotelTypeEnum(int code, String supplierCode, String desc) {
        this.code = code;
        this.supplierCode = supplierCode;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getSupplierCode() {
        return supplierCode;
    }

    public String getDesc() {
        return desc;
    }

    public static String getSupplierCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (OakHotelTypeEnum value : OakHotelTypeEnum.values()) {
            if (value.getCode() == code) {
                return value.getSupplierCode();
            }
        }
        return null;
    }
}
