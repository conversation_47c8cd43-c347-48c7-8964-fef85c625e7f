package com.juzifenqi.virtual.component.dingtalk.bean;

import com.juzifenqi.virtual.component.dingtalk.type.MessageType;
import java.io.Serializable;
import java.util.Map;

/**
 * 请求消息的抽象类
 *
 * <AUTHOR>
 * @version 1.0.0
 */
public abstract class BaseMessage implements Serializable {

    public BaseMessage() {
        init();
    }

    /**
     * 消息类型
     */
    protected MessageType msgtype;

    public MessageType getMsgtype() {
        return msgtype;
    }

    /**
     * 初始化MmessageType方法
     */
    protected abstract void init();

    /**
     * 返回Message对象组装出来的Map对象，供后续JSON序列化
     *
     * @return Map
     */
    public abstract Map<String, Object> toMessageMap();

}
