package com.juzifenqi.virtual.component.enums;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import lombok.Getter;

/**
 * 酒店订单状态枚举
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/02 10:22
 */
@Getter
public enum HotelOrderStatusEnum {

    unpaid(1, "unpaid", "待支付", ""), system_canceled(2, "system_canceled", "系统取消", "HY21"), confirming(3,
            "confirming", "已支付，待确认", "HY19"), success(4, "success", "已确认，待入住", "HY20"), check_in(5, "check_in",
            "已入住", ""), user_canceled(6, "user_canceled", "用户取消", "HY21"),
    ;

    private int    code;
    private String supplierCode;
    private String name;
    private String templateCode;

    HotelOrderStatusEnum(int code, String supplierCode, String name, String templateCode) {
        this.code = code;
        this.supplierCode = supplierCode;
        this.name = name;
        this.templateCode = templateCode;
    }

    public static HotelOrderStatusEnum getType(String supplierCode) {
        for (HotelOrderStatusEnum item : HotelOrderStatusEnum.values()) {
            if (item.supplierCode.equals(supplierCode)) {
                return item;
            }
        }
        return null;
    }

    public static Boolean needSendPhoneMsg(int code) {
        return !Objects.isNull(Arrays.asList(system_canceled.getCode(), confirming.getCode(), success.getCode(), user_canceled.getCode()).stream().filter(x -> x == code).findFirst().orElse(null));
    }

    public static List<Integer> notValidStatuses() {
        return Arrays.asList(system_canceled.getCode(), user_canceled.getCode());
    }
}
