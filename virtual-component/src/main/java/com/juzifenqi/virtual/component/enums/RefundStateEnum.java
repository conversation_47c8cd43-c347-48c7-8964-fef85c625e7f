package com.juzifenqi.virtual.component.enums;

import lombok.Getter;

/**
 * 原路退款状态
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/7 17:14
 */
@Getter
public enum RefundStateEnum {

    /**
     * 退款状态
     */
    S("S", "成功"), F("F", "失败");

    private final String code;
    private final String name;

    RefundStateEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }
}
