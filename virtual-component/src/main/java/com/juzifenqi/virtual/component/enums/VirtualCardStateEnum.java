package com.juzifenqi.virtual.component.enums;

/**
 * 卡密状态枚举，内部记录使用
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/10 16:42
 */
public enum VirtualCardStateEnum {
    DECODE_WAIT(1, "待获取卡密信息"), DECODE_ING(2, "获取中"), DECODE_SUCCESS(3, "获取成功"), DECODE_FAIL(4,
            "获取失败"),
    ;

    private int    code;
    private String desc;

    VirtualCardStateEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
