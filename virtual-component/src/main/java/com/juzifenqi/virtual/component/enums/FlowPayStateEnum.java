package com.juzifenqi.virtual.component.enums;

/**
 * 支付状态
 *
 * @description
 * <AUTHOR>
 * @Date 2024/9/19
 */
public enum FlowPayStateEnum {

    WAIT_PAY(1, "待支付"), PAY_SUCCESS(2, "支付成功"), PAY_FAIL(3, "支付失败");

    private int    code;
    private String desc;

    FlowPayStateEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
