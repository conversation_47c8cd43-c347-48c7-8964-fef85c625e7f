package com.juzifenqi.virtual.component.dingtalk;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.juzifenqi.virtual.component.dingtalk.bean.BaseMessage;
import com.juzifenqi.virtual.component.dingtalk.bean.DingTalkResponse;
import com.juzifenqi.virtual.component.dingtalk.bean.TextMessage;
import com.juzifenqi.virtual.component.util.IpUtil;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @verson 1.0
 * @date 2020/8/10 11:23 上午
 */
public class DingTalkClient {

    @NacosValue(value = "${dingtalk.urlPrefix:https://oapi.dingtalk.com/robot/send}", autoRefreshed = true)
    public String urlPrefix = "https://oapi.dingtalk.com/robot/send";

    @NacosValue(value = "${dingtalk.accessToken:ed7d624046a687f4326a9de33ad08fcb0319366961c3f713be3f5af72dfd0e94}", autoRefreshed = true)
    public String accessToken = "ed7d624046a687f4326a9de33ad08fcb0319366961c3f713be3f5af72dfd0e94";

    @NacosValue(value = "${dingtalk.secret.secretToken:SECa58a8de510a0ccafda235e07e067c7da55f9018873d9d83281f3ba9a9b9e940a}", autoRefreshed = true)
    public String secretToken="SECa58a8de510a0ccafda235e07e067c7da55f9018873d9d83281f3ba9a9b9e940a";

    private String getDefaultWebhook() {
        return getWebhook(accessToken, secretToken);
    }

    private String getWebhook(String accessToken, String secretToken) {
        String url = urlPrefix + "?access_token=" + accessToken;
        if (!StringUtils.isEmpty(secretToken)) {
            Long timestamp = System.currentTimeMillis();
            url += "&timestamp=" + timestamp + "&sign=" + getSign(secretToken, timestamp);
        }
        return url;
    }

    /**
     * 计算签名
     *
     * @param secret 密钥，机器人安全设置页面，加签一栏下面显示的SEC开头的字符
     * @param timestamp 当前时间戳，毫秒级单位
     * @return 根据时间戳计算后的签名信息
     */
    private static String getSign(String secret, Long timestamp) {
        try {
            String stringToSign = timestamp + "\n" + secret;
            Mac mac = Mac.getInstance("HmacSHA256");
            mac.init(new SecretKeySpec(secret.getBytes(StandardCharsets.UTF_8), "HmacSHA256"));
            byte[] signData = mac.doFinal(stringToSign.getBytes(StandardCharsets.UTF_8));
            return URLEncoder.encode(new String(Base64.getEncoder().encode(signData)), "UTF-8");
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 默认的发送钉钉的方法
     *
     * <AUTHOR>
     * @verson 1.0
     * @date 2021/1/26 9:57 上午
     */
    public DingTalkResponse sendMessage(BaseMessage message) {
        String result = DingTalkHttp.sendPost(getDefaultWebhook(), message.toMessageMap());
        return JSONObject.parseObject(result, DingTalkResponse.class);
    }

    /**
     * 增加发送ip和当前环境的方法
     *
     * <AUTHOR>
     * @verson 1.0
     * @date 2021/1/26 9:57 上午
     */
    public DingTalkResponse sendIpMessage(String message) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("预警服务: ").append("【virtual-center】").append("\n");
        stringBuilder.append("错误信息: ").append(message).append("\n");
        stringBuilder.append("服务器IP: ").append(IpUtil.getServerIp()).append("---")
                .append(IpUtil.getLinuxHostName()).append("\n");
        TextMessage textMessage = new TextMessage();
        textMessage.setContent(stringBuilder.toString());
        textMessage.setIsAtAll(true);
        String result = DingTalkHttp.sendPost(getDefaultWebhook(), textMessage.toMessageMap());
        return JSONObject.parseObject(result, DingTalkResponse.class);
    }

}