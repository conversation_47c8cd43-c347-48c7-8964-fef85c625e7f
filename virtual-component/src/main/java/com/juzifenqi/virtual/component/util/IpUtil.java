package com.juzifenqi.virtual.component.util;

import java.net.Inet4Address;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.util.Enumeration;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2017/11/10 13:59
 */
public class IpUtil {

    public static void main(String[] args) throws SocketException {
        String hostName = getLinuxHostName();
        System.out.println(hostName);
        String serverIp = getServerIp();
        System.out.println(serverIp);
    }

    public static InetAddress getInetAddress() {
        InetAddress ipHost = null;
        try {
            Enumeration<NetworkInterface> allNetInterfaces = NetworkInterface
                    .getNetworkInterfaces();
            while (allNetInterfaces.hasMoreElements()) {
                NetworkInterface netInterface = allNetInterfaces.nextElement();
                Enumeration<InetAddress> addresses = netInterface.getInetAddresses();
                while (addresses.hasMoreElements()) {
                    ipHost = addresses.nextElement();
                    if (ipHost instanceof Inet4Address) {
                        return ipHost;
                    }
                }
            }
        } catch (SocketException e) {
        }
        return ipHost;
    }

    public static String getLinuxHostName() {
        InetAddress inetAddress = getInetAddress();
        return inetAddress == null ? null : inetAddress.getHostName();
    }

    public static String getServerIp() {
        InetAddress inetAddress = getInetAddress();
        return inetAddress == null ? null : inetAddress.getHostAddress();
    }
}
