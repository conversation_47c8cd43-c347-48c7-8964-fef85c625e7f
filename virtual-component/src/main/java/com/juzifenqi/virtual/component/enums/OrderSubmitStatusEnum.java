package com.juzifenqi.virtual.component.enums;

/**
 * 订单下单状态枚举
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/20 9:36 下午
 */
public enum OrderSubmitStatusEnum {

    ORDER_NO_PUSH(0, "待下三方订单"), ORDER_SUCCESS(1, "下单成功"), ORDER_FAIL(2, "下单失败");

    private int    code;
    private String desc;

    OrderSubmitStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
