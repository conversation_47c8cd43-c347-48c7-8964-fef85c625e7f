<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.juzifenqi</groupId>
    <artifactId>virtual-center</artifactId>
    <version>ykd-1.0.0-SNAPSHOT</version>

    <packaging>pom</packaging>
    <name>virtual-center</name>
    <description>基础平台--权益对接服务</description>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.3.7.RELEASE</version>
        <relativePath/>
    </parent>

    <properties>
        <java.version>1.8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    </properties>

    <modules>
        <module>virtual-api</module>
        <module>virtual-dao</module>
        <module>virtual-core</module>
        <module>start</module>
        <module>virtual-bean</module>
        <module>virtual-manager</module>
        <module>virtual-component</module>
    </modules>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <distributionManagement>
        <repository>
            <id>releases</id>
            <url>http://nexus.juhuiwangluokeji.com:8081/nexus/content/repositories/releases</url>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <url>http://nexus.juhuiwangluokeji.com:8081/nexus/content/repositories/snapshots</url>
        </snapshotRepository>
    </distributionManagement>
    <repositories>
        <repository>
            <id>nexus</id>
            <name>Public Repositories</name>
            <url>http://nexus.juhuiwangluokeji.com:8081/nexus/content/groups/public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
    </repositories>
</project>
