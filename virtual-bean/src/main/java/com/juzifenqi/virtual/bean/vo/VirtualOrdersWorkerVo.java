package com.juzifenqi.virtual.bean.vo;


import com.juzifenqi.virtual.bean.pojo.VirtualOrders;
import java.io.Serializable;
import java.util.Date;

/**
 * 任务VO
 *
 * <AUTHOR>
 * @date 2023-07-14 10:33:23
 */

public class VirtualOrdersWorkerVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Integer id;

    /**
     * 权益订单号
     */
    private String orderSn;

    /**
     * 订单下单状态 0 未提交 1 下单成功 2 下单失败
     */
    private Integer orderSubmitStatus;

    /**
     * 订单下单时间
     */
    private Date orderSubmitTime;

    /**
     * 订单充值状态 0 未查证 1 充值成功 2 充值失败 4 存疑
     */
    private Integer orderRechargeStatus;

    /**
     * 订单充值时间
     */
    private Date orderRechargeTime;

    /**
     * 订单充值状态主动查证次数（上限自定义）
     */
    private Integer getRetryCount;

    /**
     * 节点备注信息
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 合作方id
     */
    private Integer supplierId;

    /**
     * 虚拟订单信息
     */
    private VirtualOrders virtualOrder;


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getOrderSn() {
        return orderSn;
    }

    public void setOrderSn(String orderSn) {
        this.orderSn = orderSn;
    }

    public Integer getOrderSubmitStatus() {
        return orderSubmitStatus;
    }

    public void setOrderSubmitStatus(Integer orderSubmitStatus) {
        this.orderSubmitStatus = orderSubmitStatus;
    }

    public Date getOrderSubmitTime() {
        return orderSubmitTime;
    }

    public void setOrderSubmitTime(Date orderSubmitTime) {
        this.orderSubmitTime = orderSubmitTime;
    }

    public Integer getOrderRechargeStatus() {
        return orderRechargeStatus;
    }

    public void setOrderRechargeStatus(Integer orderRechargeStatus) {
        this.orderRechargeStatus = orderRechargeStatus;
    }

    public Date getOrderRechargeTime() {
        return orderRechargeTime;
    }

    public void setOrderRechargeTime(Date orderRechargeTime) {
        this.orderRechargeTime = orderRechargeTime;
    }

    public Integer getGetRetryCount() {
        return getRetryCount;
    }

    public void setGetRetryCount(Integer getRetryCount) {
        this.getRetryCount = getRetryCount;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Integer supplierId) {
        this.supplierId = supplierId;
    }

    public VirtualOrders getVirtualOrder() {
        return virtualOrder;
    }

    public void setVirtualOrder(VirtualOrders virtualOrder) {
        this.virtualOrder = virtualOrder;
    }
}