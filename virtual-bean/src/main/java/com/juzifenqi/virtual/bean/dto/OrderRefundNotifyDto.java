package com.juzifenqi.virtual.bean.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 订单中心退款通知
 *
 * <AUTHOR>
 * @date 2024/9/2 16:05
 */
@Data
public class OrderRefundNotifyDto implements Serializable {

    private static final long serialVersionUID = 42L;

    /**
     * 退款支付流水号
     */
    private String serialNumber;

    /**
     * 退款业务流水号
     */
    private String thirdPayNum;

    /**
     * 退款类型 0-原路退款 1-退款换卡转代付 2-退款原卡转代付
     */
    private Integer refundType;

    /**
     * 退款状态 退款结果 F 失败 S成功
     */
    private String status;

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 退款金额（元）
     */
    private BigDecimal refundAmount;

    /**
     * 加密银行卡号
     */
    private String ctCardNo;

    /**
     * 退款通道
     */
    private String payChannel;

    /**
     * 退款成功时间
     */
    private Date successTime;

    /**
     * 支付错误码
     */
    private String errorCode;

    /**
     * 支付错误描述
     */
    private String errorMsg;

    /**
     * 扩展信息
     */
    private String extInfo;

    /**
     * 来源
     */
    private String source;

    /**
     * 用户姓名
     */
    private String customerName;

    /**
     * 收款银行名称
     */
    private String bankCardName;
    
}
