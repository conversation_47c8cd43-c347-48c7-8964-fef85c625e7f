package com.juzifenqi.virtual.bean.vo;


import com.juzifenqi.virtual.bean.pojo.VirtualOrderSupplierCoupon;
import com.juzifenqi.virtual.bean.pojo.VirtualOrdersCard;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * 充值成功VO
 *
 * <AUTHOR>
 * @date 2023-12-2 09:33:23
 */
@Data
public class VirtualOrderRechargeSuccVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 三方卡密信息
     */
    private VirtualOrdersCard card;

    /**
     * 三方优惠券信息
     */
    private VirtualOrderSupplierCoupon coupon;

    public VirtualOrderRechargeSuccVo(VirtualOrdersCard card) {
        this.card = card;
    }

    public VirtualOrderRechargeSuccVo(VirtualOrderSupplierCoupon coupon) {
        this.coupon = coupon;
    }
}