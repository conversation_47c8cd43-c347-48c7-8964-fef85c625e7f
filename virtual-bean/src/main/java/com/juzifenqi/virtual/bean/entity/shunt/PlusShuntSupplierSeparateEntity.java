package com.juzifenqi.virtual.bean.entity.shunt;

import java.math.BigDecimal;
import lombok.Data;

/**
 * 主体清分配置
 *
 * @description
 * <AUTHOR>
 * @Date 2024/9/21
 */
@Data
public class PlusShuntSupplierSeparateEntity {

    /**
     * 清分方式
     *
     * @see SeparateTypeEnum
     */
    private Integer separateType;

    /**
     * 清分主体id
     */
    private Integer separateSupplierId;

    /**
     * 清分比例
     */
    private BigDecimal separateRate;

    /**
     * 清分金额
     */
    private BigDecimal separateAmount;

    /**
     * 商务识别码（使用的时候现查缓存）
     */
    private String merchantId;
}
