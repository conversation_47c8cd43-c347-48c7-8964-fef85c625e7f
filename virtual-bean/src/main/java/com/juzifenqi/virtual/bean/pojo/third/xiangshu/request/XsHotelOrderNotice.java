package com.juzifenqi.virtual.bean.pojo.third.xiangshu.request;

import java.io.Serializable;
import lombok.Data;

/**
 * 橡树酒店订单回调-订单信息
 *
 * <AUTHOR>
 * @date 2023-12-02 10:33:23
 */
@Data
public class XsHotelOrderNotice implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 橡树用户id
     */
    private String user_id;

    /**
     * 订单名称
     */
    private String name;

    /**
     * 橡树订单状态 待支付 unpaid 系统取消 system_canceled 已支付，待确认 confirming 已确认，待入住 success 已入住 check_in 用户取消 user_canceled
     */
    private String status;

    /**
     * 订单描述
     */
    private String desc;

    /**
     * 订单金额
     */
    private String amount;

    /**
     * 单位
     */
    private String unit;

    /**
     * 订单来源
     */
    private String source;

    /**
     * 订单类型
     */
    private String type;

    /**
     * 外部订单号
     */
    private String out_order_id;

    /**
     * 桔多多订单号
     */
    private String confirmation;

    /**
     * 橡树唯一订单号
     */
    private String order_number;

    /**
     * 酒店订单创建时间
     */
    private String t_created;

    /**
     * 修改时间
     */
    private String t_modified;

    /**
     * 酒店信息
     */
    private XsHotelOrderPayInfo pay_info;

    /**
     * 用券信息
     */
    private XsHotelOrderCouponInfo coupon;
}
