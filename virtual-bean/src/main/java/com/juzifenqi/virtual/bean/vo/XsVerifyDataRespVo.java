package com.juzifenqi.virtual.bean.vo;

import java.io.Serializable;


/**
 * 橡树查证返回data
 *
 * <AUTHOR>
 * @date 2023-07-17 10:33:23
 */
public class XsVerifyDataRespVo implements Serializable {

    private static final long serialVersionUID = 40L;

    /**
     * 错误信息
     */
    private String err_message;

    /**
     * 订单状态 ：confirming：确认中 ，success：成功，failed：失败
     */
    private String status;

    /**
     * 橡树订单号
     */
    private String oak_order_number;

    /**
     * 桔子订单号
     */
    private String out_order_number;


    public String getOak_order_number() {
        return oak_order_number;
    }

    public void setOak_order_number(String oak_order_number) {
        this.oak_order_number = oak_order_number;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getErr_message() {
        return err_message;
    }

    public void setErr_message(String err_message) {
        this.err_message = err_message;
    }

    public String getOut_order_number() {
        return out_order_number;
    }

    public void setOut_order_number(String out_order_number) {
        this.out_order_number = out_order_number;
    }
}
