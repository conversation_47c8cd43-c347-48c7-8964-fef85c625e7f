package com.juzifenqi.virtual.bean.pojo;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 结算订单轨迹表
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/20 9:34 下午
 */
@Data
public class VirtualOrdersTrail implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Integer id;

    /**
     * 权益订单号
     */
    private String orderSn;

    /**
     * 订单流转之前状态
     */
    private Integer oldState;

    /**
     * 订单状态
     */
    private Integer nowState;

    /**
     * 流转备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

}