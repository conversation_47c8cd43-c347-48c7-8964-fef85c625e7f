package com.juzifenqi.virtual.bean.bo;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2019/3/27 9:56 AM
 */
public class PageEntity implements Serializable {

    private static final long serialVersionUID = -4657391917290885761L;

    private final static int DEFAULT_PAGE_NO   = 1;
    private final static int DEFAULT_PAGE_SIZE = 10;

    private Integer pageNo   = DEFAULT_PAGE_NO;
    private Integer pageSize = DEFAULT_PAGE_SIZE;

    public Integer getPageNo() {
        return pageNo;
    }

    public void setPageNo(Integer pageNo) {
        this.pageNo = pageNo;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public int getStartPage() {
        // 页数是从第一页是从1开始计算的
        if (pageNo == 0) {
            pageNo = DEFAULT_PAGE_NO;
        }
        return (pageNo - 1) * pageSize;
    }
}
