package com.juzifenqi.virtual.bean.vo;

import java.io.Serializable;


/**
 * 橡树下单返回data
 *
 * <AUTHOR>
 * @date 2023-07-17 10:33:23
 */
public class XsOrderDataRespVo implements Serializable {

    private static final long serialVersionUID = 40L;

    /**
     * 提示信息
     */
    private String message;

    /**
     * 橡树订单号
     */
    private String oak_order_number;

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getOak_order_number() {
        return oak_order_number;
    }

    public void setOak_order_number(String oak_order_number) {
        this.oak_order_number = oak_order_number;
    }
}
