package com.juzifenqi.virtual.bean.pojo.third.xiangshu.request;

import java.io.Serializable;
import lombok.Data;

/**
 * 橡树酒店订单回调-酒店信息
 *
 * <AUTHOR>
 * @date 2023-12-02 10:33:23
 */
@Data
public class XsHotelOrderPayInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 下单价格
     */
    private String item_price;

    /**
     * 是否含早餐
     */
    private String breakfast_type;

    /**
     * 离店时间
     */
    private String checkout_date;

    /**
     * 入住天数
     */
    private String stay_days;

    /**
     * 总价
     */
    private String total_price;

    /**
     * 酒店名
     */
    private String hotel_name;

    /**
     * 房间名
     */
    private String room_name;

    /**
     * 总价（原价）
     */
    private String total_origin_price;

    /**
     * 节省价格
     */
    private String total_price_difference;

    /**
     * 下单人手机号
     */
    private String phone;

    /**
     * 房间数量
     */
    private String room_cnt;

    /**
     * 入住时间
     */
    private String checkin_date;

    /**
     * 用户
     */
    private String user;

    /**
     * 实付总价
     */
    private String total_fee;

    /**
     * 取消原因
     */
    private String canceled_reason;

    /**
     * 优惠券优惠了多少钱
     */
    private String coupon_real_price;
}
