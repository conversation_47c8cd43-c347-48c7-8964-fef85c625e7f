package com.juzifenqi.virtual.bean.pojo;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 虚拟订单三方领取券信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/30 16:17
 */
@Data
public class VirtualOrderSupplierCoupon implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Integer id;

    /**
     * 虚拟订单号
     */
    private String orderSn;

    /**
     * 合作方id
     */
    private Integer supplierId;

    /**
     * 合作方名称
     */
    private String supplierName;

    /**
     * 合作方优惠券批次id
     */
    private String batchId;

    /**
     * 合作方优惠券id
     */
    private String couponId;

    /**
     * 状态 0_未领取 1_已领取 2_已失效
     */
    private Integer useState;

    /**
     * 优惠券有效期
     */
    private Date expirationTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;
}
