package com.juzifenqi.virtual.bean.pojo;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 存疑订单表
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/20 9:34 下午
 */
@Data
public class VirtualOrdersDoubt implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Integer id;

    /**
     * 下单用户id
     */
    private Integer userId;

    /**
     * 用户渠道
     */
    private Integer channelId;

    /**
     * 权益订单号
     */
    private String orderSn;

    /**
     * 存疑原因
     */
    private String reason;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

}