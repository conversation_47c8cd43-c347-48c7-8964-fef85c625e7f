package com.juzifenqi.virtual.bean.pojo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Author:gaoyu
 * @Date: 2022-05-23 09:51:29
 * @Description: 充值类型信息表
 */
@Data
public class VirtualRechargeType implements Serializable {

    private static final long serialVersionUID = 42L;

    /**
     * id
     */
    private Integer id;

    /**
     * 充值code
     */
    private Integer rechargeCode;

    /**
     * 充值类型
     */
    private String rechargeName;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private String updateBy;

    /**
     * 修改时间
     */
    private Date updateTime;

}