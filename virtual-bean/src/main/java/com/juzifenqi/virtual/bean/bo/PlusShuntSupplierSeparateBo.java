package com.juzifenqi.virtual.bean.bo;

import java.math.BigDecimal;
import lombok.Data;

/**
 * 主体清分配置
 *
 * <AUTHOR>
 * @date 2024/8/29 11:14
 */
@Data
public class PlusShuntSupplierSeparateBo {

    /**
     * 清分方式
     */
    private Integer separateType;

    /**
     * 清分主体id
     */
    private Integer separateSupplierId;

    /**
     * 清分比例
     */
    private BigDecimal separateRate;

    /**
     * 清分金额
     */
    private BigDecimal separateAmount;

    /**
     * 商务识别码（使用的时候现查缓存）
     */
    private String merchantId;
}
