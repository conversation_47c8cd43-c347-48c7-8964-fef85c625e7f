package com.juzifenqi.virtual.bean.pojo;


import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 结算订单任务表
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/20 9:34 下午
 */
@Data
public class VirtualOrdersWorker implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Integer id;

    /**
     * 权益订单号
     */
    private String orderSn;

    /**
     * 订单下单状态 0 未提交 1 下单成功 2 下单失败
     */
    private Integer orderSubmitStatus;

    /**
     * 订单下单时间
     */
    private Date orderSubmitTime;

    /**
     * 订单充值状态 0 未查证 1 充值成功 2 充值失败 4 存疑
     */
    private Integer orderRechargeStatus;

    /**
     * 订单充值时间
     */
    private Date orderRechargeTime;

    /**
     * 订单充值状态主动查证次数（上限自定义）
     */
    private Integer getRetryCount;

    /**
     * 节点备注信息
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

}