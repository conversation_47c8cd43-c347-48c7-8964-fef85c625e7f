package com.juzifenqi.virtual.bean.pojo;

import java.util.Date;
import lombok.Data;

/**
 * 虚拟权益订单退款信息
 *
 * <AUTHOR>
 * @date 2024/9/2 11:06
 */
@Data
public class VirtualOrderRefundInfo {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 会员单号
     */
    private String orderSn;

    /**
     * 退款业务流水号
     */
    private String refundSerialNo;

    /**
     * 支付退款流水号
     */
    private String paySerialNo;

    /**
     * 退款状态
     */
    private Integer refundState;

    /**
     * 备注
     */
    private String remark;

    /**
     * 支付回调时间
     */
    private Date payCallbackTime;

    /**
     * 操作人id
     */
    private Integer optUserId;

    /**
     * 操作人名称
     */
    private String optUserName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

}
