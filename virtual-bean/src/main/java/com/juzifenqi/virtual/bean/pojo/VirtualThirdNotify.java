package com.juzifenqi.virtual.bean.pojo;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 三方回调日志
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/10 14:30
 */
@Data
public class VirtualThirdNotify implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Integer id;

    /**
     * 虚拟订单号
     */
    private String orderSn;

    /**
     * 厂商订单号
     */
    private String supplierOrderSn;

    /**
     * 厂商商品id
     */
    private String supplierProductId;

    /**
     * 桔子商品id
     */
    private String productId;

    /**
     * 合作方id
     */
    private Integer supplierId;

    /**
     * 合作方名称
     */
    private String supplierName;

    /**
     * 通知类型 1_商品状态变更 2_商品价格变更 3_订单售后退款
     */
    private Integer notifyType;

    /**
     * 通知说明
     */
    private String notifyDesc;

    /**
     * 通知内容
     */
    private String notifyBody;

    /**
     * 创建时间
     */
    private Date createTime;
}
