package com.juzifenqi.virtual.bean.system;

import java.io.Serializable;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022年1月5日10:52:58
 **/
@Data
public class VirtualResult<T> implements Serializable {

    private static final long serialVersionUID = -1596994605228546558L;

    public static final String ERROR_CODE = "500";

    public static final String SUCCESS_CODE = "200";

    public static final String SYSTEM_ERROR_MSG = "系统异常...请稍后再尝试！";


    /**
     * 此字段表示业务是否成功，不是标识此次交互是否成功 譬如：保存：如果验证不通过或者系统内部异常，此字段为false，保存成功返回true
     * 查询：如果验证不通过或者系统内部异常，此字段返回false，如果查询返回有数据或者数据为空，标识此次查询成功 返回true
     */
    private boolean success;

    /**
     * 返回code码 如果success返回false，标识错误码 如果success返回true，标识200
     */
    private String code;

    /**
     * 返回msg信息 如果success返回false，标识错误信息 如果success返回true，标识成功
     */
    private String msg;

    private Integer rowsCount;

    private T result;


    public void success(String msg, T result) {
        this.success = true;
        this.code = SUCCESS_CODE;
        this.msg = msg;
        this.result = result;
    }

    public void success(Integer rowsCount, T result) {
        this.rowsCount = rowsCount;
        success(SUCCESS_CODE, result);
    }

    public void error(String code, String message) {
        this.success = false;
        this.code = code;
        this.msg = message;
    }

    public void error() {
        this.success = false;
        this.code = ERROR_CODE;
        this.msg = SYSTEM_ERROR_MSG;
    }

    public boolean isSuccess() {
        return success;
    }
}
