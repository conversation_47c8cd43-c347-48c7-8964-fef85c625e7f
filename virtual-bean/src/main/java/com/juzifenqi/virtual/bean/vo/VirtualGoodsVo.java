package com.juzifenqi.virtual.bean.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Description: 前端展示
 * @Author: sunchenhui
 * @Date: 2022/1/27 10:23
 */
@Data
public class VirtualGoodsVo implements Serializable {
    /**
     * id
     */
    private Integer id;

    /**
     * 商品id(权益id)
     */
    private Integer productId;

    /**
     * 商品名称(权益名称)
     */
    private String productName;

    /**
     * 商品sku
     */
    private String productSku;

    /**
     * 合作方id
     */
    private Integer supplierId;

    /**
     * 合作方名称
     */
    private String supplierName;

    /**
     * 合作方商品id
     */
    private String supplierItemId;

    /**
     * 合作方商品采购单价
     */
    private BigDecimal supplierItemPrice;

    /**
     * 库存量
     */
    private Long stock;

    /**
     * 商品上架状态 0 表示未上架 1 表示已上架
     */
    private Integer onsaleState;

    /**
     * 三方商品自定价
     */
    private BigDecimal privatePrice;

    /**
     * 充值code
     */
    private Integer rechargeCode;

    /**
     * 充值类型名称
     */
    private String rechargeName;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 修改人
     */
    private String updateBy;

    /**
     * 修改时间
     */
    private String updateTime;
    /**
     * 营销图片链接
     */
    private String imgUrl;
}
