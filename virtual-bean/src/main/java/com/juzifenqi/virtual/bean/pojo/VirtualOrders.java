package com.juzifenqi.virtual.bean.pojo;


import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 结算订单表
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/20 9:33 下午
 */
@Data
public class VirtualOrders implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Integer id;

    /**
     * 下单用户id
     */
    private Integer userId;

    /**
     * 下单用户名称
     */
    private String userName;

    /**
     * 用户渠道
     */
    private Integer channelId;

    /**
     * 下单手机号(废弃-使用充值账号)
     */
    private String mobile;

    /**
     * 充值账号
     */
    private String rechargeAccount;

    /**
     * 充值账号密文
     */
    private String rechargeAccountUuid;

    /**
     * 充值方式 1_直充 2_卡密
     */
    private Integer rechargeType;

    /**
     * 充值方式说明
     */
    private String rechargeTypeName;

    /**
     * 充值code
     */
    private Integer rechargeCode;

    /**
     * 充值类型名称
     */
    private String rechargeName;

    /**
     * 权益订单号
     */
    private String orderSn;

    /**
     * 开通会员单号
     */
    private String plusOrderSn;

    /**
     * 会员方案id
     */
    private Integer programId;

    /**
     * 会员方案名称
     */
    private String programName;

    /**
     * 商品sku
     */
    private String productSku;

    /**
     * 商品id(权益id)
     */
    private Integer productId;

    /**
     * 权益名称
     */
    private String productName;

    /**
     * 合作方单号
     */
    private String supplierOrderSn;

    /**
     * 合作方名称
     */
    private String supplierName;

    /**
     * 合作方商品id
     */
    private String supplierItemId;

    /**
     * 合作方id
     */
    private Integer supplierId;

    /**
     * 订单金额
     */
    private BigDecimal orderMoney;

    /**
     * 采购金额
     */
    private BigDecimal supplierMoney;

    /**
     * 下单状态 0 商城下单未支付 1 待充值下单 2 下单成功 3 下单失败 4 待充值查证 5 充值成功 6 充值失败 7 存疑订单
     */
    private Integer orderStatus;

    /**
     * 支付状态 1_待支付 2_支付成功 3_已退款
     */
    private Integer payStatus;

    /**
     * 充值状态 1_充值中 2_充值成功 3_充值失败，未支付状态下，充值状态为空
     */
    private Integer rechargeStatus;

    /**
     * 权益类型id
     */
    private Integer profitTypeId;

    /**
     * 跳转地址类型 1_经济 2_轻奢
     */
    private Integer supplierAccessType;

    /**
     * 跳转url
     */
    private String supplierAccessUrl;

    /**
     * 节点备注信息
     */
    private String remark;

    /**
     * 权益id 5_生活权益 33_渠道生活权益
     */
    private Integer modelId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 酒店订单状态， 0=无任何按钮  1=去预定酒店  2=查看酒店订单（入参不需要传入）
     */
    private Integer hotelOrderBtn = 0;
}