package com.juzifenqi.virtual.bean.entity.shunt;

import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * 分流主体
 *
 * @description
 * <AUTHOR>
 * @Date 2024/9/21
 */
@Data
public class PlusShuntSupplierEntity {

    /**
     * 主键
     */
    private Integer id;

    /**
     * 主体名称
     */
    private String supplierName;

    /**
     * 是否需要清分
     *
     * @see SupplierSeparateEnableStateEnum
     */
    private Integer separateEnableState;

    /**
     * 是否需要结算
     *
     * @see SupplierSettleEnableStateEnum
     */
    private Integer settleEnableState;

    /**
     * 是否启用 1_否 2_是
     */
    private Integer enableState;

    /**
     * 清分参数
     */
    private List<PlusShuntSupplierSeparateEntity> separateList;

    /**
     * 支付参数
     */
    private PlusShuntSupplierPayEntity pay;

    /**
     * 合同列表
     */
    private List<PlusShuntSupplierContractEntity> contractList;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;
}
