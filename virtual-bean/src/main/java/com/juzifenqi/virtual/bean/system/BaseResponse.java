package com.juzifenqi.virtual.bean.system;

import java.io.Serializable;
import lombok.Data;

@Data
public class BaseResponse implements Serializable {

    public static int     RESP_CODE_OK = 200;
    protected     boolean success;
    protected     int     code;
    protected     String  msg;

    public BaseResponse() {
    }

    public BaseResponse(boolean success, int code, String msg) {
        this.success = success;
        this.code = code;
        this.msg = msg;
    }

    public boolean isSuccess() {
        return this.success;
    }

    public boolean equals(Object o) {
        if (o == this)
            return true;
        if (!(o instanceof BaseResponse))
            return false;
        BaseResponse other = (BaseResponse) o;
        if (!other.canEqual(this))
            return false;
        if (isSuccess() != other.isSuccess())
            return false;
        if (getCode() != other.getCode())
            return false;
        Object this$msg = getMsg();
        Object other$msg = other.getMsg();
        if (this$msg == null ? other$msg != null : !this$msg.equals(other$msg))
            return false;
        return true;
    }

    protected boolean canEqual(Object other) {
        return other instanceof BaseResponse;
    }

    public String toString() {
        return "BaseResponse(success=" + isSuccess() + ", code=" + getCode() + ", msg=" + getMsg() + ")";
    }

}
