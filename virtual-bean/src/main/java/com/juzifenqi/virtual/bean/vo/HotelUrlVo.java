package com.juzifenqi.virtual.bean.vo;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 获取【去预定酒店】跳转的url
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/1 10:58
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class HotelUrlVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 预定酒店跳转url
     */
    private String hotelUrl;
}
