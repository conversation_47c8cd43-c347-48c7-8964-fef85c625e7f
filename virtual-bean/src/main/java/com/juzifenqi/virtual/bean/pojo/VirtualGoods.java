package com.juzifenqi.virtual.bean.pojo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 虚拟商品表
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/20 9:33 下午
 */
@Data
public class VirtualGoods implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Integer id;

    /**
     * 商品id(权益id)
     */
    private Integer productId;

    /**
     * 商品名称(权益名称)
     */
    private String productName;

    /**
     * 商品sku
     */
    private String productSku;

    /**
     * 合作方id
     */
    private Integer supplierId;

    /**
     * 合作方名称
     */
    private String supplierName;

    /**
     * 合作方商品id
     */
    private String supplierItemId;

    /**
     * 合作方商品采购单价
     */
    private BigDecimal supplierItemPrice;

    /**
     * 库存量
     */
    private Long stock;

    /**
     * 商品上架状态 0 表示未上架 1 表示已上架
     */
    private Integer onsaleState;

    /**
     * 三方商品自定价
     */
    private BigDecimal privatePrice;

    /**
     * 充值方式 1_直充 2_卡密
     */
    private Integer rechargeType;

    /**
     * 充值方式说明
     */
    private String rechargeTypeName;

    /**
     * 充值code
     */
    private Integer rechargeCode;

    /**
     * 充值类型名称
     */
    private String rechargeName;

    /**
     * 跳转地址类型 1_经济酒店 2_轻奢酒店
     */
    private Integer supplierAccessType;

    /**
     * 跳转url
     */
    private String supplierAccessUrl;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 修改人
     */
    private Integer updateByUserId;
    /**
     * 修改人
     */
    private String updateBy;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 营销图片链接
     */
    private String imgUrl;

}