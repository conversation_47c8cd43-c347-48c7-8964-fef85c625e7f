package com.juzifenqi.virtual.bean.bo;

import java.util.List;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description: 结算订单查询条件
 * @Author: sunchenhui
 * @Date: 2022/1/25 13:41
 */
@Data
public class VirtualOrdersBo extends PageEntity {

    /**
     * 下单用户id
     */
    private Integer userId;

    /**
     * 用户渠道
     */
    private Integer channelId;

    /**
     * 用户手机号
     */
    private String mobile;

    /**
     * 充值账号
     */
    private String rechargeAccount;

    /**
     * 充值code
     */
    private Integer rechargeCode;

    /**
     * 充值类型名称
     */
    private String rechargeName;

    /**
     * 权益订单号
     */
    private String orderSn;

    /**
     * 开通会员单号
     */
    private String plusOrderSn;

    /**
     * 会员方案id
     */
    private Integer programId;

    /**
     * 会员方案名称
     */
    private String programName;

    /**
     * 商品sku
     */
    private String productSku;

    /**
     * 商品id(权益id)
     */
    private Integer productId;

    /**
     * 权益名称
     */
    private String productName;

    /**
     * 合作方单号
     */
    private String supplierOrderSn;

    /**
     * 合作方名称
     */
    private String supplierName;

    /**
     * 合作方商品id
     */
    private String supplierItemId;

    /**
     * 合作方id
     */
    private Integer supplierId;

    /**
     * 订单金额
     */
    private BigDecimal orderMoney;

    /**
     * 采购金额
     */
    private BigDecimal supplierMoney;

    /**
     * 下单状态 0 商城下单未支付 1 待充值下单 2 下单成功 3 下单失败 4 待充值查证 5 充值成功 6 充值失败 7 存疑订单
     */
    private Integer orderStatus;

    /**
     * 权益类型id
     */
    private Integer profitTypeId;

    /**
     * 支付状态 1_待支付 2_支付成功 3_已退款
     */
    private List<Integer> payStatus;

    /**
     * 权益id
     */
    private Integer modelId;
}
