package com.juzifenqi.virtual.bean.bo;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/20 9:31 下午
 */
public class BaseBo implements Serializable {

    /**
     * 默认分页信息
     */
    private static final Integer DEFAULT_PAGE_NOW  = 1;
    private static final Integer DEFAULT_PAGE_SIZE = 10;


    /**
     * 当前查询页
     */
    private Integer pageNow;

    /**
     * 分页大小
     */
    private Integer pageSize;


    public Integer getPageNow() {
        if (pageNow == null || pageNow < 1) {
            return DEFAULT_PAGE_NOW;
        }
        return pageNow;
    }

    public void setPageNow(Integer pageNow) {
        this.pageNow = pageNow;
    }

    public Integer getPageSize() {
        if (pageSize == null || pageSize < 1) {
            return DEFAULT_PAGE_SIZE;
        }
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
}
