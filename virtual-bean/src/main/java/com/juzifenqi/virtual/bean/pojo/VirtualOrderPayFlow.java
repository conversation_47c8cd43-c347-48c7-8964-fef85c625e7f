package com.juzifenqi.virtual.bean.pojo;

import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 商品订单支付请求流水 映射表 virtual_order_pay_flow
 *
 * @description
 * <AUTHOR>
 * @Date 2024/9/21
 */
@Data
public class VirtualOrderPayFlow {

    /**
     * 主键
     */
    private Long id;

    /**
     * 权益订单号
     */
    private String orderSn;

    /**
     * 桔子用户ID
     */
    private Integer userId;

    /**
     * 请求支付流水号
     */
    private String applySerialNo;

    /**
     * 分流主体id
     */
    private Integer shuntSupplierId;

    /**
     * 订单总金额
     */
    private BigDecimal orderAmount;

    /**
     * 业务场景
     */
    private String businessScene;

    /**
     * 支付流水号
     */
    private String paySerialNo;

    /**
     * 支付状态 1_待支付 2_支付成功 3_支付失败
     */
    private Integer payState;

    /**
     * 支付回调时间
     */
    private Date payCallbackTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;
}
