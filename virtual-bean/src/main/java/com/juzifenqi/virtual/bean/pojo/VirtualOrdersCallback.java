package com.juzifenqi.virtual.bean.pojo;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 结算订单回调表
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/20 9:34 下午
 */
@Data
public class VirtualOrdersCallback implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Integer id;

    /**
     * 权益订单号
     */
    private String orderSn;

    /**
     * 合作方单号
     */
    private String supplierOrderSn;

    /**
     * 三方给定的订单状态(2-充值成功，3-充值失败)
     */
    private String callbackOrderStatus;

    /**
     * 三方给定的结果描述，充值成功/失败原因
     */
    private String callbackOrderDesc;

    /**
     * 桔子给三方的响应结果ok/fail(自定义)
     */
    private String response;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    private String remark;

}