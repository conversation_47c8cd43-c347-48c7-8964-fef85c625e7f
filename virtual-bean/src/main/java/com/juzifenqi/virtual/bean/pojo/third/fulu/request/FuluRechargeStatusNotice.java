package com.juzifenqi.virtual.bean.pojo.third.fulu.request;

import java.io.Serializable;
import lombok.Data;

/**
 * 福禄回调返回值
 *
 * <AUTHOR>
 * @date 2023-11-09 16:33:23
 */
@Data
public class FuluRechargeStatusNotice implements Serializable {

    /**
     * 订单状态 ： success：成功，failed：失败
     */
    private String order_status;

    /**
     * 桔子订单号
     */
    private String customer_order_no;

    /**
     * 开放平台订单号
     */
    private String order_id;

    /**
     * 交易完成时间，格式为：yyyy-MM-dd HH:mm:ss
     */
    private String charge_finish_time;

    /**
     * 充值描述
     */
    private String recharge_description;

    /**
     * 商品ID
     */
    private String product_id;

    /**
     * 交易单价
     */
    private String price;

    /**
     * 购买数量
     */
    private String buy_num;

    /**
     * 运营商流水号
     */
    private String operator_serial_number;

    /**
     * 签名
     */
    private String sign;
}
