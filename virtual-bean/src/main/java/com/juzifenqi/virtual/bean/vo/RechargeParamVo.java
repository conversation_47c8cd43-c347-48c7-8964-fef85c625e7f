package com.juzifenqi.virtual.bean.vo;


import java.io.Serializable;

/**
 * 调三方充值入参
 *
 * <AUTHOR>
 * @date 2023-07-14 10:33:23
 */

public class RechargeParamVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 权益订单号
     */
    private String orderSn;

    /**
     * 黑卡商品编号
     */
    private String productCode;

    /**
     * 回调地址
     */
    private String callbackUrl;

    /**
     * 充值账号
     */
    private String rechargeAccount;

    /**
     * 充值code::1.手机号 2.抖音账号 3.微博昵称 4.qq号
     */
    private Integer rechargeCode;

    /**
     * 权益appkey
     */
    private String profitKey;

    /**
     * 权益appSecret
     */
    private String profitSecret;

    public String getOrderSn() {
        return orderSn;
    }

    public void setOrderSn(String orderSn) {
        this.orderSn = orderSn;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getCallbackUrl() {
        return callbackUrl;
    }

    public void setCallbackUrl(String callbackUrl) {
        this.callbackUrl = callbackUrl;
    }


    public String getRechargeAccount() {
        return rechargeAccount;
    }

    public void setRechargeAccount(String rechargeAccount) {
        this.rechargeAccount = rechargeAccount;
    }

    public Integer getRechargeCode() {
        return rechargeCode;
    }

    public void setRechargeCode(Integer rechargeCode) {
        this.rechargeCode = rechargeCode;
    }

    public String getProfitKey() {
        return profitKey;
    }

    public void setProfitKey(String profitKey) {
        this.profitKey = profitKey;
    }

    public String getProfitSecret() {
        return profitSecret;
    }

    public void setProfitSecret(String profitSecret) {
        this.profitSecret = profitSecret;
    }
}