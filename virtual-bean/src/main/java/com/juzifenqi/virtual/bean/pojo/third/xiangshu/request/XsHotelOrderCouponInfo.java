package com.juzifenqi.virtual.bean.pojo.third.xiangshu.request;

import java.io.Serializable;
import lombok.Data;

/**
 * 橡树酒店订单回调-用券信息
 *
 * <AUTHOR>
 * @date 2023-12-02 10:33:23
 */
@Data
public class XsHotelOrderCouponInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 优惠券ID
     */
    private String coupon_id;

    /**
     * 批次ID
     */
    private String batch_id;

    /**
     * 状态
     */
    private String status;

    /**
     * 领取时间
     */
    private String draw_time;

    /**
     * 是否可用
     */
    private String enable;

    /**
     * 到期时间
     */
    private String t_expired;

    /**
     * 创建时间
     */
    private String t_created;

    /**
     * 修改时间
     */
    private String t_modified;
}
