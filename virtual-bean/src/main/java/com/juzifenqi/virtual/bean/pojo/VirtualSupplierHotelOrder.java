package com.juzifenqi.virtual.bean.pojo;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 三方酒店订单
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/30 16:17
 */
@Data
public class VirtualSupplierHotelOrder implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Integer id;

    /**
     * 虚拟订单号
     */
    private String orderSn;

    /**
     * 合作方id
     */
    private Integer supplierId;

    /**
     * 合作方名称
     */
    private String supplierName;

    /**
     * 合作方酒店订单号
     */
    private String hotelOrderSn;

    /**
     * 合作方酒店订单创建时间
     */
    private String hotelOrderCreateTime;

    /**
     * 合作方用户id
     */
    private String supplierUserId;

    /**
     * 下单用户
     */
    private String supplierUser;

    /**
     * 下单人手机号
     */
    private String supplierUserPhone;

    /**
     * 下单人手机号
     */
    private String supplierUserPhoneUuid;

    /**
     * 合作方优惠券id
     */
    private String couponId;

    /**
     * 订单名称
     */
    private String orderName;

    /**
     * 订单描述
     */
    private String orderDesc;

    /**
     * 订单金额
     */
    private String orderAmount;

    /**
     * 单位
     */
    private String orderUnit;

    /**
     * 订单来源
     */
    private String orderSource;

    /**
     * 订单类型
     */
    private String orderType;

    /**
     * 外部订单号
     */
    private String outOrderId;

    /**
     * 下单价格
     */
    private String itemPrice;

    /**
     * 是否含早餐
     */
    private String breakfastType;

    /**
     * 入住时间
     */
    private String checkInDate;

    /**
     * 离店时间
     */
    private String checkOutDate;

    /**
     * 入住天数
     */
    private String stayDays;

    /**
     * 总价
     */
    private String totalPrice;

    /**
     * 酒店名
     */
    private String hotelName;

    /**
     * 房间名
     */
    private String roomName;

    /**
     * 总价（原价）
     */
    private String totalOriginPrice;

    /**
     * 节省价格
     */
    private String totalPriceDifference;

    /**
     * 房间数量
     */
    private String roomCnt;

    /**
     * 实付总价
     */
    private String totalFee;

    /**
     * 取消原因
     */
    private String canceledReason;

    /**
     * 优惠券优惠金额
     */
    private String couponRealPrice;

    /**
     * 状态 1_待支付 2_系统取消 3_已支付，待确认 4_已确认，待入住 5_已入住 6_用户取消
     */
    private Integer orderStatus;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
