package com.juzifenqi.virtual.bean.vo;

import java.io.Serializable;


/**
 * 橡树查证返回结果
 *
 * <AUTHOR>
 * @date 2023-07-17 10:33:23
 */
public class XsVerifyRespVo implements Serializable {

    private static final long serialVersionUID = 40L;

    /**
     * 处理结果
     */
    private boolean ok;
    /**
     * code码
     */
    private String  code;

    /**
     * 请求信息
     */
    private String msg;

    /**
     * 返回数据信息
     */
    private XsVerifyDataRespVo data;

    public boolean isOk() {
        return ok;
    }

    public void setOk(boolean ok) {
        this.ok = ok;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public XsVerifyDataRespVo getData() {
        return data;
    }

    public void setData(XsVerifyDataRespVo data) {
        this.data = data;
    }
}
