package com.juzifenqi.virtual.bean.system;

import java.io.Serializable;
import java.util.List;

/**
 * 分页查询结果封装
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/20 9:35 下午
 */
public class TableResult<T>  implements Serializable {

    /**
     * 查询出来的记录
     */
    private List<T> rows;

    /**
     * 总条数
     */
    private Long totalCount;

    /**
     * 总的页数 totalCount = 91 pageSize = 10 ====10页
     */
    private Integer pageCount;

    /**
     * 当前查询页
     */
    private Integer pageNow;

    /**
     * 分页大小
     */
    private Integer pageSize;

    public List<T> getRows() {
        return rows;
    }

    public void setRows(List<T> rows) {
        this.rows = rows;
    }

    public Long getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Long totalCount) {
        this.totalCount = totalCount;
    }

    public Integer getPageCount() {
        return pageCount;
    }

    public void setPageCount(Integer pageCount) {
        this.pageCount = pageCount;
    }

    public Integer getPageNow() {
        return pageNow;
    }

    public void setPageNow(Integer pageNow) {
        this.pageNow = pageNow;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
}
