package com.juzifenqi.virtual.bean.vo;

import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * @description
 * <AUTHOR>
 * @Date 2024/9/19
 */
@Data
public class VirtualOrderPayInfoVo implements Serializable {
    /**
     * 业务渠道 com.juzifenqi.plus.constants.CommonConstant#SC_APPLICATION_NAME
     */
    private String application;

    /**
     * 业务场景
     */
    private String businessScene;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 业务申请支付流水号
     */
    private String applySerialNo;

    /**
     * 交易类型 D首付、F全款、R会员、REPAY还款 DEDUCT批扣 默认 R（会员）
     */
    private String tradeType;
    /**
     * com.juzifenqi.plus.constants.PaySourceConstant#PAY_SOURCE_MEMBER
     */
    private String source;

    /**
     * 总金额 单位元
     */
    private BigDecimal totalAmount;
    /**
     * 会员订单号
     */
    private String     orderSn;
    /**
     * 还款卡=会员续费 其他卡=会员主动划扣
     */
    private String     tradeName;
}
