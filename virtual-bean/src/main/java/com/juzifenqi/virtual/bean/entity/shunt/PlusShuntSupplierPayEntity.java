package com.juzifenqi.virtual.bean.entity.shunt;

import lombok.Data;

/**
 * 分流主体支付参数配置
 *
 * @description
 * <AUTHOR>
 * @Date 2024/9/21
 */
@Data
public class PlusShuntSupplierPayEntity {

    /**
     * 主体id
     */
    private Integer supplierId;

    /**
     * 划扣通道
     */
    private String deductPayChannel;

    /**
     * 划扣来源
     */
    private String deductPaySource;

    /**
     * 代付商编
     */
    private String defrayMerchantId;

    /**
     * 商户识别码
     */
    private String merchantId;

    /**
     * 业务场景
     */
    private String businessScene;

    /**
     * 公司账户号
     */
    private String bankAccountNo;

    /**
     * 公司账户名称
     */
    private String bankAccountName;

    /**
     * 公司账户类型 1_对公 2_对私
     */
    private Integer bankAccountType;

    /**
     * 支行行号
     */
    private String bankBranchNo;

    /**
     * 开户行
     */
    private String bankName;

    /**
     * 开户行所在省名称
     */
    private String bankProvinceName;

    /**
     * 开户行所在省code
     */
    private String bankProvinceCode;

    /**
     * 开户行所在市名称
     */
    private String bankCityName;

    /**
     * 开户行所在市code
     */
    private String bankCityCode;

}
