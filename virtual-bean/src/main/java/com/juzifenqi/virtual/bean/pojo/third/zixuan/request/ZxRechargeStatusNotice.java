package com.juzifenqi.virtual.bean.pojo.third.zixuan.request;

import lombok.Data;

/**
 * 子轩订单充值结果回调入参
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/22 2:54 下午
 */
@Data
public class ZxRechargeStatusNotice {

    /**
     * 应用ID
     */
    private String appId;

    /**
     * 子轩订单号
     */
    private String orderId;

    /**
     * 桔子订单号
     */
    private String outOrderId;

    /**
     * 订单状态，2-充值成功，3-充值失败
     */
    private String orderStatus;

    /**
     * 结果描述，充值成功/失败原因
     */
    private String orderDesc;

    /**
     * 订单完成时间，格式：yyyyMMddHHmmss
     */
    private String completeTime;

    /**
     * 签名
     */
    private String sign;

}
