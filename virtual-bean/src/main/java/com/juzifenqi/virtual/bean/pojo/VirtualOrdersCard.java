package com.juzifenqi.virtual.bean.pojo;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 虚拟订单卡密表
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/10 14:34 下午
 */
@Data
public class VirtualOrdersCard implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Integer id;

    /**
     * 虚拟订单号
     */
    private String orderSn;

    /**
     * 状态 1_待获取卡密信息 2_获取中 3_获取成功 4_获取失败/异常
     */
    private Integer cardState;

    /**
     * 获取卡密信息重试次数
     */
    private Integer retryNum;

    /**
     * 类型 1_普通卡密 2_二维码 3_短链
     */
    private Integer cardType;

    /**
     * 卡号密文
     */
    private String cardNo;

    /**
     * 密码密文
     */
    private String cardPwd;

    /**
     * 有效期截止时间
     */
    private Date cardDeadline;

    /**
     * 卡密短链密文
     */
    private String cardLink;

    /**
     * 卡密二维码密文
     */
    private String cardQrcode;

    /**
     * 运营商流水号
     */
    private String operatorSerialNumber;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建时间
     */
    private Date updateTime;
}
