package com.juzifenqi.virtual.bean.bo;

import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * 分流主体信息
 *
 * <AUTHOR>
 * @date 2024/9/9 09:57
 */
@Data
public class PlusShuntSupplierBo {

    /**
     * 主键
     */
    private Integer id;

    /**
     * 主体名称
     */
    private String supplierName;

    /**
     * 是否需要清分
     */
    private Integer separateEnableState;

    /**
     * 是否需要结算
     */
    private Integer settleEnableState;

    /**
     * 是否启用 1_否 2_是
     */
    private Integer enableState;

    /**
     * 清分参数
     */
    private List<PlusShuntSupplierSeparateBo> separateList;

    /**
     * 支付参数
     */
    private PlusShuntSupplierPayBo pay;

    /**
     * 合同列表
     */
    private List<PlusShuntSupplierContractBo> contractList;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

}
