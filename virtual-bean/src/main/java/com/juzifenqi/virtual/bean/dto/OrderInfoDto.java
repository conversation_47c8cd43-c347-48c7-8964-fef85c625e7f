package com.juzifenqi.virtual.bean.dto;

import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 订单状态变更信息
 *
 * <AUTHOR>
 * @date 2024/7/11 下午2:02
 */
@Data
public class OrderInfoDto {

    private Integer channel;

    private Integer memberId;

    private BigDecimal moneyOrder;

    private String orderNode;

    private String orderSn;

    private String orderChildNode;

    private Integer orderStatus;

    private Date createTime;

    /**
     * 0-无任何绑定会员卡（有可能null） 1- 融单卡 2- 重提客群-自创订单--孟强同步的
     */
    private Integer memberShip;

    /**
     * 分期期数
     */
    private Integer periodNum;

    /**
     * 银行卡id
     */
    private Integer bankId;

    /**
     * 订单支付类型：0 分期  1 全款
     */
    private Integer isFullPayment;

    /**
     * 商品id
     */
    private Integer productId;

    /**
     * 小额月卡标识
     */
    private Boolean smallMonthlyCard;

    /**
     * 订单归因标记-可空
     */
    private String ascribeTo;

    /**
     * 资方id
     */
    private Integer capitalId;

}
