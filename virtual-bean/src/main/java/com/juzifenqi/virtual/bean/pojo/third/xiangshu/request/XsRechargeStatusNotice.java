package com.juzifenqi.virtual.bean.pojo.third.xiangshu.request;

import lombok.Data;

/**
 * 橡树回调返回值
 *
 * <AUTHOR>
 * @date 2023-07-18 10:33:23
 */
@Data
public class XsRechargeStatusNotice {

    /**
     * 订单状态 ： success：成功，failed：失败
     */
    private String status;

    /**
     * 桔子订单号
     */
    private String out_order_number;

    /**
     * 橡树订单号
     */
    private String oak_order_number;

    /**
     * 错误信息
     */
    private String err_message;

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getOut_order_number() {
        return out_order_number;
    }

    public void setOut_order_number(String out_order_number) {
        this.out_order_number = out_order_number;
    }

    public String getOak_order_number() {
        return oak_order_number;
    }

    public void setOak_order_number(String oak_order_number) {
        this.oak_order_number = oak_order_number;
    }

    public String getErr_message() {
        return err_message;
    }

    public void setErr_message(String err_message) {
        this.err_message = err_message;
    }
}
