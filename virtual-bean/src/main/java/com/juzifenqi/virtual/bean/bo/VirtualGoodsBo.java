package com.juzifenqi.virtual.bean.bo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 条件参数
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/20 9:31 下午
 */
@Data
public class VirtualGoodsBo extends PageEntity {

    /**
     * 商品名称(权益名称)
     */
    private String productName;

    /**
     * 货品id(权益id)
     */
    private Integer productId;

    /**
     * 商品sku
     */
    private String productSku;

    /**
     * 合作方名称
     */
    private String supplierName;

    /**
     * 合作方商品id
     */
    private String supplierItemId;

    /**
     * 商品上架状态 0 表示未上架 1 表示已上架
     */
    private Integer onsaleState;

    /**
     * 三方商品自定价
     */
    private BigDecimal privatePrice;

    /**
     * 充值code
     */
    private Integer rechargeCode;

    /**
     * 充值类型名称
     */
    private String rechargeName;
}
