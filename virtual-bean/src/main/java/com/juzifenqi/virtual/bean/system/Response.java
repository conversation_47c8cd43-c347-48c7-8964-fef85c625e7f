package com.juzifenqi.virtual.bean.system;

import lombok.Data;


@Data
public class Response <T> extends BaseResponse {

    private T data;

    public Response(boolean success, int code, String msg) {
        super(success, code, msg);
        this.data = null;
    }

    public Response(boolean success, T result, int code, String msg) {
        super(success, code, msg);
        this.data = result;
    }

    public static <T> Response<T> ok() {
        return new Response<>(true, RESP_CODE_OK, null);
    }

    public static <T> Response<T> ok(T result) {
        return new Response<>(true, result, RESP_CODE_OK, null);
    }

    public static <T> Response<T> fail(int code, String respMsg) {
        return new Response<>(false, code, respMsg);
    }

    public static <T> Response<T> fail(int code, String msg, Throwable e) {
        return new Response<>(false, code, msg);
    }

    public static <T> Response<T> fail(int code, String msgFormat, Object[] args) {
        return new Response(false, code, String.format(msgFormat, args));
    }

    public static <T> Response<T> fail(String msg) {
        return fail(-1, msg);
    }

    public boolean isOk() {
        return this.success;
    }

    public boolean equals(Object o) {
        if (o == this)
            return true;
        if (!(o instanceof Response))
            return false;
        Response other = (Response) o;
        if (!other.canEqual(this))
            return false;
        Object this$data = getData();
        Object other$data = other.getData();
        return this$data == null ? other$data == null : this$data.equals(other$data);
    }

    protected boolean canEqual(Object other) {
        return other instanceof Response;
    }

    public String toString() {
        return "Response(data=" + getData() + ")";
    }

}
