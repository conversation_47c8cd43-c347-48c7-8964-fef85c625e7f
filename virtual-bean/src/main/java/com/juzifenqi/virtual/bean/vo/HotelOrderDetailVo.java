package com.juzifenqi.virtual.bean.vo;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 获取酒店详情需要的参数
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/1 15:42
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class HotelOrderDetailVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 三方用户token
     */
    private String token;

    /**
     * 三方酒店订单号
     */
    private String orderSn;
}
