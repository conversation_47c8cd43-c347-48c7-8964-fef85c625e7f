<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.virtual.dao.VirtualOrderPayFlowDao">
    <resultMap id="virtualOrderPayFlowMap" type="com.juzifenqi.virtual.bean.pojo.VirtualOrderPayFlow">
        <result column="id" property="id"/>
        <result column="order_sn" property="orderSn"/>
        <result column="user_id" property="userId"/>
        <result column="apply_serial_no" property="applySerialNo"/>
        <result column="shunt_supplier_id" property="shuntSupplierId"/>
        <result column="order_amount" property="orderAmount"/>
        <result column="business_scene" property="businessScene"/>
        <result column="pay_serial_no" property="paySerialNo"/>
        <result column="pay_state" property="payState"/>
        <result column="pay_callback_time" property="payCallbackTime"/>
        <result column="remark" property="remark"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        order_sn,
        user_id,
        apply_serial_no,
        shunt_supplier_id,
        order_amount,
        business_scene,
        pay_serial_no,
        pay_state,
        pay_callback_time,
        remark,
        create_time,
        update_time
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyColumn="id" keyProperty="id">
        INSERT INTO virtual_order_pay_flow (order_sn,
                                            user_id,
                                            apply_serial_no,
                                            shunt_supplier_id,
                                            order_amount,
                                            business_scene,
                                            pay_serial_no,
                                            pay_state,
                                            pay_callback_time,
                                            remark,
                                            create_time,
                                            update_time)
        values (#{orderSn},
                #{userId},
                #{applySerialNo},
                #{shuntSupplierId},
                #{orderAmount},
                #{businessScene},
                #{paySerialNo},
                #{payState},
                #{payCallbackTime},
                #{remark},
                NOW(),
                NOW())
    </insert>

    <select id="listByOrderSnAndPayState" resultMap="virtualOrderPayFlowMap">
        select <include refid="Base_Column_List"/>
        FROM virtual_order_pay_flow
        WHERE order_sn = #{orderSn}
        and pay_state = #{payState}
    </select>

    <select id="getByApplySerialNo" resultMap="virtualOrderPayFlowMap">
        select <include refid="Base_Column_List"/>
        FROM virtual_order_pay_flow
        WHERE apply_serial_no = #{applySerialNo}
        <if test="null != payState ">
            and pay_state = #{payState}
        </if>
    </select>

    <update id="update">
        UPDATE virtual_order_pay_flow
        set
            <if test="null != paySerialNo and '' != paySerialNo">
                pay_serial_no = #{paySerialNo},
            </if>
            <if test="null != payState ">
                pay_state = #{payState},
            </if>
            <if test="null != payCallbackTime ">
                pay_callback_time = #{payCallbackTime},
            </if>
            <if test="null != remark and '' != remark">
                remark = #{remark},
            </if>
            update_time = NOW()
        WHERE id = #{id}
    </update>

</mapper>