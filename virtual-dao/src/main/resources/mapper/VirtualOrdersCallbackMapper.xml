<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.virtual.dao.VirtualOrdersCallbackDao">
    <resultMap id="VirtualOrdersCallbackMap" type="com.juzifenqi.virtual.bean.pojo.VirtualOrdersCallback" >
        <result column="id" property="id" />
        <result column="order_sn" property="orderSn" />
        <result column="supplier_order_sn" property="supplierOrderSn" />
        <result column="callback_order_status" property="callbackOrderStatus" />
        <result column="callback_order_desc" property="callbackOrderDesc" />
        <result column="response" property="response" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <insert id="saveVirtualOrdersCallback" useGeneratedKeys="true" keyColumn="id" keyProperty="id" parameterType="com.juzifenqi.virtual.bean.pojo.VirtualOrdersCallback">
        INSERT INTO virtual_orders_callback
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != orderSn and '' != orderSn">
                order_sn,
            </if>
            <if test="null != supplierOrderSn and '' != supplierOrderSn">
                supplier_order_sn,
            </if>
            <if test="null != callbackOrderStatus and '' != callbackOrderStatus">
                callback_order_status,
            </if>
            <if test="null != callbackOrderDesc and '' != callbackOrderDesc">
                callback_order_desc,
            </if>
            <if test="null != response and '' != response">
                response,
            </if>
            <if test="null != remark and '' != remark">
                remark,
            </if>
            create_time,
            update_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != orderSn and '' != orderSn">
                #{orderSn},
            </if>
            <if test="null != supplierOrderSn and '' != supplierOrderSn">
                #{supplierOrderSn},
            </if>
            <if test="null != callbackOrderStatus and '' != callbackOrderStatus">
                #{callbackOrderStatus},
            </if>
            <if test="null != callbackOrderDesc and '' != callbackOrderDesc">
                #{callbackOrderDesc},
            </if>
            <if test="null != response and '' != response">
                #{response},
            </if>
            <if test="null != remark and '' != remark">
                #{remark},
            </if>
            now(),now()
        </trim>
    </insert>
</mapper>