<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.virtual.dao.VirtualOrdersTrailDao">
    <resultMap id="VirtualOrdersTrailMap" type="com.juzifenqi.virtual.bean.pojo.VirtualOrdersTrail" >
        <result column="id" property="id" />
        <result column="order_sn" property="orderSn" />
        <result column="old_state" property="oldState" />
        <result column="now_state" property="nowState" />
        <result column="remark" property="remark" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <insert id="saveVirtualOrdersTrail" useGeneratedKeys="true" keyColumn="id" keyProperty="id" parameterType="com.juzifenqi.virtual.bean.pojo.VirtualOrdersTrail">
        INSERT INTO virtual_orders_trail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != orderSn and '' != orderSn">
                order_sn,
            </if>
            <if test="null != oldState">
                old_state,
            </if>
            <if test="null != nowState and '' != nowState">
                now_state,
            </if>
            <if test="null != remark and '' != remark">
                remark,
            </if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != orderSn and '' != orderSn">
                #{orderSn},
            </if>
            <if test="null != oldState">
                #{oldState},
            </if>
            <if test="null != nowState and '' != nowState">
                #{nowState},
            </if>
            <if test="null != remark and '' != remark">
                #{remark},
            </if>
            now()
        </trim>
    </insert>

    <select id="selectByOrderSn" resultMap="VirtualOrdersTrailMap">
        select * from virtual_orders_trail where order_sn = #{orderSn} order by id
    </select>
</mapper>