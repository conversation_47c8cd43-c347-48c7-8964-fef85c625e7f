<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.virtual.dao.VirtualSupplierHotelOrderDao">

    <resultMap id="VirtualSupplierHotelOrder" type="com.juzifenqi.virtual.bean.pojo.VirtualSupplierHotelOrder" >
        <result column="id" property="id" />
        <result column="order_sn" property="orderSn" />
        <result column="supplier_id" property="supplierId" />
        <result column="supplier_name" property="supplierName" />
        <result column="hotel_order_sn" property="hotelOrderSn" />
        <result column="hotel_order_create_time" property="hotelOrderCreateTime" />
        <result column="supplier_user_id" property="supplierUserId" />
        <result column="supplier_user" property="supplierUser" />
        <result column="supplier_user_phone" property="supplierUserPhone" />
        <result column="coupon_id" property="couponId" />
        <result column="order_name" property="orderName" />
        <result column="order_desc" property="orderDesc" />
        <result column="order_amount" property="orderAmount" />
        <result column="order_unit" property="orderUnit" />
        <result column="order_source" property="orderSource" />
        <result column="order_type" property="orderType" />
        <result column="out_order_id" property="outOrderId" />
        <result column="item_price" property="itemPrice" />
        <result column="breakfast_type" property="breakfastType" />
        <result column="check_in_date" property="checkInDate" />
        <result column="check_out_date" property="checkOutDate" />
        <result column="stay_days" property="stayDays" />
        <result column="total_price" property="totalPrice" />
        <result column="hotel_name" property="hotelName" />
        <result column="room_name" property="roomName" />
        <result column="total_origin_price" property="totalOriginPrice" />
        <result column="total_price_difference" property="totalPriceDifference" />
        <result column="room_cnt" property="roomCnt" />
        <result column="total_fee" property="totalFee" />
        <result column="canceled_reason" property="canceledReason" />
        <result column="coupon_real_price" property="couponRealPrice" />
        <result column="order_status" property="orderStatus" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `order_sn`,
        `supplier_id`,
        `supplier_name`,
        `hotel_order_sn`,
        `hotel_order_create_time`,
        `supplier_user_id`,
        `supplier_user`,
        `supplier_user_phone`,
        `coupon_id`,
        `order_name`,
        `order_desc`,
        `order_amount`,
        `order_unit`,
        `order_source`,
        `order_type`,
        `out_order_id`,
        `item_price`,
        `breakfast_type`,
        `check_in_date`,
        `check_out_date`,
        `stay_days`,
        `total_price`,
        `hotel_name`,
        `room_name`,
        `total_origin_price`,
        `total_price_difference`,
        `room_cnt`,
        `total_fee`,
        `canceled_reason`,
        `coupon_real_price`,
        `order_status`,
        `create_time`,
        `update_time`
    </sql>

    <insert id="save" parameterType="com.juzifenqi.virtual.bean.pojo.VirtualSupplierHotelOrder">
        INSERT INTO virtual_supplier_hotel_order (
        `order_sn`,
        `supplier_id`,
        `supplier_name`,
        `hotel_order_sn`,
        `hotel_order_create_time`,
        `supplier_user_id`,
        `supplier_user`,
        `supplier_user_phone`,
        `coupon_id`,
        `order_name`,
        `order_desc`,
        `order_amount`,
        `order_unit`,
        `order_source`,
        `order_type`,
        `out_order_id`,
        `item_price`,
        `breakfast_type`,
        `check_in_date`,
        `check_out_date`,
        `stay_days`,
        `total_price`,
        `hotel_name`,
        `room_name`,
        `total_origin_price`,
        `total_price_difference`,
        `room_cnt`,
        `total_fee`,
        `canceled_reason`,
        `coupon_real_price`,
        `order_status`,
        `create_time`,
        `update_time`
        )
        VALUES(
        #{virtualSupplierHotelOrder.orderSn},
        #{virtualSupplierHotelOrder.supplierId},
        #{virtualSupplierHotelOrder.supplierName},
        #{virtualSupplierHotelOrder.hotelOrderSn},
        #{virtualSupplierHotelOrder.hotelOrderCreateTime},
        #{virtualSupplierHotelOrder.supplierUserId},
        #{virtualSupplierHotelOrder.supplierUser},
        #{virtualSupplierHotelOrder.supplierUserPhone},
        #{virtualSupplierHotelOrder.couponId},
        #{virtualSupplierHotelOrder.orderName},
        #{virtualSupplierHotelOrder.orderDesc},
        #{virtualSupplierHotelOrder.orderAmount},
        #{virtualSupplierHotelOrder.orderUnit},
        #{virtualSupplierHotelOrder.orderSource},
        #{virtualSupplierHotelOrder.orderType},
        #{virtualSupplierHotelOrder.outOrderId},
        #{virtualSupplierHotelOrder.itemPrice},
        #{virtualSupplierHotelOrder.breakfastType},
        #{virtualSupplierHotelOrder.checkInDate},
        #{virtualSupplierHotelOrder.checkOutDate},
        #{virtualSupplierHotelOrder.stayDays},
        #{virtualSupplierHotelOrder.totalPrice},
        #{virtualSupplierHotelOrder.hotelName},
        #{virtualSupplierHotelOrder.roomName},
        #{virtualSupplierHotelOrder.totalOriginPrice},
        #{virtualSupplierHotelOrder.totalPriceDifference},
        #{virtualSupplierHotelOrder.roomCnt},
        #{virtualSupplierHotelOrder.totalFee},
        #{virtualSupplierHotelOrder.canceledReason},
        #{virtualSupplierHotelOrder.couponRealPrice},
        #{virtualSupplierHotelOrder.orderStatus},
        NOW(),
        #{virtualSupplierHotelOrder.updateTime}
        )
    </insert>


    <update id="update" parameterType="com.juzifenqi.virtual.bean.pojo.VirtualSupplierHotelOrder" >
        UPDATE virtual_supplier_hotel_order
        SET
        <if test="virtualSupplierHotelOrder.hotelOrderSn != null">`hotel_order_sn`= #{virtualSupplierHotelOrder.hotelOrderSn},</if>
        <if test="virtualSupplierHotelOrder.hotelOrderCreateTime != null">`hotel_order_create_time`= #{virtualSupplierHotelOrder.hotelOrderCreateTime},</if>
        <if test="virtualSupplierHotelOrder.supplierUserId != null">`supplier_user_id`= #{virtualSupplierHotelOrder.supplierUserId},</if>
        <if test="virtualSupplierHotelOrder.supplierUser != null">`supplier_user`= #{virtualSupplierHotelOrder.supplierUser},</if>
        <if test="virtualSupplierHotelOrder.supplierUserPhone != null">`supplier_user_phone`= #{virtualSupplierHotelOrder.supplierUserPhone},</if>
        <if test="virtualSupplierHotelOrder.couponId != null">`coupon_id`= #{virtualSupplierHotelOrder.couponId},</if>
        <if test="virtualSupplierHotelOrder.orderName != null">`order_name`= #{virtualSupplierHotelOrder.orderName},</if>
        <if test="virtualSupplierHotelOrder.orderDesc != null">`order_desc`= #{virtualSupplierHotelOrder.orderDesc},</if>
        <if test="virtualSupplierHotelOrder.orderAmount != null">`order_amount`= #{virtualSupplierHotelOrder.orderAmount},</if>
        <if test="virtualSupplierHotelOrder.orderUnit != null">`order_unit`= #{virtualSupplierHotelOrder.orderUnit},</if>
        <if test="virtualSupplierHotelOrder.orderSource != null">`order_source`= #{virtualSupplierHotelOrder.orderSource},</if>
        <if test="virtualSupplierHotelOrder.orderType != null">`order_type`= #{virtualSupplierHotelOrder.orderType},</if>
        <if test="virtualSupplierHotelOrder.outOrderId != null">`out_order_id`= #{virtualSupplierHotelOrder.outOrderId},</if>
        <if test="virtualSupplierHotelOrder.itemPrice != null">`item_price`= #{virtualSupplierHotelOrder.itemPrice},</if>
        <if test="virtualSupplierHotelOrder.breakfastType != null">`breakfast_type`= #{virtualSupplierHotelOrder.breakfastType},</if>
        <if test="virtualSupplierHotelOrder.checkInDate != null">`check_in_date`= #{virtualSupplierHotelOrder.checkInDate},</if>
        <if test="virtualSupplierHotelOrder.checkOutDate != null">`check_out_date`= #{virtualSupplierHotelOrder.checkOutDate},</if>
        <if test="virtualSupplierHotelOrder.stayDays != null">`stay_days`= #{virtualSupplierHotelOrder.stayDays},</if>
        <if test="virtualSupplierHotelOrder.totalPrice != null">`total_price`= #{virtualSupplierHotelOrder.totalPrice},</if>
        <if test="virtualSupplierHotelOrder.hotelName != null">`hotel_name`= #{virtualSupplierHotelOrder.hotelName},</if>
        <if test="virtualSupplierHotelOrder.roomName != null">`room_name`= #{virtualSupplierHotelOrder.roomName},</if>
        <if test="virtualSupplierHotelOrder.totalOriginPrice != null">`total_origin_price`= #{virtualSupplierHotelOrder.totalOriginPrice},</if>
        <if test="virtualSupplierHotelOrder.totalPriceDifference != null">`total_price_difference`= #{virtualSupplierHotelOrder.totalPriceDifference},</if>
        <if test="virtualSupplierHotelOrder.roomCnt != null">`room_cnt`= #{virtualSupplierHotelOrder.roomCnt},</if>
        <if test="virtualSupplierHotelOrder.totalFee != null">`total_fee`= #{virtualSupplierHotelOrder.totalFee},</if>
        <if test="virtualSupplierHotelOrder.canceledReason != null">`canceled_reason`= #{virtualSupplierHotelOrder.canceledReason},</if>
        <if test="virtualSupplierHotelOrder.couponRealPrice != null">`coupon_real_price`= #{virtualSupplierHotelOrder.couponRealPrice},</if>
        <if test="virtualSupplierHotelOrder.orderStatus != null">`order_status`= #{virtualSupplierHotelOrder.orderStatus},</if>
        update_time = now()
        WHERE `id` = #{virtualSupplierHotelOrder.id}
    </update>


    <select id="selectHotelOrderByOrderSn" resultMap="VirtualSupplierHotelOrder">
        select <include refid="Base_Column_List"/> from virtual_supplier_hotel_order
        where order_sn = #{orderSn} and order_status not in (2,6) order by id desc
    </select>

    <select id="selectHotelOrderByOrderSns" resultMap="VirtualSupplierHotelOrder">
        select <include refid="Base_Column_List"/> from virtual_supplier_hotel_order
        where order_sn =
        (
        <foreach collection="orderSns" item="orderSn" index="index" separator=",">
            #{orderSn}
        </foreach>
        )
        and order_status not in (2,6) order by id desc
    </select>

    <select id="selectValidHotelOrderByOrderSnAndCouponId" resultMap="VirtualSupplierHotelOrder">
        select <include refid="Base_Column_List"/>
          from virtual_supplier_hotel_order
         where order_sn = #{orderSn}
           And coupon_id = #{couponId}
           and order_status not in
        <foreach collection="notValidStatuses" item="status" index="index" open="(" separator="," close=")">
            #{status}
        </foreach>
        order by id desc limit 1
    </select>

    <select id="selectHotelOrderByCouponIds" resultMap="VirtualSupplierHotelOrder">
        select <include refid="Base_Column_List"/>
        from virtual_supplier_hotel_order
        where coupon_id in (
            <foreach collection="couponIds" item="couponId" index="index" separator=",">
                #{couponId}
            </foreach>
        )
        and order_status not in (2,6)
        order by id desc
    </select>
    <select id="selectHotelOrderBySn" resultMap="VirtualSupplierHotelOrder">
        select <include refid="Base_Column_List"/>
        from virtual_supplier_hotel_order
        where order_sn = #{orderSn}
        And hotel_order_sn = #{hotelOrderSn}
        order by id desc limit 1
    </select>
</mapper>
