<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.virtual.dao.VirtualGoodsDao">
    <resultMap id="VirtualGoodsMap" type="com.juzifenqi.virtual.bean.pojo.VirtualGoods">
        <result column="id" property="id"/>
        <result column="product_id" property="productId"/>
        <result column="product_name" property="productName"/>
        <result column="product_sku" property="productSku"/>
        <result column="supplier_id" property="supplierId"/>
        <result column="supplier_name" property="supplierName"/>
        <result column="supplier_item_id" property="supplierItemId"/>
        <result column="supplier_item_price" property="supplierItemPrice"/>
        <result column="stock" property="stock"/>
        <result column="onsale_state" property="onsaleState"/>
        <result column="private_price" property="privatePrice"/>
        <result column="recharge_type" property="rechargeType"/>
        <result column="recharge_type_name" property="rechargeTypeName"/>
        <result column="recharge_code" property="rechargeCode"/>
        <result column="recharge_name" property="rechargeName"/>
        <result column="supplier_access_type" property="supplierAccessType"/>
        <result column="supplier_access_url" property="supplierAccessUrl"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="img_url" property="imgUrl"/>
    </resultMap>
    <sql id="getConditionNew">
        <where>
            <if test="virtualGoodsBo.productName!=null and virtualGoodsBo.productName!=''">
                AND product_name LIKE CONCAT('%', #{virtualGoodsBo.productName}, '%')
            </if>
            <if test="virtualGoodsBo.productId!=null">
                AND product_id = #{virtualGoodsBo.productId}
            </if>
            <if test="virtualGoodsBo.productSku!=null and virtualGoodsBo.productSku!=''">
                AND product_sku = #{virtualGoodsBo.productSku}
            </if>
            <if test="virtualGoodsBo.supplierName!=null and virtualGoodsBo.supplierName!=''">
                AND supplier_name LIKE CONCAT('%', #{virtualGoodsBo.supplierName}, '%')
            </if>
            <if test="virtualGoodsBo.supplierItemId!=null and virtualGoodsBo.supplierItemId!=''">
                AND supplier_item_id = #{virtualGoodsBo.supplierItemId}
            </if>
            <if test="virtualGoodsBo.onsaleState!=null and virtualGoodsBo.onsaleState!=''">
                AND onsale_state = #{virtualGoodsBo.onsaleState}
            </if>
        </where>
    </sql>

    <insert id="saveVirtualGoods" parameterType="com.juzifenqi.virtual.bean.pojo.VirtualGoods">
        INSERT INTO virtual_goods (
        `product_id`,
        `product_name`,
        `product_sku`,
        `supplier_id`,
        `supplier_name`,
        `supplier_item_id`,
        `supplier_item_price`,
        `stock`,
        `onsale_state`,
        `private_price`,
        `recharge_type`,
        `recharge_type_name`,
        `recharge_code`,
        `recharge_name`,
        `supplier_access_type`,
        `supplier_access_url`,
        `create_by`,
        `create_time`,
        `img_url`
        )
        VALUES (
        #{productId},
        #{productName},
        #{productSku},
        #{supplierId},
        #{supplierName},
        #{supplierItemId},
        #{supplierItemPrice},
        #{stock},
        #{onsaleState},
        #{privatePrice},
        #{rechargeType},
        #{rechargeTypeName},
        #{rechargeCode},
        #{rechargeName},
        #{supplierAccessType},
        #{supplierAccessUrl},
        #{createBy},
        NOW(),
        #{imgUrl}
        )
    </insert>

    <select id="getVirtualGoodsById" parameterType="java.lang.Integer" resultMap="VirtualGoodsMap">
        SELECT * FROM virtual_goods
        WHERE id = #{id}
    </select>

    <delete id="deleteVirtualGoodsById" parameterType="java.lang.Integer">
        DELETE FROM virtual_goods
        WHERE id = #{id}
    </delete>

    <update id="updateVirtualGoodsById" parameterType="com.juzifenqi.virtual.bean.pojo.VirtualGoods">
        UPDATE virtual_goods
        SET
        <if test="productId != null">
            product_id = #{productId},
        </if>
        <if test="productName != null and productName !=''">
            product_name = #{productName},
        </if>
        <if test="productSku != null and productSku !=''">
            product_sku = #{productSku},
        </if>
        <if test="supplierId != null">
            supplier_id = #{supplierId},
        </if>
        <if test="supplierName != null and supplierName !=''">
            supplier_name = #{supplierName},
        </if>
        <if test="supplierItemId != null and supplierItemId !=''">
            supplier_item_id = #{supplierItemId},
        </if>
        <if test="supplierItemPrice != null">
            supplier_item_price = #{supplierItemPrice},
        </if>
        <if test="stock != null">
            stock = #{stock},
        </if>
        <if test="onsaleState != null">
            onsale_state = #{onsaleState},
        </if>
        <if test="privatePrice != null">
            private_price = #{privatePrice},
        </if>
        <if test="rechargeType != null">
            recharge_type = #{rechargeType},
        </if>
        <if test="rechargeTypeName != null">
            recharge_type_name = #{rechargeTypeName},
        </if>
        <if test="rechargeCode != null">
            recharge_code = #{rechargeCode},
        </if>
        <if test="rechargeName != null">
            recharge_name = #{rechargeName},
        </if>
        <if test="updateBy != null and updateBy!=''">
            update_by = #{updateBy},
        </if>
        <if test="supplierAccessType != null">
            supplier_access_type = #{supplierAccessType},
        </if>
        <if test="supplierAccessUrl != null and supplierAccessUrl != ''">
            supplier_access_url = #{supplierAccessUrl},
        </if>
        <if test="imgUrl != null and imgUrl != ''">
            img_url = #{imgUrl},
        </if>
        update_time = NOW()
        WHERE id = #{id}
    </update>

    <update id="updateOnsale">
        UPDATE virtual_goods
        SET onsale_state = #{onsaleState},
            update_by= #{updateBy},
            update_time = NOW()
        WHERE id = #{id}
    </update>

    <select id="getVirtualGoodsBySkus" parameterType="java.lang.String" resultMap="VirtualGoodsMap">
        SELECT * FROM virtual_goods
        WHERE product_sku in
        (
        <foreach collection="skus" item="sku" index="index" separator=",">
            #{sku}
        </foreach>
        )
    </select>

    <select id="getVirtualGoodsBySku" parameterType="java.lang.String" resultMap="VirtualGoodsMap">
        SELECT * FROM virtual_goods
        WHERE product_sku = #{sku}
    </select>

    <select id="pageListCount"
            parameterType="com.juzifenqi.virtual.bean.bo.VirtualGoodsBo"
            resultType="java.lang.Integer">
        select count(1)
        from virtual_goods
        <include refid="getConditionNew"/>
    </select>

    <select id="pageList" parameterType="com.juzifenqi.virtual.bean.bo.VirtualGoodsBo"
            resultMap="VirtualGoodsMap">
        SELECT * FROM virtual_goods
        <include refid="getConditionNew"/>
        ORDER BY create_time DESC
        <if test="size != null and size &gt; 0">limit #{start},#{size}</if>
    </select>

    <select id="getGoodsBySupplierItemId" resultMap="VirtualGoodsMap">
        SELECT * FROM virtual_goods
        WHERE supplier_item_id = #{productId}
    </select>

    <select id="getOnlineProduct"  resultMap="VirtualGoodsMap">
        SELECT * FROM virtual_goods
        WHERE onsale_state = 1 and supplier_id = #{supplierId}
    </select>
</mapper>