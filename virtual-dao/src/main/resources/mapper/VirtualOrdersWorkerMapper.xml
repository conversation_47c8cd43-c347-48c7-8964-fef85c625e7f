<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.virtual.dao.VirtualOrdersWorkerDao">
    <resultMap id="VirtualOrdersWorkerMap" type="com.juzifenqi.virtual.bean.pojo.VirtualOrdersWorker" >
        <result column="id" property="id" />
        <result column="order_sn" property="orderSn" />
        <result column="order_submit_status" property="orderSubmitStatus" />
        <result column="order_submit_time" property="orderSubmitTime" />
        <result column="order_recharge_status" property="orderRechargeStatus" />
        <result column="order_recharge_time" property="orderRechargeTime" />
        <result column="get_retry_count" property="getRetryCount" />
        <result column="remark" property="remark" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>
    
    <select id="getAllNeedPushOrders" resultMap="VirtualOrdersWorkerMap">
        SELECT * FROM virtual_orders_worker
        WHERE virtual_orders_worker.order_submit_status = 0
        and create_time > DATE_SUB(NOW(), INTERVAL 1 DAY)
        ORDER BY create_time ASC limit #{start},#{limit}
    </select>

    <select id="getAllNeedVerifyOrdersByTimeLimit" resultMap="VirtualOrdersWorkerMap">
        SELECT * FROM virtual_orders_worker
        WHERE virtual_orders_worker.order_submit_status = 1 AND
        virtual_orders_worker.order_recharge_status=0 AND
        virtual_orders_worker.order_submit_time <![CDATA[ <= ]]> #{virifyTime}
        and create_time > DATE_SUB(NOW(), INTERVAL 1 DAY)
        ORDER BY order_submit_time ASC limit #{start},#{limit}
    </select>

    <select id="getVirtualOrdersWorkerByOrderSn" parameterType="java.lang.String" resultMap="VirtualOrdersWorkerMap">
        SELECT * FROM virtual_orders_worker WHERE order_sn = #{orderSn}
    </select>

    <update id="updateVirtualOrdersWorkerById" parameterType="com.juzifenqi.virtual.bean.pojo.VirtualOrdersWorker">
        UPDATE virtual_orders_worker
        <set>
            <if test="null != orderSn and '' != orderSn">order_sn = #{orderSn},</if>
            <if test="null != orderSubmitStatus ">order_submit_status = #{orderSubmitStatus},</if>
            <if test="null != orderSubmitTime ">order_submit_time = #{orderSubmitTime},</if>
            <if test="null != orderRechargeStatus ">order_recharge_status = #{orderRechargeStatus},</if>
            <if test="null != orderRechargeTime ">order_recharge_time = #{orderRechargeTime},</if>
            <if test="null != getRetryCount ">get_retry_count = #{getRetryCount},</if>
            <if test="null != remark and '' != remark">remark = #{remark},</if>
            update_time = now()
        </set>
        WHERE id = #{id}
    </update>

    <update id="updateVirtualOrdersByOrderSn" parameterType="com.juzifenqi.virtual.bean.pojo.VirtualOrdersWorker">
        UPDATE virtual_orders_worker
        <set>
            <if test="null != orderSubmitStatus ">order_submit_status = #{orderSubmitStatus},</if>
            <if test="null != orderSubmitTime ">order_submit_time = #{orderSubmitTime},</if>
            <if test="null != orderRechargeStatus ">order_recharge_status = #{orderRechargeStatus},</if>
            <if test="null != orderRechargeTime ">order_recharge_time = #{orderRechargeTime},</if>
            <if test="null != getRetryCount ">get_retry_count = #{getRetryCount},</if>
            <if test="null != remark and '' != remark">remark = #{remark},</if>
            update_time = now()
        </set>
        WHERE order_sn = #{orderSn}
    </update>

    <insert id="saveVirtualOrdersWorker" useGeneratedKeys="true" keyColumn="id" keyProperty="id" parameterType="com.juzifenqi.virtual.bean.pojo.VirtualOrdersWorker">
        INSERT INTO virtual_orders_worker
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != orderSn and '' != orderSn">
                order_sn,
            </if>
            <if test="null != orderSubmitStatus ">
                order_submit_status,
            </if>
            <if test="null != orderSubmitTime ">
                order_submit_time,
            </if>
            <if test="null != orderRechargeStatus ">
                order_recharge_status,
            </if>
            <if test="null != orderRechargeTime ">
                order_recharge_time,
            </if>
            <if test="null != getRetryCount ">
                get_retry_count,
            </if>
            <if test="null != remark and '' != remark">
                remark,
            </if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != orderSn and '' != orderSn">
                #{orderSn},
            </if>
            <if test="null != orderSubmitStatus ">
                #{orderSubmitStatus},
            </if>
            <if test="null != orderSubmitTime ">
                #{orderSubmitTime},
            </if>
            <if test="null != orderRechargeStatus ">
                #{orderRechargeStatus},
            </if>
            <if test="null != orderRechargeTime ">
                #{orderRechargeTime},
            </if>
            <if test="null != getRetryCount ">
                #{getRetryCount},
            </if>
            <if test="null != remark and '' != remark">
                #{remark},
            </if>
            now()
        </trim>
    </insert>




</mapper>