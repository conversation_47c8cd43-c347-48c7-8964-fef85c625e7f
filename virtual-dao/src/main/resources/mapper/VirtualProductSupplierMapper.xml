<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.virtual.dao.VirtualProductSupplierDao">

    <resultMap id="VirtualProductSupplierMap" type="com.juzifenqi.virtual.bean.pojo.VirtualProductSupplier" >
        <result column="id" property="id" />
        <result column="supplier_id" property="supplierId" />
        <result column="supplier_name" property="supplierName" />
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <select id="getAllVirtualProductSupplier" resultMap="VirtualProductSupplierMap">
        SELECT * FROM virtual_product_supplier
    </select>
</mapper>