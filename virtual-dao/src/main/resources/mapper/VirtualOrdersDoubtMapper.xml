<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.virtual.dao.VirtualOrdersDoubtDao">
    <resultMap id="VirtualOrdersDoubtMap" type="com.juzifenqi.virtual.bean.pojo.VirtualOrdersDoubt">
        <result column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="channel_id" property="channelId"/>
        <result column="order_sn" property="orderSn"/>
        <result column="reason" property="reason"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <insert id="saveVirtualOrdersDoubt" useGeneratedKeys="true" keyColumn="id" keyProperty="id"
            parameterType="com.juzifenqi.virtual.bean.pojo.VirtualOrdersDoubt">
        INSERT INTO virtual_orders_doubt
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != userId ">
                user_id,
            </if>
            <if test="null != channelId ">
                channel_id,
            </if>
            <if test="null != orderSn and '' != orderSn">
                order_sn,
            </if>
            <if test="null != reason and '' != reason">
                reason,
            </if>
            <if test="null != createTime ">
                create_time
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != userId ">
                #{userId},
            </if>
            <if test="null != channelId ">
                #{channelId},
            </if>
            <if test="null != orderSn and '' != orderSn">
                #{orderSn},
            </if>
            <if test="null != reason and '' != reason">
                #{reason},
            </if>
            <if test="null != createTime ">
                NOW()
            </if>
        </trim>
    </insert>

</mapper>
