<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.virtual.dao.VirtualOrdersCardDao">
    <resultMap id="VirtualOrdersCardMap" type="com.juzifenqi.virtual.bean.pojo.VirtualOrdersCard">
        <result column="id" property="id"/>
        <result column="order_sn" property="orderSn"/>
        <result column="card_state" property="cardState"/>
        <result column="retry_num" property="retryNum"/>
        <result column="card_type" property="cardType"/>
        <result column="card_no" property="cardNo"/>
        <result column="card_pwd" property="cardPwd"/>
        <result column="card_deadline" property="cardDeadline"/>
        <result column="card_link" property="cardLink"/>
        <result column="card_qrcode" property="cardQrcode"/>
        <result column="operator_serial_number" property="operatorSerialNumber"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`
        ,
        `order_sn`,
        `card_state`,
        `retry_num`,
        `card_type`,
        `card_no`,
        `card_pwd`,
        `card_deadline`,
        `card_link`,
        `card_qrcode`,
        `operator_serial_number`,
        `create_time`,
        `update_time`
    </sql>

    <insert id="insertVirtualOrdersCard"
            parameterType="com.juzifenqi.virtual.bean.pojo.VirtualOrdersCard">
        INSERT INTO virtual_orders_card (`order_sn`,
                                         `card_state`,
                                         `retry_num`,
                                         `card_type`,
                                         `card_no`,
                                         `card_pwd`,
                                         `card_deadline`,
                                         `card_link`,
                                         `card_qrcode`,
                                         `operator_serial_number`,
                                         `create_time`,
                                         `update_time`)
        VALUES (#{virtualOrdersCard.orderSn},
                #{virtualOrdersCard.cardState},
                #{virtualOrdersCard.retryNum},
                #{virtualOrdersCard.cardType},
                #{virtualOrdersCard.cardNo},
                #{virtualOrdersCard.cardPwd},
                #{virtualOrdersCard.cardDeadline},
                #{virtualOrdersCard.cardLink},
                #{virtualOrdersCard.cardQrcode},
                #{virtualOrdersCard.operatorSerialNumber},
                NOW(),
                NOW())
    </insert>

    <select id="selectByOrderSn" resultMap="VirtualOrdersCardMap">
        select
        <include refid="Base_Column_List"/>
        from virtual_orders_card
        where order_sn = #{orderSn} limit 1
    </select>

    <select id="getAllNeedDecodeCardsByTimeLimit" resultMap="VirtualOrdersCardMap">
        SELECT <include refid="Base_Column_List"/>
        FROM virtual_orders_card AS c
        WHERE c.card_state = #{allowState}
          AND c.retry_num <![CDATA[ < ]]> 5
          AND c.create_time > DATE_SUB(NOW(), INTERVAL 1 DAY)
        ORDER BY c.create_time ASC limit #{start}, #{limit}
    </select>

    <update id="batchUpdateCardsStateByIds" parameterType="map">
        UPDATE virtual_orders_card
        SET card_state = #{state}
        WHERE id IN
        (
        <foreach collection="ids" item="id" index="index" separator=",">
            #{id}
        </foreach>
        ) AND card_state = #{allowState}
    </update>

    <update id="updateVirtualOrdersCardFailById" parameterType="map">
        UPDATE virtual_orders_card
        SET card_state  = #{state},
            retry_num   = retry_num + 1,
            update_time = NOW()
        WHERE id = #{id}
    </update>

    <update id="updateVirtualOrdersCardById"
            parameterType="com.juzifenqi.virtual.bean.pojo.VirtualOrdersCard">
        UPDATE virtual_orders_card
        <set>
            <if test="null != cardType ">card_type = #{cardType},</if>
            <if test="null != cardNo and '' != cardNo">card_no = #{cardNo},</if>
            <if test="null != cardPwd and '' != cardPwd">card_pwd = #{cardPwd},</if>
            <if test="null != cardLink and '' != cardLink">card_link = #{cardLink},</if>
            <if test="null != cardQrcode and '' != cardQrcode">card_qrcode = #{cardQrcode},</if>
            <if test="null != cardDeadline ">card_deadline = #{cardDeadline},</if>
            <if test="null != operatorSerialNumber and '' != operatorSerialNumber">
                operator_serial_number = #{operatorSerialNumber},
            </if>
            card_state = #{cardState},
            update_time = NOW()
        </set>
        WHERE id = #{id}
    </update>

</mapper>