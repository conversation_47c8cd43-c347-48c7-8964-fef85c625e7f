<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.virtual.dao.VirtualOrderRefundInfoDao">

    <resultMap id="PlusOrderRefundInfo" type="com.juzifenqi.virtual.bean.pojo.VirtualOrderRefundInfo" >
        <result column="id" property="id" />
        <result column="order_sn" property="orderSn" />
        <result column="refund_serial_no" property="refundSerialNo" />
        <result column="pay_serial_no" property="paySerialNo" />
        <result column="refund_state" property="refundState" />
        <result column="remark" property="remark" />
        <result column="pay_callback_time" property="payCallbackTime"/>
        <result column="opt_user_id" property="optUserId" />
        <result column="opt_user_name" property="optUserName" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `order_sn`,
        `refund_serial_no`,
        `pay_serial_no`,
        `refund_state`,
        `remark`,
        `pay_callback_time`,
        `opt_user_id`,
        `opt_user_name`,
        `create_time`,
        `update_time`
    </sql>

    <insert id="save" parameterType="com.juzifenqi.virtual.bean.pojo.VirtualOrderRefundInfo" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO virtual_order_refund_info (`order_sn`,
                                            `refund_serial_no`,
                                            `pay_serial_no`,
                                            `refund_state`,
                                            `remark`,
                                            `pay_callback_time`,
                                            `opt_user_id`,
                                            `opt_user_name`,
                                            `create_time`,
                                            `update_time`)
        VALUES (#{orderSn},
                #{refundSerialNo},
                #{paySerialNo},
                #{refundState},
                #{remark},
                #{payCallbackTime},
                #{optUserId},
                #{optUserName},
                NOW(),
                #{updateTime})
    </insert>

    <update id="updateById">
        UPDATE virtual_order_refund_info
        SET
        <if test="refundState != null">
            `refund_state`= #{refundState},
        </if>
        <if test="paySerialNo != null">
            `pay_serial_no`= #{paySerialNo},
        </if>
        <if test="payCallbackTime != null">
            `pay_callback_time`= #{payCallbackTime},
        </if>
        update_time = now()
        WHERE `id` = #{id}
    </update>

    <select id="getByRefundSerialNo"
            resultMap="PlusOrderRefundInfo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM virtual_order_refund_info
        WHERE `refund_serial_no` = #{refundSerialNo}
    </select>

    <update id="updateRefundState">
        UPDATE virtual_order_refund_info
        SET `refund_state`= #{refundState},
            update_time   = now()
        WHERE refund_serial_no = #{refundSerialNo}
    </update>

    <update id="updatePaySerialNoById">
        UPDATE virtual_order_refund_info
        SET `pay_serial_no`= #{paySerialNo},
            update_time   = now()
        WHERE `id` = #{id}
    </update>

</mapper>
