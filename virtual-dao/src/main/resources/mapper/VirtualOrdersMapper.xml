<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.virtual.dao.VirtualOrdersDao">
    <resultMap id="VirtualOrdersMap" type="com.juzifenqi.virtual.bean.pojo.VirtualOrders">
        <result column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="channel_id" property="channelId"/>
        <result column="mobile" property="mobile"/>
        <result column="recharge_account" property="rechargeAccount"/>
        <result column="recharge_account_uuid" property="rechargeAccountUuid"/>
        <result column="recharge_type" property="rechargeType"/>
        <result column="recharge_type_name" property="rechargeTypeName"/>
        <result column="recharge_code" property="rechargeCode"/>
        <result column="recharge_name" property="rechargeName"/>
        <result column="order_sn" property="orderSn"/>
        <result column="plus_order_sn" property="plusOrderSn"/>
        <result column="program_id" property="programId"/>
        <result column="program_name" property="programName"/>
        <result column="product_sku" property="productSku"/>
        <result column="product_id" property="productId"/>
        <result column="product_name" property="productName"/>
        <result column="supplier_order_sn" property="supplierOrderSn"/>
        <result column="supplier_name" property="supplierName"/>
        <result column="supplier_item_id" property="supplierItemId"/>
        <result column="supplier_id" property="supplierId"/>
        <result column="order_money" property="orderMoney"/>
        <result column="supplier_money" property="supplierMoney"/>
        <result column="order_status" property="orderStatus"/>
        <result column="pay_status" property="payStatus"/>
        <result column="recharge_status" property="rechargeStatus"/>
        <result column="profit_type_id" property="profitTypeId"/>
        <result column="remark" property="remark"/>
        <result column="model_id" property="modelId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="supplier_access_type" property="supplierAccessType"/>
        <result column="supplier_access_url" property="supplierAccessUrl"/>
    </resultMap>

    <sql id="getCondition">
        <where>
            <if test="virtualOrdersBo.userId != null">
                AND user_id = #{virtualOrdersBo.userId}
            </if>
            <if test="virtualOrdersBo.channelId != null">
                AND channel_id = #{virtualOrdersBo.channelId}
            </if>
            <if test="virtualOrdersBo.rechargeAccount != null and virtualOrdersBo.rechargeAccount != ''">
                AND recharge_account = #{virtualOrdersBo.rechargeAccount}
            </if>
            <if test="virtualOrdersBo.orderSn != null and virtualOrdersBo.orderSn != ''">
                AND order_sn = #{virtualOrdersBo.orderSn}
            </if>
            <if test="virtualOrdersBo.productSku != null and virtualOrdersBo.productSku != ''">
                AND product_sku = #{virtualOrdersBo.productSku}
            </if>
            <if test="virtualOrdersBo.orderStatus != null">
                AND order_status = #{virtualOrdersBo.orderStatus}
            </if>
            <if test="virtualOrdersBo.plusOrderSn != null and virtualOrdersBo.plusOrderSn != ''">
                AND plus_order_sn = #{virtualOrdersBo.plusOrderSn}
            </if>
            <if test="virtualOrdersBo.productName != null and virtualOrdersBo.productName != ''">
                AND product_name LIKE CONCAT('%', #{virtualOrdersBo.productName}, '%')
            </if>
            <if test="virtualOrdersBo.productId != null">
                AND product_id = #{virtualOrdersBo.productId}
            </if>
            <if test="virtualOrdersBo.supplierId != null">
                AND supplier_id = #{virtualOrdersBo.supplierId}
            </if>
            <if test="virtualOrdersBo.supplierOrderSn != null and virtualOrdersBo.supplierOrderSn != ''">
                AND supplier_order_sn LIKE CONCAT('%', #{virtualOrdersBo.supplierOrderSn}, '%')
            </if>
        </where>
    </sql>
    <select id="getVirtualOrdersByOrderSn" parameterType="java.lang.String"
            resultMap="VirtualOrdersMap">
        SELECT * FROM virtual_orders WHERE order_sn = #{orderSn}
    </select>

    <update id="updateVirtualOrdersById"
            parameterType="com.juzifenqi.virtual.bean.pojo.VirtualOrders">
        UPDATE virtual_orders
        <set>
            <if test="null != userId ">user_id = #{userId},</if>
            <if test="null != channelId ">channel_id = #{channelId},</if>
            <if test="null != rechargeAccount and '' != rechargeAccount">recharge_account =
                #{rechargeAccount},
            </if>
            <if test="null != rechargeAccountUuid and '' != rechargeAccountUuid">recharge_account_uuid =
                #{rechargeAccountUuid},
            </if>
            <if test="null != rechargeCode">recharge_code = #{rechargeCode},</if>
            <if test="null != rechargeName and '' != rechargeName">recharge_name =
                #{rechargeName},
            </if>
            <if test="null != orderSn and '' != orderSn">order_sn = #{orderSn},</if>
            <if test="null != plusOrderSn and '' != plusOrderSn">plus_order_sn = #{plusOrderSn},
            </if>
            <if test="null != programId ">program_id = #{programId},</if>
            <if test="null != programName and '' != programName">program_name = #{programName},</if>
            <if test="null != productSku and '' != productSku">product_sku = #{productSku},</if>
            <if test="null != productId ">product_id = #{productId},</if>
            <if test="null != productName and '' != productName">product_name = #{productName},</if>
            <if test="null != supplierOrderSn and '' != supplierOrderSn">supplier_order_sn =
                #{supplierOrderSn},
            </if>
            <if test="null != supplierName and '' != supplierName">supplier_name =
                #{supplierName},
            </if>
            <if test="null != supplierItemId and '' != supplierItemId">supplier_item_id =
                #{supplierItemId},
            </if>
            <if test="null != supplierId ">supplier_id = #{supplierId},</if>
            <if test="null != orderMoney ">order_money = #{orderMoney},</if>
            <if test="null != supplierMoney ">supplier_money = #{supplierMoney},</if>
            <if test="null != orderStatus ">order_status = #{orderStatus},</if>
            <if test="null != payStatus ">pay_status = #{payStatus},</if>
            <if test="null != rechargeStatus ">recharge_status = #{rechargeStatus},</if>
            <if test="null != profitTypeId ">profit_type_id = #{profitTypeId},</if>
            <if test="null != remark and '' != remark">remark = #{remark},</if>
            update_time = now()
        </set>
        WHERE id = #{id}
    </update>

    <select id="countUesdOrdersByOrderSn" parameterType="map"
            resultType="java.lang.Integer">
        SELECT count(*) FROM virtual_orders WHERE plus_order_sn = #{orderSn} and order_status in
        (1,2,4,5,7)
        <if test="modelId!=null">
            and model_id = #{modelId}
        </if>
    </select>

    <sql id="simple_column">
        `id`,
        `user_id`,
        `order_sn`,
        `plus_order_sn`,
        `product_sku`,
        `product_id`,
        `product_name`,
        `order_money`,
        `recharge_account`,
        `recharge_account_uuid`,
        `profit_type_id`,
        `order_status`,
        `create_time`,
        `recharge_type`,
        `recharge_type_name`,
        `pay_status`,
        `recharge_status`,
        `supplier_access_type`,
        `supplier_access_url`,
        `supplier_id`,
        `supplier_name`,
        `channel_id`,
        `model_id`
    </sql>

    <select id="countOrderByPlus" parameterType="map" resultMap="VirtualOrdersMap">
        SELECT
        <include refid="simple_column"/>
        FROM virtual_orders
        WHERE
        plus_order_sn = #{plusOrderSn}
        and order_status in (1,2,4,5,7)
        <if test="modelId!=null">
            and model_id = #{modelId}
        </if>
    </select>


    <!--（待充值下单，下单成功，待充值查证）-->
    <select id="countProcessingOrder" parameterType="map"
            resultType="java.lang.Integer">
        SELECT
        count(*)
        FROM virtual_orders
        WHERE
        plus_order_sn = #{plusOrderSn}
        and order_status in (1,2,4)
        <if test="modelId!=null">
            and model_id = #{modelId}
        </if>
    </select>


    <select id="selectProcessingOrder" parameterType="map"
            resultMap="VirtualOrdersMap">
        SELECT
        <include refid="simple_column"/>
        FROM virtual_orders
        WHERE
        plus_order_sn = #{plusOrderSn}
        and order_status in (1,2,4)
        <if test="modelId!=null">
            and model_id = #{modelId}
        </if>
    </select>

    <select id="getNeedPayOrder" parameterType="map" resultMap="VirtualOrdersMap">
        SELECT
        order_sn
        FROM virtual_orders
        WHERE
        plus_order_sn = #{plusOrderSn}
        and product_sku = #{productSku}
        and order_status = 0
        <if test="modelId!=null">
            and model_id = #{modelId}
        </if>
        order by id desc
    </select>

    <insert id="saveVirtualOrders" useGeneratedKeys="true" keyColumn="id" keyProperty="id"
            parameterType="com.juzifenqi.virtual.bean.pojo.VirtualOrders">
        INSERT INTO virtual_orders
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != userId ">
                user_id,
            </if>
            <if test="null != channelId ">
                channel_id,
            </if>
            <if test="null != rechargeAccount and '' != rechargeAccount">
                recharge_account,
            </if>
            <if test="null != rechargeAccountUuid and '' != rechargeAccountUuid">
                recharge_account_uuid,
            </if>
            <if test="null != rechargeType">
                recharge_type,
            </if>
            <if test="null != rechargeTypeName and '' != rechargeTypeName">
                recharge_type_name,
            </if>
            <if test="null != rechargeCode">
                recharge_code,
            </if>
            <if test="null != rechargeName and '' != rechargeName">
                recharge_name,
            </if>
            <if test="null != orderSn and '' != orderSn">
                order_sn,
            </if>
            <if test="null != plusOrderSn and '' != plusOrderSn">
                plus_order_sn,
            </if>
            <if test="null != programId ">
                program_id,
            </if>
            <if test="null != programName and '' != programName">
                program_name,
            </if>
            <if test="null != productSku and '' != productSku">
                product_sku,
            </if>
            <if test="null != productId ">
                product_id,
            </if>
            <if test="null != productName and '' != productName">
                product_name,
            </if>
            <if test="null != supplierOrderSn and '' != supplierOrderSn">
                supplier_order_sn,
            </if>
            <if test="null != supplierName and '' != supplierName">
                supplier_name,
            </if>
            <if test="null != supplierItemId and '' != supplierItemId">
                supplier_item_id,
            </if>
            <if test="null != supplierId ">
                supplier_id,
            </if>
            <if test="null != orderMoney ">
                order_money,
            </if>
            <if test="null != supplierMoney ">
                supplier_money,
            </if>
            <if test="null != orderStatus ">
                order_status,
            </if>
            <if test="null != payStatus ">
                pay_status,
            </if>
            <if test="null != rechargeStatus ">
                recharge_status,
            </if>
            <if test="null != profitTypeId ">
                profit_type_id,
            </if>
            <if test="null != modelId ">
                model_id,
            </if>
            <if test="null != remark and '' != remark">
                remark,
            </if>
            <if test="supplierAccessType != null">
                supplier_access_type,
            </if>
            <if test="supplierAccessUrl != null and supplierAccessUrl != ''">
                supplier_access_url,
            </if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != userId ">
                #{userId},
            </if>
            <if test="null != channelId ">
                #{channelId},
            </if>
            <if test="null != rechargeAccount and '' != rechargeAccount">
                #{rechargeAccount},
            </if>
            <if test="null != rechargeAccountUuid and '' != rechargeAccountUuid">
                #{rechargeAccountUuid},
            </if>
            <if test="null != rechargeType">
                #{rechargeType},
            </if>
            <if test="null != rechargeTypeName and '' != rechargeTypeName">
                #{rechargeTypeName},
            </if>
            <if test="null != rechargeCode">
                #{rechargeCode},
            </if>
            <if test="null != rechargeName and '' != rechargeName">
                #{rechargeName},
            </if>
            <if test="null != orderSn and '' != orderSn">
                #{orderSn},
            </if>
            <if test="null != plusOrderSn and '' != plusOrderSn">
                #{plusOrderSn},
            </if>
            <if test="null != programId ">
                #{programId},
            </if>
            <if test="null != programName and '' != programName">
                #{programName},
            </if>
            <if test="null != productSku and '' != productSku">
                #{productSku},
            </if>
            <if test="null != productId ">
                #{productId},
            </if>
            <if test="null != productName and '' != productName">
                #{productName},
            </if>
            <if test="null != supplierOrderSn and '' != supplierOrderSn">
                #{supplierOrderSn},
            </if>
            <if test="null != supplierName and '' != supplierName">
                #{supplierName},
            </if>
            <if test="null != supplierItemId and '' != supplierItemId">
                #{supplierItemId},
            </if>
            <if test="null != supplierId ">
                #{supplierId},
            </if>
            <if test="null != orderMoney ">
                #{orderMoney},
            </if>
            <if test="null != supplierMoney ">
                #{supplierMoney},
            </if>
            <if test="null != orderStatus ">
                #{orderStatus},
            </if>
            <if test="null != payStatus ">
                #{payStatus},
            </if>
            <if test="null != rechargeStatus ">
                #{rechargeStatus},
            </if>
            <if test="null != profitTypeId ">
                #{profitTypeId},
            </if>
            <if test="null != modelId ">
                #{modelId},
            </if>
            <if test="null != remark and '' != remark">
                #{remark},
            </if>
            <if test="supplierAccessType != null">
                #{supplierAccessType},
            </if>
            <if test="supplierAccessUrl != null and supplierAccessUrl != ''">
                #{supplierAccessUrl},
            </if>
            NOW()
        </trim>
    </insert>

    <update id="updateOrderStatusByOrderSn">
        UPDATE virtual_orders
        SET order_status = #{orderStatus},
        remark = #{remark}
        WHERE order_sn = #{orderSn}
    </update>

    <select id="getVirtualOrdersListByPage"
            parameterType="com.juzifenqi.virtual.bean.bo.VirtualOrdersBo"
            resultMap="VirtualOrdersMap">
        SELECT * FROM virtual_orders
        <include refid="getCondition"/>
        ORDER BY create_time DESC
        <if test="size != null and size &gt; 0">limit #{start},#{size}</if>
    </select>

    <select id="getVirtualOrdersList"
            parameterType="com.juzifenqi.virtual.bean.bo.VirtualOrdersBo"
            resultMap="VirtualOrdersMap">
        SELECT * FROM virtual_orders
        WHERE plus_order_sn = #{plusOrderSn} and user_id = #{userId} and order_status in (1,2,4,5,7) and model_id = #{modelId}
    </select>

    <select id="pageListCount"
            parameterType="com.juzifenqi.virtual.bean.bo.VirtualOrdersBo"
            resultType="java.lang.Integer">
        select count(1)
        from virtual_orders
        <include refid="getCondition"/>
    </select>

    <select id="getOrdersListByUser"
            parameterType="com.juzifenqi.virtual.bean.bo.VirtualOrdersBo"
            resultMap="VirtualOrdersMap">
        SELECT recharge_account,recharge_account_uuid,recharge_type,recharge_type_name,recharge_code,recharge_name,
        order_sn,product_name,order_money,recharge_status,create_time,order_status FROM virtual_orders
        WHERE user_id = #{userId}
        <if test="payStatus != null">
         and pay_status in   (
            <foreach collection="payStatus" item="state" index="index" separator=",">
                #{state}
            </foreach>
            )
        </if>
        order by id desc
        <if test="pageSize != null and pageSize &gt; 0">limit #{startPage},#{pageSize}</if>
    </select>


    <select id="countOrdersListByUser"
            parameterType="com.juzifenqi.virtual.bean.bo.VirtualOrdersBo"
            resultType="integer">
        SELECT count(*) FROM virtual_orders
        WHERE user_id = #{userId}
        <if test="payStatus != null">
            and pay_status in (
            <foreach collection="payStatus" item="state" index="index" separator=",">
                #{state}
            </foreach>
            )
        </if>
    </select>


    <update id="updateOrderByOrderSn"
            parameterType="com.juzifenqi.virtual.bean.pojo.VirtualOrders">
        UPDATE virtual_orders
        <set>
            <if test="null != orderStatus ">order_status = #{orderStatus},</if>
            <if test="null != payStatus ">pay_status = #{payStatus},</if>
            <if test="null != rechargeStatus ">recharge_status = #{rechargeStatus},</if>
            <if test="null != remark and '' != remark">remark = #{remark},</if>
            update_time = now()
        </set>
        WHERE order_sn = #{orderSn}
    </update>
    <update id="updateBatchById">
        <foreach collection="list" item="item" separator=";">
            update
            `virtual_orders`
            set
            `recharge_account_uuid` = #{item.rechargeAccountUuid},
            `update_time` = now()
            where
            `id` = #{item.id}
        </foreach>
    </update>

    <select id="getByOrderSn"  resultMap="VirtualOrdersMap">
        SELECT <include refid="simple_column"/> FROM virtual_orders
        WHERE order_sn = #{orderSn}
    </select>

    <select id="getRechargeSuccessOrders"  resultMap="VirtualOrdersMap">
        SELECT <include refid="simple_column"/>
          FROM virtual_orders
         WHERE plus_order_sn = #{plusOrderSn}
           AND recharge_status = #{rechargeStatus}
           AND pay_status = #{payStatus}
           AND supplier_id IN
        <foreach collection="supplierIds" item="supplierId" index="index" open="(" separator="," close=")">
            #{supplierId}
        </foreach>
           AND recharge_type IN
        <foreach collection="rechargeTypes" item="rechargeType" index="index" open="(" separator="," close=")">
            #{rechargeType}
        </foreach>
    </select>

    <select id="getAllVirtualOrdersList"
            parameterType="com.juzifenqi.virtual.bean.bo.VirtualOrdersBo"
            resultMap="VirtualOrdersMap">
        SELECT * FROM virtual_orders
        WHERE plus_order_sn = #{plusOrderSn} and user_id = #{userId} and model_id = #{modelId}
    </select>
    <select id="getWithEmptyUuidVirtualOrders" resultType="com.juzifenqi.virtual.bean.pojo.VirtualOrders">
        SELECT id, recharge_account
        FROM virtual_orders
        WHERE recharge_code = 1
        and recharge_type =1
        AND recharge_account is not null
        AND recharge_account_uuid is null
        and id > #{index}
        ORDER BY id ASC
            LIMIT #{batchSize}
    </select>
    <select id="getWithUuidVirtualOrders" resultType="com.juzifenqi.virtual.bean.pojo.VirtualOrders">
        SELECT id, recharge_account, recharge_account_uuid
        FROM virtual_orders
        WHERE recharge_code = 1
          and recharge_type =1
          AND recharge_account is not null
          AND recharge_account_uuid is not null
          and id > #{index}
        ORDER BY id ASC
            LIMIT #{batchSize}
    </select>
    <select id="getVirtualOrdersById" resultType="com.juzifenqi.virtual.bean.pojo.VirtualOrders">
        SELECT id, recharge_account, recharge_account_uuid
        FROM virtual_orders
        WHERE id = #{id}
    </select>

</mapper>