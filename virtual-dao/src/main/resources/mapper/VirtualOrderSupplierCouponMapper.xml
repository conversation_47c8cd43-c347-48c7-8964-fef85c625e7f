<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.virtual.dao.VirtualOrderSupplierCouponDao">

    <resultMap id="VirtualOrderSupplierCouponMap" type="com.juzifenqi.virtual.bean.pojo.VirtualOrderSupplierCoupon" >
        <result column="id" property="id" />
        <result column="order_sn" property="orderSn" />
        <result column="supplier_id" property="supplierId" />
        <result column="supplier_name" property="supplierName" />
        <result column="batch_id" property="batchId" />
        <result column="coupon_id" property="couponId" />
        <result column="use_state" property="useState" />
        <result column="expiration_time" property="expirationTime" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `order_sn`,
        `supplier_id`,
        `supplier_name`,
        `batch_id`,
        `coupon_id`,
        `use_state`,
        `expiration_time`,
        `create_time`,
        `update_time`
    </sql>

    <insert id="saveVirtualOrderSupplierCoupon" parameterType="com.juzifenqi.virtual.bean.pojo.VirtualOrderSupplierCoupon" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO virtual_order_supplier_coupon (
            `order_sn`,
            `supplier_id`,
            `supplier_name`,
            `batch_id`,
            `coupon_id`,
            `use_state`,
            `expiration_time`,
            `create_time`,
            `update_time`
        )
        VALUES(
                  #{orderSn},
                  #{supplierId},
                  #{supplierName},
                  #{batchId},
                  #{couponId},
                  #{useState},
                  #{expirationTime},
                  NOW(),
                  #{updateTime}
              )
    </insert>

    <update id="updateVirtualOrderSupplierCouponById"
            parameterType="com.juzifenqi.virtual.bean.pojo.VirtualOrderSupplierCoupon">
        UPDATE virtual_order_supplier_coupon
        <set>
            <if test="null != couponId and '' != couponId">coupon_id = #{couponId},</if>
            <if test="null != useState">use_state = #{useState},</if>
            update_time = NOW()
        </set>
        WHERE id = #{id}
    </update>


    <select id="selectByOrderSn" resultMap="VirtualOrderSupplierCouponMap">
        select
        <include refid="Base_Column_List"/>
        from virtual_order_supplier_coupon
        where order_sn = #{orderSn} limit 1
    </select>


    <select id="selectByOrderSns" resultMap="VirtualOrderSupplierCouponMap">
        select
        <include refid="Base_Column_List"/>
        from virtual_order_supplier_coupon
        where order_sn in
        (
        <foreach collection="orderSns" item="orderSn" index="index" separator=",">
            #{orderSn}
        </foreach>
        )
        and use_state  = 1
    </select>
</mapper>
