<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.virtual.dao.VirtualRechargeTypeDao">

    <resultMap id="VirtualRechargeType" type="com.juzifenqi.virtual.bean.pojo.VirtualRechargeType">
        <result column="id" property="id"/>
        <result column="recharge_code" property="rechargeCode"/>
        <result column="recharge_name" property="rechargeName"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `recharge_code`,
        `recharge_name`,
        `create_by`,
        `create_time`,
        `update_by`,
        `update_time`
    </sql>

    <insert id="saveVirtualRechargeType"
            parameterType="com.juzifenqi.virtual.bean.pojo.VirtualRechargeType"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO virtual_recharge_type (
        `recharge_code`,
        `recharge_name`,
        `create_by`,
        `create_time`
        )
        VALUES(
        #{rechargeCode},
        #{rechargeName},
        #{createBy},
        NOW()
        )
    </insert>

    <delete id="deleteVirtualRechargeType" parameterType="java.lang.Integer">
        DELETE FROM virtual_recharge_type
        WHERE `id` = #{id}
    </delete>

    <update id="updateVirtualRechargeType"
            parameterType="com.juzifenqi.virtual.bean.pojo.VirtualRechargeType">
        UPDATE virtual_recharge_type
        SET
        <if test="virtualRechargeType.rechargeCode != null">`recharge_code`=
            #{virtualRechargeType.rechargeCode},
        </if>
        <if test="virtualRechargeType.rechargeName != null">`recharge_name`=
            #{virtualRechargeType.rechargeName},
        </if>
        <if test="virtualRechargeType.createBy != null">`create_by`=
            #{virtualRechargeType.createBy},
        </if>
        <if test="virtualRechargeType.updateBy != null">`update_by`=
            #{virtualRechargeType.updateBy},
        </if>
        update_time = now()
        WHERE `id` = #{virtualRechargeType.id}
    </update>


    <select id="loadVirtualRechargeType" parameterType="java.lang.Integer"
            resultMap="VirtualRechargeType">
        SELECT
        <include refid="Base_Column_List"/>
        FROM virtual_recharge_type
        WHERE `id` = #{id}
    </select>

    <select id="getAllVirtualRechargeType" resultMap="VirtualRechargeType">
        SELECT
        <include refid="Base_Column_List"/>
        FROM virtual_recharge_type
        LIMIT 100;
    </select>

</mapper>
