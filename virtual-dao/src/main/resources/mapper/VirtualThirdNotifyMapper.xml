<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.virtual.dao.VirtualThirdNotifyDao">

    <resultMap id="VirtualThirdNotify" type="com.juzifenqi.virtual.bean.pojo.VirtualThirdNotify">
        <result column="id" property="id"/>
        <result column="order_sn" property="orderSn"/>
        <result column="supplier_order_sn" property="supplierOrderSn"/>
        <result column="supplier_product_id" property="supplierProductId"/>
        <result column="product_id" property="productId"/>
        <result column="supplier_id" property="supplierId"/>
        <result column="supplier_name" property="supplierName"/>
        <result column="notify_type" property="notifyType"/>
        <result column="notify_desc" property="notifyDesc"/>
        <result column="notify_body" property="notifyBody"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `order_sn`,
        `supplier_order_sn`,
        `supplier_product_id`,
        `product_id`,
        `supplier_id`,
        `supplier_name`,
        `notify_type`,
        `notify_desc`,
        `notify_body`,
        `create_time`
    </sql>

    <insert id="saveVirtualThirdNotify"
            parameterType="com.juzifenqi.virtual.bean.pojo.VirtualThirdNotify"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO virtual_third_notify (
        `order_sn`,
        `supplier_order_sn`,
        `supplier_product_id`,
        `product_id`,
        `supplier_id`,
        `supplier_name`,
        `notify_type`,
        `notify_desc`,
        `notify_body`,
        `create_time`
        )
        VALUES(
        #{orderSn},
        #{supplierOrderSn},
        #{supplierProductId},
        #{productId},
        #{supplierId},
        #{supplierName},
        #{notifyType},
        #{notifyDesc},
        #{notifyBody},
        NOW()
        )
    </insert>
</mapper>
