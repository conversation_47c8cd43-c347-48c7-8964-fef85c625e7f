package com.juzifenqi.virtual.dao;

import com.juzifenqi.virtual.bean.pojo.VirtualGoods;
import com.juzifenqi.virtual.bean.pojo.VirtualOrdersCard;
import com.juzifenqi.virtual.bean.pojo.VirtualOrdersWorker;
import java.util.Date;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * 卡密
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/10 16:04
 */
public interface VirtualOrdersCardDao {

    /**
     * 新增
     */
    Integer insertVirtualOrdersCard(
            @Param("virtualOrdersCard") VirtualOrdersCard virtualOrdersCard);

    /**
     * 获取卡密
     */
    VirtualOrdersCard selectByOrderSn(String orderSn);

    /**
     * 查询所有状态为待获取卡密信息的任务
     */
    List<VirtualOrdersCard> getAllNeedDecodeCardsByTimeLimit(@Param("allowState") int allowState,
            @Param("start") int start, @Param("limit") Integer limit);

    /**
     * 批量更新状态
     */
    void batchUpdateCardsStateByIds(@Param("ids") List<Integer> ids, @Param("state") int state, @Param("allowState") int allowState);

    /**
     * 解析失败
     */
    Integer updateVirtualOrdersCardFailById(@Param("id") Integer id, @Param("state") int state);

    /**
     * 根据主键更新记录
     */
    Integer updateVirtualOrdersCardById(VirtualOrdersCard virtualOrdersCard);
}
