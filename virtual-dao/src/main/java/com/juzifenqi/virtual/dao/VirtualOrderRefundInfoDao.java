package com.juzifenqi.virtual.dao;

import com.juzifenqi.virtual.bean.pojo.VirtualOrderRefundInfo;
import org.apache.ibatis.annotations.Param;

/**
 * 权益订单退款信息
 *
 * <AUTHOR>
 * @date 2024/9/20 14:46
 */
public interface VirtualOrderRefundInfoDao {

    /**
     * 新增数据
     */
    Integer save(VirtualOrderRefundInfo refundInfo);

    /**
     * 根据业务退款流水号查询数据
     */
    VirtualOrderRefundInfo getByRefundSerialNo(String refundSerialNo);

    /**
     * 根据id修改
     */
    Integer updateById(VirtualOrderRefundInfo refundInfo);

    /**
     * 修改退款状态
     */
    Integer updateRefundState(@Param("refundSerialNo") String refundSerialNo,
            @Param("refundState") Integer refundState);

    /**
     * 修改支付退款流水号
     */
    Integer updatePaySerialNoById(@Param("id") Long id, @Param("paySerialNo") String paySerialNo);

}
