package com.juzifenqi.virtual.dao;

import com.juzifenqi.virtual.bean.pojo.VirtualOrderPayFlow;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * @description 商品订单支付请求流水
 * <AUTHOR>
 * @Date 2024/9/19
 */
public interface VirtualOrderPayFlowDao {

    /**
     * 新增
     **/
    int insert(VirtualOrderPayFlow virtualOrderPayFlow);

    /**
     * 根据商品订单和支付状态获取列表
     **/
    List<VirtualOrderPayFlow> listByOrderSnAndPayState(@Param("orderSn") String orderSn,
            @Param("payState") int payState);

    /**
     * 根据支付流水获取流水记录
     */
    VirtualOrderPayFlow getByApplySerialNo(@Param("applySerialNo") String applySerialNo,
            @Param("payState") Integer payState);

    /**
     * 更新
     **/
    int update(VirtualOrderPayFlow orderPayFlow);

}
