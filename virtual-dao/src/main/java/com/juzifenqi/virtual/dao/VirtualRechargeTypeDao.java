package com.juzifenqi.virtual.dao;

import com.juzifenqi.virtual.bean.pojo.VirtualRechargeType;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author:gaoyu
 * @Date: 2022-05-23 09:51:29
 * @Description: 充值类型信息表
 */

public interface VirtualRechargeTypeDao {

    /**
     * 新增返回ID
     */
    Integer saveVirtualRechargeType(VirtualRechargeType virtualRechargeType);

    /**
     * 删除
     */
    Integer deleteVirtualRechargeType(@Param("id") Integer id);

    /**
     * 更新
     */
    Integer updateVirtualRechargeType(
            @Param("virtualRechargeType") VirtualRechargeType virtualRechargeType);

    /**
     * Load查询
     */
    VirtualRechargeType loadVirtualRechargeType(@Param("id") Integer id);

    /**
     * 分页查询Data
     */
    List<VirtualRechargeType> getAllVirtualRechargeType();

}
