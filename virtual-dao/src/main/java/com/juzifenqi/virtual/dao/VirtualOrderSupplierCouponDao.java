package com.juzifenqi.virtual.dao;

import com.juzifenqi.virtual.bean.pojo.VirtualOrderSupplierCoupon;
import java.util.List;

/**
 * 三方厂商酒店订单表
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/30 16:26
 */
public interface VirtualOrderSupplierCouponDao {

    /**
     * 新增返回ID
     */
    Integer saveVirtualOrderSupplierCoupon(VirtualOrderSupplierCoupon virtualOrderSupplierCoupon);

    /**
     * 根据主键更新记录
     */
    Integer updateVirtualOrderSupplierCouponById(VirtualOrderSupplierCoupon virtualOrderSupplierCoupon);

    /**
     * 获取领券记录
     */
    VirtualOrderSupplierCoupon selectByOrderSn(String orderSn);

    /**
     * 获取已领取的领券记录
     */
    List<VirtualOrderSupplierCoupon> selectByOrderSns(List<String> orderSns);
}
