package com.juzifenqi.virtual.dao;

import com.juzifenqi.virtual.bean.pojo.VirtualOrdersWorker;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface VirtualOrdersWorkerDao {

    /**
     * 查询所有待下单的订单
     */
    List<VirtualOrdersWorker> getAllNeedPushOrders(@Param("start") int start,
            @Param("limit") Integer limit);

    /**
     * 查询所有充值状态待查证的订单
     */
    List<VirtualOrdersWorker> getAllNeedVerifyOrdersByTimeLimit(
            @Param("virifyTime") Date virifyTime, @Param("start") int start,
            @Param("limit") Integer limit);

    /**
     * 根据主键更新记录
     */
    Integer updateVirtualOrdersWorkerById(VirtualOrdersWorker virtualOrdersWorker);

    /**
     * 根据虚拟订单号更新记录
     */
    Integer updateVirtualOrdersByOrderSn(VirtualOrdersWorker virtualOrdersWorker);


    /**
     * 根据权益订单号查询订单
     */
    VirtualOrdersWorker getVirtualOrdersWorkerByOrderSn(@Param("orderSn") String orderSn);

    /**
     * 新增
     */
    Integer saveVirtualOrdersWorker(VirtualOrdersWorker virtualOrdersWorker);
}
