package com.juzifenqi.virtual.dao;

import com.juzifenqi.virtual.bean.bo.VirtualGoodsBo;
import com.juzifenqi.virtual.bean.pojo.VirtualGoods;
import com.juzifenqi.virtual.bean.vo.VirtualGoodsVo;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * virtual_goods表crud
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/20 9:38 下午
 */
public interface VirtualGoodsDao {

    /**
     * 根据id查询记录
     */
    VirtualGoods getVirtualGoodsById(Integer id);

    /**
     * 新增
     */
    Integer saveVirtualGoods(VirtualGoods virtualGoods);

    /**
     * 删除
     */
    Integer deleteVirtualGoodsById(Integer id);

    /**
     * 更新
     */
    Integer updateVirtualGoodsById(VirtualGoods virtualGoods);

    /**
     * 上架/下架
     */
    Integer updateOnsale(@Param("id") Integer id, @Param("onsaleState") Integer onsaleState,
            @Param("updateBy") String updateBy);

    /**
     * 批量查
     */
    List<VirtualGoods> getVirtualGoodsBySkus(@Param("skus") List<String> skus);

    /**
     * 单个查
     */
    VirtualGoods getVirtualGoodsBySku(@Param("sku") String sku);

    int pageListCount(@Param("virtualGoodsBo") VirtualGoodsBo virtualGoodsBo);

    List<VirtualGoodsVo> pageList(@Param("virtualGoodsBo") VirtualGoodsBo virtualGoodsBo,
            @Param("start") Integer start, @Param("size") Integer size);

    /**
     * 按三方商品id获取
     */
    List<VirtualGoods> getGoodsBySupplierItemId(Integer productId);

    /**
     * 获取所有已经上架商品
     */
    List<VirtualGoods> getOnlineProduct(Integer supplierId);
}
