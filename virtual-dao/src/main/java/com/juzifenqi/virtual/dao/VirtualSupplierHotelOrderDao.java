package com.juzifenqi.virtual.dao;


import com.juzifenqi.virtual.bean.pojo.VirtualSupplierHotelOrder;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * 三方厂商酒店订单表
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/30 16:26
 */
public interface VirtualSupplierHotelOrderDao {

    /**
     * 新增返回ID
     */
    Integer save(@Param("virtualSupplierHotelOrder") VirtualSupplierHotelOrder virtualSupplierHotelOrder);

    /**
     * 更新
     */
    Integer update(
            @Param("virtualSupplierHotelOrder") VirtualSupplierHotelOrder virtualSupplierHotelOrder);

    /**
     * 获取有效的酒店订单
     */
    List<VirtualSupplierHotelOrder> selectHotelOrderByOrderSn(String orderSn);

    /**
     * 获取有效的酒店订单列表
     */
    List<VirtualSupplierHotelOrder> selectHotelOrderByOrderSns(List<String> orderSns);

    /**
     * 通过虚拟权益订单id和优惠券id 查询有效的酒店订单
     */
    VirtualSupplierHotelOrder selectValidHotelOrderByOrderSnAndCouponId(@Param("orderSn") String orderSn, @Param("couponId") String couponId,
            @Param("notValidStatuses") List<Integer> notValidStatuses);

    /**
     * 获取有效的酒店订单列表
     */
    List<VirtualSupplierHotelOrder> selectHotelOrderByCouponIds(List<String> couponIds);

    /**
     * 通过虚拟权益订单id和酒店订单号 查询酒店订单
     */
    VirtualSupplierHotelOrder selectHotelOrderBySn(@Param("orderSn") String orderSn, @Param("hotelOrderSn") String hotelOrderSn);
}
